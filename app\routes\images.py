from flask import Blueprint, Response, abort
from flask_login import login_required, current_user
from app import db
from app.models.user import User
from app.models.client import Client
from app.utils.decorators import admin_required

images_bp = Blueprint('images', __name__)

@images_bp.route('/user/<int:user_id>/profile_image')
@login_required
def user_profile_image(user_id):
    """
    عرض صورة الملف الشخصي للمستخدم من قاعدة البيانات
    
    Args:
        user_id: معرف المستخدم
        
    Returns:
        Response: صورة الملف الشخصي
    """
    user = User.query.get_or_404(user_id)
    
    # التحقق من الصلاحيات - فقط المستخدم نفسه أو المسؤول يمكنه عرض الصورة
    if not (current_user.id == user_id or current_user.has_role('admin') or current_user.has_role('manager')):
        abort(403)
    
    # التحقق من وجود صورة في قاعدة البيانات
    image_data = user.get_profile_image_data()
    if not image_data:
        # إذا لم تكن هناك صورة في قاعدة البيانات، استخدم الصورة الافتراضية
        abort(404)
    
    # إرجاع الصورة
    return Response(image_data[0], mimetype=image_data[1])

@images_bp.route('/client/<int:client_id>/profile_image')
@login_required
def client_profile_image(client_id):
    """
    عرض صورة الملف الشخصي للعميل من قاعدة البيانات
    
    Args:
        client_id: معرف العميل
        
    Returns:
        Response: صورة الملف الشخصي
    """
    client = Client.query.get_or_404(client_id)
    
    # التحقق من الصلاحيات - فقط المسؤول أو المدير يمكنه عرض صور العملاء
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        abort(403)
    
    # التحقق من وجود صورة في قاعدة البيانات
    image_data = client.get_profile_image_data()
    if not image_data:
        # إذا لم تكن هناك صورة في قاعدة البيانات، استخدم الصورة الافتراضية
        abort(404)
    
    # إرجاع الصورة
    return Response(image_data[0], mimetype=image_data[1])
