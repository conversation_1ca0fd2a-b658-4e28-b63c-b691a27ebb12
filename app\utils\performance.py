import time
import functools
import logging
from flask import request, current_app, g
from sqlalchemy import event
from sqlalchemy.engine import Engine
import sqlite3

# Configure logger
logger = logging.getLogger('performance')

def setup_performance_monitoring(app):
    """
    Set up performance monitoring for the Flask application

    Args:
        app: The Flask application
    """
    # Enable query performance logging in debug mode
    if app.debug:
        @event.listens_for(Engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            conn.info.setdefault('query_start_time', []).append(time.time())
            logger.debug("Start Query: %s", statement)

        @event.listens_for(Engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total = time.time() - conn.info['query_start_time'].pop(-1)
            logger.debug("Query Complete: %s", statement)
            logger.debug("Query Time: %f", total)

            # Log slow queries (more than 100ms)
            if total > 0.1:
                logger.warning("Slow Query (%.3fs): %s", total, statement)

    # Add request timing middleware
    @app.before_request
    def before_request():
        g.start_time = time.time()

    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            elapsed = time.time() - g.start_time
            response.headers['X-Request-Time'] = str(elapsed)

            # Log slow requests (more than 500ms)
            if elapsed > 0.5:
                logger.warning("Slow Request (%.3fs): %s %s",
                              elapsed, request.method, request.path)
        return response

def optimize_sqlite_connection(app):
    """
    Optimize SQLite connection for better performance

    Args:
        app: The Flask application
    """
    # Enable SQLite WAL mode for better concurrency
    with app.app_context():
        try:
            from app import db
            from sqlalchemy import text
            db.session.execute(text('PRAGMA journal_mode=WAL'))
            db.session.execute(text('PRAGMA synchronous=NORMAL'))
            db.session.execute(text('PRAGMA cache_size=10000'))
            db.session.execute(text('PRAGMA temp_store=MEMORY'))
            db.session.execute(text('PRAGMA mmap_size=30000000000'))
            db.session.commit()
            print("SQLite optimizations applied")
        except Exception as e:
            print(f"Failed to apply SQLite optimizations: {str(e)}")

def cache_control(max_age=0, private=True, no_store=False, must_revalidate=True):
    """
    Decorator to set Cache-Control headers on a route

    Args:
        max_age: Maximum age in seconds
        private: Whether the response is private
        no_store: Whether to prevent storing the response
        must_revalidate: Whether the client must revalidate the response

    Returns:
        Decorated function
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)

            cache_parts = []
            if private:
                cache_parts.append('private')
            elif not private:
                cache_parts.append('public')

            if no_store:
                cache_parts.append('no-store')

            cache_parts.append(f'max-age={max_age}')

            if must_revalidate:
                cache_parts.append('must-revalidate')

            response.headers['Cache-Control'] = ', '.join(cache_parts)
            return response
        return decorated_function
    return decorator

def profile_function(f):
    """
    Decorator to profile a function's execution time

    Args:
        f: The function to profile

    Returns:
        Decorated function
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        elapsed = time.time() - start_time

        # Log function execution time
        logger.debug("Function %s executed in %.3fs", f.__name__, elapsed)

        # Log slow functions (more than 100ms)
        if elapsed > 0.1:
            logger.warning("Slow Function (%.3fs): %s", elapsed, f.__name__)

        return result
    return decorated_function

def optimize_query(query, paginate=False, page=1, per_page=25):
    """
    Optimize a SQLAlchemy query for better performance

    Args:
        query: The SQLAlchemy query
        paginate: Whether to paginate the query
        page: The page number
        per_page: The number of items per page

    Returns:
        Optimized query
    """
    # Add query options to optimize loading
    from sqlalchemy.orm import joinedload, contains_eager

    # Detect relationships in the query and optimize loading
    if hasattr(query, '_join_entities') and query._join_entities:
        for entity in query._join_entities:
            if hasattr(entity, 'entity'):
                # Add contains_eager for each joined entity
                query = query.options(contains_eager(entity.entity))

    # Apply pagination if requested
    if paginate:
        return query.paginate(page=page, per_page=per_page, error_out=False)

    return query

def get_database_stats():
    """
    Get database statistics for monitoring

    Returns:
        Dictionary of database statistics
    """
    try:
        from app import db

        # Get table sizes
        table_stats = {}
        tables = db.engine.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()

        for table in tables:
            table_name = table[0]
            if table_name.startswith('sqlite_'):
                continue

            count = db.engine.execute(f"SELECT COUNT(*) FROM {table_name}").scalar()
            table_stats[table_name] = count

        # Get database file size
        import os
        db_path = db.engine.url.database
        if db_path and os.path.exists(db_path):
            db_size = os.path.getsize(db_path) / (1024 * 1024)  # Size in MB
        else:
            db_size = 0

        return {
            'tables': table_stats,
            'db_size_mb': db_size,
            'connection_pool': {
                'size': db.engine.pool.size(),
                'checkedin': db.engine.pool.checkedin(),
                'overflow': db.engine.pool.overflow()
            }
        }
    except Exception as e:
        logger.error("Failed to get database stats: %s", str(e))
        return {'error': str(e)}
