{% extends 'base.html' %}

{% block title %}خطأ في استعادة النسخة الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">خطأ في استعادة النسخة الاحتياطية</h1>
        <a href="{{ url_for('restore.restore_page') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى صفحة الاستعادة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">فشلت عملية استعادة النسخة الاحتياطية</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error_message }}
                    </div>

                    <h5 class="mt-4 mb-3">تفاصيل الخطأ:</h5>
                    <div class="bg-light p-3 rounded mb-4" style="max-height: 400px; overflow-y: auto; direction: ltr; text-align: left;">
                        <pre class="mb-0">{{ log_content }}</pre>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>يرجى التأكد من أن ملف النسخة الاحتياطية صالح ومحاولة الاستعادة مرة أخرى. إذا استمرت المشكلة، يرجى الاتصال بمسؤول النظام.
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('restore.restore_page') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>العودة إلى صفحة الاستعادة
                        </a>
                        <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                            <i class="fas fa-home me-1"></i>الذهاب إلى لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
