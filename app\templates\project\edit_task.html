{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل مهمة</h1>
    <div>
        <a href="{{ url_for('project.tasks', id=project.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للمهام
        </a>
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
            <i class="fas fa-project-diagram me-1"></i>عرض المشروع
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">تعديل مهمة "{{ task.title }}" في مشروع {{ project.name }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('project.edit_task', task_id=task.id) }}">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="title" class="form-label">عنوان المهمة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="{{ task.title }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المهمة</label>
                                <textarea class="form-control" id="description" name="description" rows="4">{{ task.description or '' }}</textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="start_date" class="form-label">تاريخ البدء</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ task.start_date.strftime('%Y-%m-%d') }}">
                            </div>
                            
                            <div class="mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else '' }}">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">معلومات إضافية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="pending" {% if task.status == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="in_progress" {% if task.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                    <option value="completed" {% if task.status == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if task.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" {% if task.priority == 'low' %}selected{% endif %}>منخفضة</option>
                                    <option value="medium" {% if task.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                                    <option value="high" {% if task.priority == 'high' %}selected{% endif %}>عالية</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="assignee_id" class="form-label">المسؤول عن المهمة</label>
                                <select class="form-select" id="assignee_id" name="assignee_id">
                                    <option value="">-- اختر المسؤول --</option>
                                    {% for member in members %}
                                    <option value="{{ member.id }}" {% if task.assignee_id == member.id %}selected{% endif %}>{{ member.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            {% if task.completed_at %}
                            <div class="mb-3">
                                <label class="form-label">تاريخ الإكمال</label>
                                <input type="text" class="form-control" value="{{ task.completed_at.strftime('%Y-%m-%d %H:%M') }}" readonly>
                                <small class="text-muted">سيتم تحديث هذا التاريخ تلقائيًا عند تغيير حالة المهمة</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('project.tasks', id=project.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
