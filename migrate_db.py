import os
import sqlite3
import sys

def find_db_files():
    """Find all SQLite database files in the current directory and subdirectories."""
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return db_files

def migrate_db(db_path):
    """Add new columns to the users table in the specified database."""
    print(f"Attempting to migrate database: {db_path}")

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the users table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    if not cursor.fetchone():
        print(f"No 'users' table found in {db_path}")
        conn.close()
        return False

    # Get current columns in the users table
    cursor.execute('PRAGMA table_info(users)')
    columns = [column[1] for column in cursor.fetchall()]
    print(f"Current columns in users table: {columns}")

    # Add new columns if they don't exist
    new_columns = {
        'first_name_en': 'TEXT',
        'last_name_en': 'TEXT',
        'birth_date': 'DATE',
        'nationality': 'TEXT',
        'bank_account': 'TEXT',
        'cv': 'TEXT'
    }

    for column_name, column_type in new_columns.items():
        if column_name not in columns:
            try:
                cursor.execute(f'ALTER TABLE users ADD COLUMN {column_name} {column_type}')
                print(f"Added column {column_name} to users table")
            except sqlite3.OperationalError as e:
                print(f"Error adding column {column_name}: {e}")

    # Create id_documents table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='id_documents'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE id_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_type TEXT NOT NULL,
                document_number TEXT NOT NULL,
                issue_date DATE,
                expiry_date DATE,
                issuing_country TEXT,
                document_file TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            print("Created id_documents table")
        except sqlite3.OperationalError as e:
            print(f"Error creating id_documents table: {e}")
    else:
        print("id_documents table already exists")

    # Commit changes and close connection
    conn.commit()
    conn.close()
    return True

if __name__ == '__main__':
    db_files = find_db_files()

    if not db_files:
        print("No database files found")
        sys.exit(1)

    print(f"Found {len(db_files)} database files: {db_files}")

    success = False
    for db_file in db_files:
        if migrate_db(db_file):
            success = True

    if success:
        print("Migration completed successfully for at least one database")
        sys.exit(0)
    else:
        print("Migration failed for all databases")
        sys.exit(1)
