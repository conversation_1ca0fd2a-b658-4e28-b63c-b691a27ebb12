# No need to import app models for this migration
import sqlite3
import os

def upgrade():
    # Connect to the database
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()

    # Add new columns to users table
    try:
        cursor.execute('ALTER TABLE users ADD COLUMN first_name_en TEXT')
        cursor.execute('ALTER TABLE users ADD COLUMN last_name_en TEXT')
        cursor.execute('ALTER TABLE users ADD COLUMN birth_date DATE')
        cursor.execute('ALTER TABLE users ADD COLUMN nationality TEXT')
        cursor.execute('ALTER TABLE users ADD COLUMN bank_account TEXT')
        cursor.execute('ALTER TABLE users ADD COLUMN cv TEXT')
        conn.commit()
        print("Added new columns to users table")
    except sqlite3.OperationalError as e:
        print(f"Error adding columns to users table: {e}")

    # Create id_documents table
    try:
        cursor.execute('''
        CREATE TABLE id_documents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            document_type TEXT NOT NULL,
            document_number TEXT NOT NULL,
            issue_date DATE,
            expiry_date DATE,
            issuing_country TEXT,
            document_file TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        conn.commit()
        print("Created id_documents table")
    except sqlite3.OperationalError as e:
        print(f"Error creating id_documents table: {e}")

    conn.close()

if __name__ == '__main__':
    upgrade()
