{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل ملخص الاجتماع</h1>
        <div>
            <a href="{{ url_for('meeting.view', id=summary.meeting_id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى الاجتماع
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تعديل الملخص</h6>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('meeting.edit_summary', summary_id=summary.id) }}" method="POST">
                        <div class="mb-3">
                            <label for="summary_content" class="form-label">محتوى الملخص</label>
                            <textarea class="form-control" id="summary_content" name="summary_content" rows="10" required>{{ summary.content }}</textarea>
                        </div>
                        <div class="mb-3">
                            <p class="text-muted">
                                <small>تم الإنشاء بواسطة: {{ summary.created_by.get_full_name() }} - {{ summary.created_at.strftime('%Y-%m-%d %I:%M %p') }}</small>
                                {% if summary.updated_at and summary.updated_at != summary.created_at %}
                                <br>
                                <small>آخر تحديث: {{ summary.updated_at.strftime('%Y-%m-%d %I:%M %p') }}</small>
                                {% endif %}
                            </p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                            <a href="{{ url_for('meeting.view', id=summary.meeting_id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
