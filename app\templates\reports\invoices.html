{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الفواتير</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export me-1"></i>تصدير التقرير
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">فلترة الفواتير</h6>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
            </button>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form id="filterForm" action="{{ url_for('report.invoices') }}" method="GET">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if status == 'all' %}selected{% endif %}>الكل</option>
                                <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                                <option value="pending" {% if status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوعة</option>
                                <option value="overdue" {% if status == 'overdue' %}selected{% endif %}>متأخرة</option>
                                <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغاة</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="client_id" class="form-label">العميل</label>
                            <select class="form-select" id="client_id" name="client_id">
                                <option value="all" {% if client_id == 'all' %}selected{% endif %}>الكل</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}" {% if client_id|string == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="project_id" class="form-label">المشروع</label>
                            <select class="form-select" id="project_id" name="project_id">
                                <option value="all" {% if project_id == 'all' %}selected{% endif %}>الكل</option>
                                {% for project in projects %}
                                <option value="{{ project.id }}" {% if project_id|string == project.id|string %}selected{% endif %}>{{ project.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                        </div>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>تطبيق الفلتر
                        </button>
                        <a href="{{ url_for('report.invoices') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">الفواتير</h6>
        </div>
        <div class="card-body">
            {% if invoices %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المشروع</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ invoice.invoice_number }}</td>
                            <td>{{ invoice.client.name if invoice.client else 'غير محدد' }}</td>
                            <td>{{ invoice.project.name if invoice.project else 'غير محدد' }}</td>
                            <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد' }}</td>
                            <td>${{ "%.2f"|format(invoice.calculate_total()) }}</td>
                            <td>
                                {% if invoice.status == 'draft' %}
                                <span class="badge bg-secondary">مسودة</span>
                                {% elif invoice.status == 'pending' %}
                                <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                {% elif invoice.status == 'paid' %}
                                <span class="badge bg-success">مدفوعة</span>
                                {% elif invoice.status == 'overdue' %}
                                <span class="badge bg-danger">متأخرة</span>
                                {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-dark">ملغاة</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد فواتير لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('report.export', report_type='invoices') }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">تصدير تقرير الفواتير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="export_format" class="form-label">صيغة التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>

                    <!-- Hidden fields for filters -->
                    <input type="hidden" id="hidden_status" name="status" value="{{ status }}">
                    <input type="hidden" id="hidden_client_id" name="client_id" value="{{ client_id }}">
                    <input type="hidden" id="hidden_project_id" name="project_id" value="{{ project_id }}">
                    <input type="hidden" id="hidden_start_date" name="start_date" value="{{ start_date }}">
                    <input type="hidden" id="hidden_end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تصدير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[4, "desc"]]
        });

        // Update hidden fields when form is submitted
        $('#filterForm').on('submit', function() {
            $('#hidden_status').val($('#status').val());
            $('#hidden_client_id').val($('#client_id').val());
            $('#hidden_project_id').val($('#project_id').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });

        // Update hidden fields when export button is clicked
        $('#exportModal').on('show.bs.modal', function() {
            $('#hidden_status').val($('#status').val());
            $('#hidden_client_id').val($('#client_id').val());
            $('#hidden_project_id').val($('#project_id').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });
    });
</script>
{% endblock %}
