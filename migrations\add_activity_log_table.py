"""
Migration script to add the activity_logs table to the database.
"""

import sqlite3
import os

def run_migration():
    """
    Run the migration to add the activity_logs table.
    """
    # Get the database path
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app', 'sparkle.db')

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the table already exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='activity_logs'")
    if cursor.fetchone():
        print("Table 'activity_logs' already exists. Skipping migration.")
        conn.close()
        return

    # Create the activity_logs table
    cursor.execute('''
    CREATE TABLE activity_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        entity_type TEXT NOT NULL,
        entity_id INTEGER,
        description TEXT NOT NULL,
        details TEXT,
        ip_address TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (user_id) REFERENCES users (id)
    )
    ''')

    # Create an index on timestamp for faster queries
    cursor.execute('CREATE INDEX idx_activity_logs_timestamp ON activity_logs (timestamp)')

    # Create an index on user_id for faster queries
    cursor.execute('CREATE INDEX idx_activity_logs_user_id ON activity_logs (user_id)')

    # Create an index on action for faster queries
    cursor.execute('CREATE INDEX idx_activity_logs_action ON activity_logs (action)')

    # Create an index on entity_type for faster queries
    cursor.execute('CREATE INDEX idx_activity_logs_entity_type ON activity_logs (entity_type)')

    # Commit the changes
    conn.commit()
    conn.close()

    print("Migration completed: Added 'activity_logs' table.")

if __name__ == '__main__':
    run_migration()
