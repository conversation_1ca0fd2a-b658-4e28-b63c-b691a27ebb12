{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-calculator"></i> لوحة التحكم المحاسبية المتكاملة
            </h1>
            <p class="mb-0 text-muted">نظرة شاملة على الوضع المالي والمحاسبي للشركة</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <a href="{{ url_for('accounting.receive_payment') }}" class="btn btn-success">
                    <i class="fas fa-money-bill-wave"></i> تسجيل دفعة
                </a>
                <a href="{{ url_for('accounting.reports') }}" class="btn btn-info">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a>
                <a href="{{ url_for('accounting.chart_of_accounts') }}" class="btn btn-secondary">
                    <i class="fas fa-list"></i> شجرة الحسابات
                </a>
            </div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        <!-- الإيرادات الشهرية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الإيرادات هذا الشهر</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(monthly_revenue) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المصروفات الشهرية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                المصروفات هذا الشهر</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(monthly_expenses) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- صافي الربح -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                صافي الربح</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800 {{ 'text-success' if net_profit >= 0 else 'text-danger' }}">
                                ${{ "{:,.2f}".format(net_profit) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرصيد النقدي -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الرصيد النقدي</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(cash_balance) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-university fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row KPIs -->
    <div class="row mb-4">
        <!-- حسابات العملاء -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                حسابات العملاء</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(ar_balance) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- حسابات الموردين -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                حسابات الموردين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(ap_balance) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفواتير المعلقة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الفواتير المعلقة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ pending_invoices }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المبالغ المستحقة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                المبالغ المستحقة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(outstanding_receivables) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Monthly Performance Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الأداء المالي الشهري</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">خيارات التقرير:</div>
                            <a class="dropdown-item" href="{{ url_for('accounting.profit_loss_report') }}">قائمة الدخل</a>
                            <a class="dropdown-item" href="{{ url_for('accounting.balance_sheet_report') }}">الميزانية العمومية</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="monthlyPerformanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Account Balances Pie Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع الأصول</h6>
                </div>
                <div class="card-body">
                    <canvas id="assetsDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('finance.create_invoice') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-file-invoice"></i><br>إنشاء فاتورة
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('accounting.receive_payment') }}" class="btn btn-success btn-block">
                                <i class="fas fa-money-bill-wave"></i><br>تسجيل دفعة
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('accounting.journal_entries') }}" class="btn btn-info btn-block">
                                <i class="fas fa-book"></i><br>قيود اليومية
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('accounting.chart_of_accounts') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-list"></i><br>شجرة الحسابات
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('accounting.trial_balance_report') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-balance-scale"></i><br>ميزان المراجعة
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ url_for('accounting.reports') }}" class="btn btn-dark btn-block">
                                <i class="fas fa-chart-bar"></i><br>جميع التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    {% if overdue_invoices > 0 %}
    <div class="row">
        <div class="col-lg-12">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تنبيه!</strong> لديك {{ overdue_invoices }} فاتورة متأخرة الدفع.
                <a href="{{ url_for('finance.invoices') }}?status=overdue" class="alert-link">عرض الفواتير المتأخرة</a>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Performance Chart
    const ctx1 = document.getElementById('monthlyPerformanceChart').getContext('2d');
    new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات',
                data: [{{ monthly_revenue }}, 35000, 42000, 38000, 45000, 40000],
                borderColor: 'rgb(28, 200, 138)',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.1,
                fill: true
            }, {
                label: 'المصروفات',
                data: [{{ monthly_expenses }}, 15000, 18000, 16000, 20000, 17000],
                borderColor: 'rgb(231, 74, 59)',
                backgroundColor: 'rgba(231, 74, 59, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الأداء المالي الشهري'
                },
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Assets Distribution Chart
    const ctx2 = document.getElementById('assetsDistributionChart').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['النقد في البنك', 'حسابات العملاء', 'أصول أخرى'],
            datasets: [{
                data: [{{ cash_balance }}, {{ ar_balance }}, 25000],
                backgroundColor: [
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 99, 132)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': $' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}
