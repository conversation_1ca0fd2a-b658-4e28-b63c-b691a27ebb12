{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف التحديث</h1>
    <a href="{{ url_for('project.updates', id=update.project_id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للتحديثات
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                
                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف التحديث التالي؟</h4>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ update.title }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-muted small mb-2">
                            <i class="fas fa-user me-1"></i>{{ update.created_by.get_full_name() }} | 
                            <i class="fas fa-calendar me-1"></i>{{ update.created_at.strftime('%Y-%m-%d %H:%M') }}
                            {% if update.is_pinned %}
                            | <i class="fas fa-thumbtack me-1"></i>مثبت
                            {% endif %}
                        </div>
                        <div class="update-content">
                            {{ update.content|safe }}
                        </div>
                    </div>
                </div>
                
                <form action="{{ url_for('project.delete_update', update_id=update.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('project.updates', id=update.project_id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف التحديث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .update-content {
        white-space: pre-line;
    }
</style>
{% endblock %}
