{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">العقوبات</h1>
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <a href="{{ url_for('penalty.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إنشاء عقوبة جديدة
        </a>
        {% endif %}
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة العقوبات</h6>
        </div>
        <div class="card-body">
            <!-- Search and Filter Form -->
            <form method="GET" action="{{ url_for('penalty.index') }}" class="mb-4">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" placeholder="ابحث باسم الموظف أو سبب العقوبة" value="{{ search_query }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            {% if search_query %}
                            <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="penalty_type" name="penalty_type" onchange="this.form.submit()">
                            <option value="all" {% if penalty_type == 'all' or not penalty_type %}selected{% endif %}>جميع أنواع العقوبات</option>
                            {% for type in penalty_types %}
                            <option value="{{ type.value }}" {% if penalty_type == type.value %}selected{% endif %}>{{ type.label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="all" {% if status == 'all' or not status %}selected{% endif %}>جميع الحالات</option>
                            <option value="active" {% if status == 'active' %}selected{% endif %}>سارية</option>
                            <option value="expired" {% if status == 'expired' %}selected{% endif %}>منتهية</option>
                        </select>
                    </div>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <div class="col-md-2">
                        <select class="form-select" id="employee_id" name="employee_id" onchange="this.form.submit()">
                            <option value="" {% if not employee_id %}selected{% endif %}>جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if employee_id|string == employee.id|string %}selected{% endif %}>{{ employee.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div class="col-md-{% if current_user.has_role('admin') or current_user.has_role('manager') %}2{% else %}4{% endif %}">
                        <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                            <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 عقوبة</option>
                            <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 عقوبة</option>
                            <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 عقوبة</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">من تاريخ</span>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">إلى تاريخ</span>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
            {% if penalties.items %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>نوع العقوبة</th>
                            <th>تاريخ البدء</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>تم الإصدار بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for penalty in penalties.items %}
                        <tr>
                            <td>{{ penalty.user.get_full_name() }}</td>
                            <td>
                                {% if penalty.penalty_type == 'verbal_warning' %}
                                <span class="badge bg-warning text-dark">لفت نظر شفوي</span>
                                {% elif penalty.penalty_type == 'written_warning' %}
                                <span class="badge bg-warning">لفت نظر كتابي (تحذير أول)</span>
                                {% elif penalty.penalty_type == 'written_notice' %}
                                <span class="badge bg-warning">إنذار كتابي (تحذير ثاني)</span>
                                {% elif penalty.penalty_type == 'suspension' %}
                                <span class="badge bg-danger">إيقاف مؤقت / خصم من الراتب</span>
                                {% elif penalty.penalty_type == 'final_warning' %}
                                <span class="badge bg-danger">الإنذار النهائي</span>
                                {% elif penalty.penalty_type == 'termination' %}
                                <span class="badge bg-dark">الفصل من العمل / إنهاء التعاقد</span>
                                {% endif %}
                            </td>
                            <td>{{ penalty.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else 'غير محدد' }}</td>
                            <td>
                                {% if penalty.is_active() %}
                                <span class="badge bg-success">سارية</span>
                                {% else %}
                                <span class="badge bg-secondary">منتهية</span>
                                {% endif %}
                            </td>
                            <td>{{ penalty.issued_by.get_full_name() }}</td>
                            <td>
                                <a href="{{ url_for('penalty.view', id=penalty.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                <a href="{{ url_for('penalty.edit', id=penalty.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_role('admin') %}
                                <form action="{{ url_for('penalty.delete', id=penalty.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه العقوبة؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        عرض {{ penalties.items|length }} من {{ penalties.total }} عقوبة
                        {% if search_query %}
                        <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                        {% endif %}
                        {% if penalty_type and penalty_type != 'all' %}
                        <span class="text-muted">
                            (النوع:
                            {% for type in penalty_types %}
                                {% if type.value == penalty_type %}
                                    {{ type.label }}
                                {% endif %}
                            {% endfor %}
                            )
                        </span>
                        {% endif %}
                        {% if status and status != 'all' %}
                        <span class="text-muted">
                            (الحالة:
                            {% if status == 'active' %}سارية{% endif %}
                            {% if status == 'expired' %}منتهية{% endif %}
                            )
                        </span>
                        {% endif %}
                        {% if employee_id %}
                        <span class="text-muted">
                            (الموظف:
                            {% for employee in employees %}
                                {% if employee.id|string == employee_id|string %}
                                    {{ employee.get_full_name() }}
                                {% endif %}
                            {% endfor %}
                            )
                        </span>
                        {% endif %}
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            {% if penalties.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('penalty.index', page=penalties.prev_num, per_page=current_per_page, search=search_query, penalty_type=penalty_type, status=status, date_from=date_from, date_to=date_to, employee_id=employee_id) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% set start_page = penalties.page - 2 if penalties.page > 2 else 1 %}
                            {% set end_page = start_page + 4 if start_page + 4 <= penalties.pages else penalties.pages %}
                            {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            <li class="page-item {% if page_num == penalties.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('penalty.index', page=page_num, per_page=current_per_page, search=search_query, penalty_type=penalty_type, status=status, date_from=date_from, date_to=date_to, employee_id=employee_id) }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}

                            {% if penalties.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('penalty.index', page=penalties.next_num, per_page=current_per_page, search=search_query, penalty_type=penalty_type, status=status, date_from=date_from, date_to=date_to, employee_id=employee_id) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                {% if search_query or penalty_type != 'all' or status != 'all' or date_from or date_to or employee_id %}
                <p class="text-muted mb-0">لا توجد نتائج مطابقة للبحث</p>
                <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary mt-3">
                    <i class="fas fa-redo me-1"></i>عرض جميع العقوبات
                </a>
                {% else %}
                <p class="text-muted mb-0">لا توجد عقوبات حالياً</p>
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <a href="{{ url_for('penalty.create') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus me-1"></i>إنشاء عقوبة جديدة
                </a>
                {% endif %}
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
