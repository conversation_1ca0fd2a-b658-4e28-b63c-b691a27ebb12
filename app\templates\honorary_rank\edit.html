{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل الرتبة الشرفية</h1>
        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الرتبة الشرفية</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('honorary_rank.edit', id=rank.id) }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">اسم الرتبة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ rank.name }}" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="color" class="form-label">لون الرتبة</label>
                        <input type="color" class="form-control form-control-color w-100" id="color" name="color" value="{{ rank.color }}">
                        <div class="form-text">اختر لوناً للرتبة</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">وصف الرتبة</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ rank.description }}</textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="icon" class="form-label">أيقونة الرتبة</label>
                        <select class="form-select" id="icon" name="icon">
                            <option value="fa-award" {% if rank.icon == 'fa-award' %}selected{% endif %}>جائزة (Award)</option>
                            <option value="fa-medal" {% if rank.icon == 'fa-medal' %}selected{% endif %}>ميدالية (Medal)</option>
                            <option value="fa-trophy" {% if rank.icon == 'fa-trophy' %}selected{% endif %}>كأس (Trophy)</option>
                            <option value="fa-star" {% if rank.icon == 'fa-star' %}selected{% endif %}>نجمة (Star)</option>
                            <option value="fa-certificate" {% if rank.icon == 'fa-certificate' %}selected{% endif %}>شهادة (Certificate)</option>
                            <option value="fa-crown" {% if rank.icon == 'fa-crown' %}selected{% endif %}>تاج (Crown)</option>
                            <option value="fa-gem" {% if rank.icon == 'fa-gem' %}selected{% endif %}>جوهرة (Gem)</option>
                            <option value="fa-shield-alt" {% if rank.icon == 'fa-shield-alt' %}selected{% endif %}>درع (Shield)</option>
                            <option value="fa-bookmark" {% if rank.icon == 'fa-bookmark' %}selected{% endif %}>علامة مرجعية (Bookmark)</option>
                            <option value="fa-heart" {% if rank.icon == 'fa-heart' %}selected{% endif %}>قلب (Heart)</option>
                            <option value="fa-thumbs-up" {% if rank.icon == 'fa-thumbs-up' %}selected{% endif %}>إعجاب (Thumbs Up)</option>
                        </select>
                        <div class="form-text">اختر أيقونة للرتبة</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">معاينة الرتبة:</label>
                        <div class="p-3 border rounded">
                            <span id="preview-badge" class="badge" style="background-color: {{ rank.color }}; color: {{ '#000' if rank.color == '#ffffff' else '#fff' }}">
                                <i id="preview-icon" class="fas {{ rank.icon }} me-1"></i>
                                <span id="preview-name">{{ rank.name }}</span>
                            </span>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Update preview when inputs change
        function updatePreview() {
            const name = $('#name').val() || 'اسم الرتبة';
            const color = $('#color').val();
            const icon = $('#icon').val();
            
            $('#preview-name').text(name);
            $('#preview-badge').css('background-color', color);
            $('#preview-badge').css('color', isLightColor(color) ? '#000' : '#fff');
            $('#preview-icon').attr('class', 'fas ' + icon + ' me-1');
        }
        
        // Check if color is light (to determine text color)
        function isLightColor(color) {
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
            return brightness > 155;
        }
        
        // Update preview on input change
        $('#name, #color, #icon').on('input change', updatePreview);
    });
</script>
{% endblock %}
