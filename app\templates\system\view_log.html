{% extends 'base.html' %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ title }}</h5>
                    <a href="{{ url_for('system.config') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right ml-1"></i> العودة إلى الإعدادات
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        يعرض هذا السجل تفاصيل عملية {{ 'النسخ الاحتياطي' if log_type == 'backup' else 'استعادة النسخة الاحتياطية' }}، بما في ذلك الملفات التي تم نسخها واستعادتها.
                    </div>
                    
                    <div class="bg-light p-3 rounded" style="direction: ltr; text-align: left; max-height: 600px; overflow-y: auto; font-family: monospace; white-space: pre-wrap;">
{{ log_content }}
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('system.config') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right ml-1"></i> العودة
                        </a>
                        
                        {% if log_type == 'backup' %}
                        <a href="{{ url_for('system.backup') }}" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية جديدة
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
