{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الأقسام</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='departments') }}" method="POST">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="has_employees" class="form-label">عدد الموظفين</label>
                                <select class="form-select" id="has_employees" name="has_employees">
                                    <option value="all" selected>الكل</option>
                                    <option value="with_employees">أقسام بها موظفين</option>
                                    <option value="without_employees">أقسام بدون موظفين</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="has_head" class="form-label">رئيس القسم</label>
                                <select class="form-select" id="has_head" name="has_head">
                                    <option value="all" selected>الكل</option>
                                    <option value="with_head">أقسام لها رئيس</option>
                                    <option value="without_head">أقسام بدون رئيس</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم القسم</th>
                                    <th>الوصف</th>
                                    <th>عدد الموظفين</th>
                                    <th>رئيس القسم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for department in departments %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ department.name }}</td>
                                    <td>{{ department.description or 'لا يوجد وصف' }}</td>
                                    <td>{{ department.employees|length }}</td>
                                    <td>{{ department.head.get_full_name() if department.head else 'غير محدد' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#has_employees, #has_head, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
    });
</script>
{% endblock %}
