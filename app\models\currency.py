from app import db

class Currency(db.Model):
    __tablename__ = 'currencies'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(3), unique=True, nullable=False)  # ISO 4217 code (USD, EUR, etc.)
    name = db.Column(db.String(50), nullable=False)
    symbol = db.Column(db.String(5), nullable=False)
    exchange_rate = db.Column(db.Float, nullable=False, default=1.0)  # Exchange rate to USD

    def __repr__(self):
        return f'<Currency {self.code}: {self.name}>'
