# أمثلة على منطق الواجهة الخلفية للنظام المالي المتكامل
from flask import Blueprint, request, jsonify, render_template, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, date
from decimal import Decimal
from app import db
from enhanced_models import *

# إنشاء Blueprint للنظام المالي المحسن
enhanced_finance_bp = Blueprint('enhanced_finance', __name__, url_prefix='/finance/v2')

# ===== خدمات المحاسبة العامة =====

class AccountingService:
    """خدمة المحاسبة العامة - محرك إنشاء القيود التلقائية"""
    
    @staticmethod
    def create_invoice_journal_entry(invoice):
        """إنشاء قيد يومية عند إصدار فاتورة"""
        # البحث عن الحسابات المطلوبة
        ar_account = ChartOfAccounts.query.filter_by(account_code='1020').first()  # حسابات العملاء
        
        if not ar_account:
            raise ValueError("حساب العملاء غير موجود")
        
        # إنشاء القيد
        entry = JournalEntry(
            entry_number=f"INV-{invoice.invoice_number}",
            entry_date=invoice.issue_date,
            description=f"فاتورة رقم {invoice.invoice_number} للعميل {invoice.client.name}",
            source_document_type='invoice',
            source_document_id=invoice.id,
            created_by_id=current_user.id
        )
        
        # بند المدين: حسابات العملاء
        debit_item = JournalItem(
            account_id=ar_account.id,
            debit=invoice.total_amount,
            credit=0,
            description=f"فاتورة للعميل {invoice.client.name}"
        )
        entry.items.append(debit_item)
        
        # بنود الدائن: حسابات الإيرادات حسب نوع الخدمة
        for item in invoice.items:
            revenue_account = AccountingService.get_revenue_account_for_service(item.description)
            
            credit_item = JournalItem(
                account_id=revenue_account.id,
                debit=0,
                credit=item.subtotal,
                description=item.description
            )
            entry.items.append(credit_item)
        
        db.session.add(entry)
        db.session.flush()
        
        # ربط الفاتورة بالقيد
        invoice.journal_entry_id = entry.id
        
        # ترحيل القيد
        entry.post()
        
        return entry
    
    @staticmethod
    def get_revenue_account_for_service(service_description):
        """تحديد حساب الإيرادات المناسب حسب نوع الخدمة"""
        service_lower = service_description.lower()
        
        if 'تصميم' in service_lower or 'design' in service_lower:
            return ChartOfAccounts.query.filter_by(account_code='4010').first()
        elif 'برمجة' in service_lower or 'programming' in service_lower:
            return ChartOfAccounts.query.filter_by(account_code='4020').first()
        elif 'مونتاج' in service_lower or 'editing' in service_lower:
            return ChartOfAccounts.query.filter_by(account_code='4030').first()
        elif 'إدارة' in service_lower or 'management' in service_lower:
            return ChartOfAccounts.query.filter_by(account_code='4040').first()
        else:
            # حساب إيرادات عام
            return ChartOfAccounts.query.filter_by(account_code='4010').first()

# ===== API Endpoints =====

@enhanced_finance_bp.route('/api/invoices', methods=['POST'])
@login_required
def create_invoice_api():
    """إنشاء فاتورة جديدة مع القيد المحاسبي التلقائي"""
    try:
        data = request.get_json()
        
        # إنشاء الفاتورة
        invoice = Invoice(
            client_id=data['client_id'],
            project_id=data.get('project_id'),
            invoice_number=data['invoice_number'],
            issue_date=datetime.strptime(data['issue_date'], '%Y-%m-%d').date(),
            due_date=datetime.strptime(data['due_date'], '%Y-%m-%d').date(),
            created_by_id=current_user.id
        )
        
        # إضافة بنود الفاتورة
        for item_data in data['items']:
            item = InvoiceItem(
                description=item_data['description'],
                quantity=Decimal(str(item_data['quantity'])),
                unit_price=Decimal(str(item_data['unit_price']))
            )
            item.calculate_subtotal()
            invoice.items.append(item)
        
        # حساب إجماليات الفاتورة
        invoice.calculate_totals()
        invoice.amount_due = invoice.total_amount
        
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID
        
        # إنشاء القيد المحاسبي التلقائي
        journal_entry = AccountingService.create_invoice_journal_entry(invoice)
        
        # تحديث حالة الفاتورة
        invoice.status = 'sent'
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الفاتورة والقيد المحاسبي بنجاح',
            'invoice_id': invoice.id,
            'journal_entry_id': journal_entry.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء الفاتورة: {str(e)}'
        }), 400

@enhanced_finance_bp.route('/api/payments/receive', methods=['POST'])
@login_required
def receive_payment_api():
    """تسجيل دفعة مستلمة مع القيد المحاسبي التلقائي"""
    try:
        data = request.get_json()
        
        # إنشاء الدفعة
        payment = PaymentReceived(
            customer_id=data['customer_id'],
            invoice_id=data.get('invoice_id'),
            payment_number=data['payment_number'],
            payment_date=datetime.strptime(data['payment_date'], '%Y-%m-%d').date(),
            amount=Decimal(str(data['amount'])),
            payment_method=data['payment_method'],
            reference_number=data.get('reference_number'),
            notes=data.get('notes'),
            created_by_id=current_user.id
        )
        
        db.session.add(payment)
        db.session.flush()
        
        # إنشاء القيد المحاسبي التلقائي
        payment.create_journal_entry()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تسجيل الدفعة والقيد المحاسبي بنجاح',
            'payment_id': payment.id,
            'journal_entry_id': payment.journal_entry_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في تسجيل الدفعة: {str(e)}'
        }), 400

@enhanced_finance_bp.route('/api/vendor-bills', methods=['POST'])
@login_required
def create_vendor_bill_api():
    """إنشاء فاتورة مورد مع القيد المحاسبي التلقائي"""
    try:
        data = request.get_json()
        
        # إنشاء فاتورة المورد
        bill = VendorBill(
            vendor_id=data['vendor_id'],
            bill_number=data['bill_number'],
            vendor_reference=data.get('vendor_reference'),
            issue_date=datetime.strptime(data['issue_date'], '%Y-%m-%d').date(),
            due_date=datetime.strptime(data['due_date'], '%Y-%m-%d').date(),
            tax_amount=Decimal(str(data.get('tax_amount', 0))),
            created_by_id=current_user.id
        )
        
        # إضافة بنود الفاتورة
        for item_data in data['items']:
            item = VendorBillItem(
                description=item_data['description'],
                quantity=Decimal(str(item_data['quantity'])),
                unit_price=Decimal(str(item_data['unit_price'])),
                account_id=item_data['account_id']  # حساب المصروف
            )
            item.calculate_subtotal()
            bill.items.append(item)
        
        # حساب إجماليات الفاتورة
        bill.calculate_totals()
        
        db.session.add(bill)
        db.session.flush()
        
        # إنشاء القيد المحاسبي التلقائي
        bill.create_journal_entry()
        
        # تحديث حالة الفاتورة
        bill.status = 'to_pay'
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء فاتورة المورد والقيد المحاسبي بنجاح',
            'bill_id': bill.id,
            'journal_entry_id': bill.journal_entry_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء فاتورة المورد: {str(e)}'
        }), 400

# ===== خدمات التقارير المالية =====

class ReportingService:
    """خدمة التقارير المالية"""
    
    @staticmethod
    def get_profit_loss_report(start_date, end_date):
        """تقرير قائمة الدخل (الأرباح والخسائر)"""
        # الإيرادات
        revenue_accounts = ChartOfAccounts.query.filter_by(account_type='Revenue').all()
        total_revenue = sum(account.balance for account in revenue_accounts)
        
        # المصروفات
        expense_accounts = ChartOfAccounts.query.filter_by(account_type='Expense').all()
        total_expenses = sum(account.balance for account in expense_accounts)
        
        # صافي الربح
        net_profit = total_revenue - total_expenses
        
        return {
            'period': f"{start_date} إلى {end_date}",
            'revenue_accounts': [
                {
                    'account_code': acc.account_code,
                    'account_name': acc.account_name,
                    'balance': float(acc.balance)
                } for acc in revenue_accounts
            ],
            'total_revenue': float(total_revenue),
            'expense_accounts': [
                {
                    'account_code': acc.account_code,
                    'account_name': acc.account_name,
                    'balance': float(acc.balance)
                } for acc in expense_accounts
            ],
            'total_expenses': float(total_expenses),
            'net_profit': float(net_profit)
        }
    
    @staticmethod
    def get_balance_sheet_report(as_of_date):
        """تقرير الميزانية العمومية"""
        # الأصول
        asset_accounts = ChartOfAccounts.query.filter_by(account_type='Asset').all()
        total_assets = sum(account.balance for account in asset_accounts)
        
        # الخصوم
        liability_accounts = ChartOfAccounts.query.filter_by(account_type='Liability').all()
        total_liabilities = sum(account.balance for account in liability_accounts)
        
        # حقوق الملكية
        equity_accounts = ChartOfAccounts.query.filter_by(account_type='Equity').all()
        total_equity = sum(account.balance for account in equity_accounts)
        
        return {
            'as_of_date': str(as_of_date),
            'assets': [
                {
                    'account_code': acc.account_code,
                    'account_name': acc.account_name,
                    'balance': float(acc.balance)
                } for acc in asset_accounts
            ],
            'total_assets': float(total_assets),
            'liabilities': [
                {
                    'account_code': acc.account_code,
                    'account_name': acc.account_name,
                    'balance': float(acc.balance)
                } for acc in liability_accounts
            ],
            'total_liabilities': float(total_liabilities),
            'equity': [
                {
                    'account_code': acc.account_code,
                    'account_name': acc.account_name,
                    'balance': float(acc.balance)
                } for acc in equity_accounts
            ],
            'total_equity': float(total_equity),
            'balance_check': float(total_assets) == float(total_liabilities + total_equity)
        }

@enhanced_finance_bp.route('/api/reports/profit-loss')
@login_required
def profit_loss_report_api():
    """API لتقرير قائمة الدخل"""
    start_date = request.args.get('start_date', date.today().replace(day=1))
    end_date = request.args.get('end_date', date.today())
    
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    report = ReportingService.get_profit_loss_report(start_date, end_date)
    return jsonify(report)

@enhanced_finance_bp.route('/api/reports/balance-sheet')
@login_required
def balance_sheet_report_api():
    """API لتقرير الميزانية العمومية"""
    as_of_date = request.args.get('as_of_date', date.today())
    
    if isinstance(as_of_date, str):
        as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
    
    report = ReportingService.get_balance_sheet_report(as_of_date)
    return jsonify(report)

# ===== لوحة التحكم المالية المحسنة =====

@enhanced_finance_bp.route('/dashboard')
@login_required
def enhanced_dashboard():
    """لوحة التحكم المالية المحسنة"""
    # مؤشرات الأداء الرئيسية
    current_month_start = date.today().replace(day=1)
    
    # إجمالي الإيرادات هذا الشهر
    revenue_accounts = ChartOfAccounts.query.filter_by(account_type='Revenue').all()
    monthly_revenue = sum(account.balance for account in revenue_accounts)
    
    # إجمالي المصروفات هذا الشهر
    expense_accounts = ChartOfAccounts.query.filter_by(account_type='Expense').all()
    monthly_expenses = sum(account.balance for account in expense_accounts)
    
    # صافي الربح
    net_profit = monthly_revenue - monthly_expenses
    
    # المبالغ المستحقة لك (من العملاء)
    outstanding_receivables = sum(
        invoice.amount_due for invoice in Invoice.query.filter_by(status='sent').all()
    )
    
    # المبالغ المستحقة عليك (للموردين)
    outstanding_payables = sum(
        bill.amount_due for bill in VendorBill.query.filter_by(status='to_pay').all()
    )
    
    # الرصيد النقدي
    cash_account = ChartOfAccounts.query.filter_by(account_code='1010').first()
    cash_balance = cash_account.balance if cash_account else 0
    
    return render_template('finance/enhanced_dashboard.html',
                         monthly_revenue=monthly_revenue,
                         monthly_expenses=monthly_expenses,
                         net_profit=net_profit,
                         outstanding_receivables=outstanding_receivables,
                         outstanding_payables=outstanding_payables,
                         cash_balance=cash_balance)
