<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="gold-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EABF54;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4A93E;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1765A0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1794BF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="white" stroke="#303030" stroke-width="2" />
  
  <!-- Sparkle Star -->
  <g transform="translate(100, 100) scale(0.8)">
    <!-- Main Star -->
    <path d="M0,-80 L20,-20 L80,0 L20,20 L0,80 L-20,20 L-80,0 L-20,-20 Z" fill="url(#gold-gradient)" />
    
    <!-- Small Stars -->
    <g transform="rotate(45)">
      <path d="M0,-100 L10,-30 L40,0 L10,30 L0,100 L-10,30 L-40,0 L-10,-30 Z" fill="url(#blue-gradient)" opacity="0.7" />
    </g>
    
    <!-- Sparkles -->
    <g>
      <circle cx="60" cy="-60" r="5" fill="url(#gold-gradient)" />
      <circle cx="-60" cy="60" r="5" fill="url(#gold-gradient)" />
      <circle cx="-60" cy="-60" r="5" fill="url(#gold-gradient)" />
      <circle cx="60" cy="60" r="5" fill="url(#gold-gradient)" />
      
      <circle cx="80" cy="-30" r="3" fill="url(#blue-gradient)" />
      <circle cx="-80" cy="30" r="3" fill="url(#blue-gradient)" />
      <circle cx="-30" cy="-80" r="3" fill="url(#blue-gradient)" />
      <circle cx="30" cy="80" r="3" fill="url(#blue-gradient)" />
    </g>
  </g>
  
  <!-- Text "Sparkle" -->
  <text x="100" y="160" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#303030">SPARKLE MEDIA</text>
</svg>
