{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعيين رتبة شرفية لموظف</h1>
        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل التعيين</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('honorary_rank.assign') }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="" selected disabled>اختر الموظف...</option>
                            {% for user in users %}
                            <option value="{{ user.id }}">{{ user.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر الموظف الذي تريد منحه الرتبة الشرفية</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="rank_id" class="form-label">الرتبة الشرفية <span class="text-danger">*</span></label>
                        <select class="form-select" id="rank_id" name="rank_id" required>
                            <option value="" selected disabled>اختر الرتبة...</option>
                            {% for rank in ranks %}
                            <option value="{{ rank.id }}" data-color="{{ rank.color }}" data-icon="{{ rank.icon }}">{{ rank.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر الرتبة الشرفية التي تريد منحها للموظف</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">معاينة:</label>
                        <div id="preview-container" class="p-3 border rounded d-none">
                            <p class="mb-1">سيتم منح الموظف:</p>
                            <div class="d-flex align-items-center">
                                <span id="preview-user" class="fw-bold me-2"></span>
                                <span id="preview-badge" class="badge">
                                    <i id="preview-icon" class="fas me-1"></i>
                                    <span id="preview-rank"></span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>تعيين الرتبة
                        </button>
                        <a href="{{ url_for('honorary_rank.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#user_id, #rank_id').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
        
        // Update preview when selections change
        function updatePreview() {
            const userId = $('#user_id').val();
            const rankId = $('#rank_id').val();
            
            if (userId && rankId) {
                const userName = $('#user_id option:selected').text();
                const rankName = $('#rank_id option:selected').text();
                const rankColor = $('#rank_id option:selected').data('color');
                const rankIcon = $('#rank_id option:selected').data('icon');
                
                $('#preview-user').text(userName);
                $('#preview-rank').text(rankName);
                $('#preview-badge').css('background-color', rankColor);
                $('#preview-badge').css('color', isLightColor(rankColor) ? '#000' : '#fff');
                $('#preview-icon').attr('class', 'fas ' + rankIcon + ' me-1');
                
                $('#preview-container').removeClass('d-none');
            } else {
                $('#preview-container').addClass('d-none');
            }
        }
        
        // Check if color is light (to determine text color)
        function isLightColor(color) {
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
            return brightness > 155;
        }
        
        // Update preview on selection change
        $('#user_id, #rank_id').on('change', updatePreview);
    });
</script>
{% endblock %}
