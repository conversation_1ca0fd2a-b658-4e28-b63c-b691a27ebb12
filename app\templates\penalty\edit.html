{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل العقوبة</h1>
        <div>
            <a href="{{ url_for('penalty.view', id=penalty.id) }}" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i>عرض
            </a>
            <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل العقوبة</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('penalty.edit', id=penalty.id) }}" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">الموظف</label>
                        <input type="text" class="form-control" value="{{ penalty.user.get_full_name() }}" readonly>
                        <div class="form-text">لا يمكن تغيير الموظف بعد إنشاء العقوبة</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="penalty_type" class="form-label">نوع العقوبة <span class="text-danger">*</span></label>
                        <select class="form-select" id="penalty_type" name="penalty_type" required>
                            <option value="verbal_warning" {% if penalty.penalty_type == 'verbal_warning' %}selected{% endif %}>لفت نظر شفوي</option>
                            <option value="written_warning" {% if penalty.penalty_type == 'written_warning' %}selected{% endif %}>لفت نظر كتابي (تحذير أول)</option>
                            <option value="written_notice" {% if penalty.penalty_type == 'written_notice' %}selected{% endif %}>إنذار كتابي (تحذير ثاني)</option>
                            <option value="suspension" {% if penalty.penalty_type == 'suspension' %}selected{% endif %}>إيقاف مؤقت عن العمل / خصم من الراتب</option>
                            <option value="final_warning" {% if penalty.penalty_type == 'final_warning' %}selected{% endif %}>الإنذار النهائي</option>
                            <option value="termination" {% if penalty.penalty_type == 'termination' %}selected{% endif %}>الفصل من العمل / إنهاء التعاقد</option>
                        </select>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="reason" class="form-label">سبب العقوبة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required>{{ penalty.reason }}</textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="details" class="form-label">تفاصيل إضافية</label>
                        <textarea class="form-control" id="details" name="details" rows="3">{{ penalty.details }}</textarea>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="start_date" class="form-label">تاريخ بدء العقوبة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ penalty.start_date.strftime('%Y-%m-%d') }}" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="end_date" class="form-label">تاريخ انتهاء العقوبة</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else '' }}">
                        <div class="form-text">اتركه فارغاً إذا كانت العقوبة دائمة</div>
                    </div>

                    <div class="col-md-4 mb-3 suspension-field {% if penalty.penalty_type != 'suspension' %}d-none{% endif %}">
                        <label for="salary_deduction" class="form-label">قيمة الخصم من الراتب</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="salary_deduction" name="salary_deduction" min="0" step="0.01" value="{{ penalty.salary_deduction }}">
                            <span class="input-group-text">$</span>
                        </div>
                        <div class="form-text">أدخل قيمة الخصم من الراتب (إذا كان ينطبق)</div>
                    </div>

                    {% if penalty.attachments.count() > 0 %}
                    <div class="col-md-12 mb-3">
                        <label class="form-label">المرفقات الحالية:</label>
                        <div class="list-group">
                            {% for attachment in penalty.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                </div>
                                <form action="{{ url_for('penalty.delete_attachment', attachment_id=attachment.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا المرفق؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="col-md-12 mb-3">
                        <label for="attachments" class="form-label">إضافة مرفقات جديدة</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">يمكنك إرفاق ملفات متعددة (مثل المستندات الداعمة أو الأدلة)</div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('penalty.view', id=penalty.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#penalty_type').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
        
        // Update end_date min value when start_date changes
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
            
            // If end_date is before start_date, clear it
            if ($('#end_date').val() && $('#end_date').val() < $(this).val()) {
                $('#end_date').val('');
            }
        });
        
        // Show/hide salary deduction field based on penalty type
        $('#penalty_type').change(function() {
            if ($(this).val() === 'suspension') {
                $('.suspension-field').removeClass('d-none');
            } else {
                $('.suspension-field').addClass('d-none');
                $('#salary_deduction').val('');
            }
        });
        
        // Set initial min value for end_date
        $('#end_date').attr('min', $('#start_date').val());
    });
</script>
{% endblock %}
