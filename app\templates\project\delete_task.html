{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف المهمة</h1>
    <a href="{{ url_for('project.tasks', id=task.project_id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لقائمة المهام
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                
                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف المهمة التالية؟</h4>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ task.title }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>المشروع:</strong> {{ task.project.name }}</p>
                                <p><strong>الوصف:</strong> {{ task.description }}</p>
                                <p><strong>المسؤول:</strong> {{ task.assignee.get_full_name() if task.assignee else 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>تاريخ الإنشاء:</strong> {{ task.created_at.strftime('%Y-%m-%d') }}</p>
                                <p><strong>تاريخ الاستحقاق:</strong> {{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}</p>
                                <p><strong>الحالة:</strong> 
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form action="{{ url_for('project.delete_task', task_id=task.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('project.tasks', id=task.project_id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف المهمة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
