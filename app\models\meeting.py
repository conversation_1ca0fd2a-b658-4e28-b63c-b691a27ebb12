from datetime import datetime, timedelta
from app import db

# Association table for meeting attendees (employees)
MeetingAttendee = db.Table('meeting_attendees',
    db.<PERSON>umn('meeting_id', db.<PERSON>teger, db.<PERSON><PERSON>('meetings.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.<PERSON>ger, db.<PERSON>('users.id'), primary_key=True)
)

# Association table for meeting clients
MeetingClient = db.Table('meeting_clients',
    db.Column('meeting_id', db.Integer, db.<PERSON>ey('meetings.id'), primary_key=True),
    db.Column('client_id', db.Integer, db.<PERSON>ey('clients.id'), primary_key=True)
)

class Meeting(db.Model):
    __tablename__ = 'meetings'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    date = db.Column(db.Date, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    location = db.Column(db.String(255))
    external_attendees = db.Column(db.Text)  # Comma-separated list of external attendees
    external_link = db.Column(db.String(255))  # External link for the meeting (e.g., Zoom, Teams, etc.)
    attachments_link = db.Column(db.String(255))  # Link to meeting attachments (documents, presentations, etc.)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='created_meetings')

    # Many-to-many relationships
    attendees = db.relationship('User', secondary=MeetingAttendee, lazy='subquery',
                               backref=db.backref('meetings', lazy=True))
    clients = db.relationship('Client', secondary=MeetingClient, lazy='subquery',
                             backref=db.backref('meetings', lazy=True))

    # Attachments for this meeting
    attachments = db.relationship('MeetingAttachment', backref='meeting', lazy='dynamic', cascade='all, delete-orphan')

    # Summaries for this meeting
    summaries = db.relationship('MeetingSummary', backref='meeting', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Meeting {self.id}: {self.title} ({self.date})>'

    def get_duration_minutes(self):
        """Calculate the duration of the meeting in minutes"""
        if not self.start_time or not self.end_time:
            return 0

        start_datetime = datetime.combine(datetime.today(), self.start_time)
        end_datetime = datetime.combine(datetime.today(), self.end_time)

        if end_datetime < start_datetime:  # Meeting ends on the next day
            end_datetime = datetime.combine(datetime.today() + timedelta(days=1), self.end_time)

        delta = end_datetime - start_datetime
        return delta.seconds // 60

class MeetingAttachment(db.Model):
    __tablename__ = 'meeting_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    meeting_id = db.Column(db.Integer, db.ForeignKey('meetings.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='meeting_attachments')

    def __repr__(self):
        return f'<MeetingAttachment {self.filename}>'


class MeetingSummary(db.Model):
    __tablename__ = 'meeting_summaries'

    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    meeting_id = db.Column(db.Integer, db.ForeignKey('meetings.id'), nullable=False)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='meeting_summaries')

    def __repr__(self):
        return f'<MeetingSummary {self.id} for Meeting {self.meeting_id}>'
