import sqlite3
import os

def migrate_database(db_path):
    print(f"Attempting to migrate database: {db_path}")

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the users table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    if not cursor.fetchone():
        print(f"No 'users' table found in {db_path}")
        conn.close()
        return False

    # Get current columns in the users table
    cursor.execute('PRAGMA table_info(users)')
    columns = [column[1] for column in cursor.fetchall()]
    print(f"Current columns in users table: {columns}")

    # Add new columns if they don't exist
    new_columns = {
        'full_name_ar': 'TEXT',
        'full_name_en': 'TEXT'
    }

    for column_name, column_type in new_columns.items():
        if column_name not in columns:
            try:
                cursor.execute(f'ALTER TABLE users ADD COLUMN {column_name} {column_type}')
                print(f"Added column {column_name} to users table")
            except sqlite3.OperationalError as e:
                print(f"Error adding column {column_name}: {e}")

    # Commit changes and close connection
    conn.commit()
    conn.close()
    print(f"Migration completed successfully for {db_path}")
    return True

def run_migration():
    # Get the paths to the database files
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    db_paths = [
        os.path.join(base_dir, 'app.db'),
        os.path.join(base_dir, 'app', 'sparkle.db')
    ]

    success = False
    for db_path in db_paths:
        if os.path.exists(db_path):
            if migrate_database(db_path):
                success = True

    if not success:
        print("No databases were successfully migrated.")
    else:
        print("Migration process completed.")

if __name__ == "__main__":
    run_migration()
