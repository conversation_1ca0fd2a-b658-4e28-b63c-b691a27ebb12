"""
Migration script to add external_link column to meetings table
"""
import sqlite3
import os

def run_migration():
    """
    Add external_link column to meetings table
    """
    # Get the database path
    db_path = os.path.join('app', 'sparkle.db')

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Check if the column already exists
        cursor.execute("PRAGMA table_info(meetings)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'external_link' not in column_names:
            # Add the external_link column
            cursor.execute("ALTER TABLE meetings ADD COLUMN external_link TEXT")
            print("Added external_link column to meetings table")
        else:
            print("external_link column already exists in meetings table")

        # Commit the changes
        conn.commit()

    except Exception as e:
        print(f"Error during migration: {e}")
        conn.rollback()
    finally:
        # Close the connection
        conn.close()

if __name__ == "__main__":
    run_migration()
