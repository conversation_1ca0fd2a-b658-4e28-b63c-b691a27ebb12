{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>{{ client.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('client.index') }}">العملاء</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ client.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <a href="{{ url_for('client.edit', id=client.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>تعديل
                </a>
                <a href="{{ url_for('client.delete', id=client.id) }}" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i>حذف
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Client Information -->
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات العميل</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            {% if client.profile_image and client.profile_image != 'default.jpg' %}
                            <img src="{{ url_for('static', filename=client.profile_image) }}" alt="{{ client.name }}" class="img-thumbnail rounded-circle mb-2" style="width: 150px; height: 150px; object-fit: cover;">
                            {% else %}
                            <img src="{{ url_for('static', filename='uploads/default.jpg') }}" alt="{{ client.name }}" class="img-thumbnail rounded-circle mb-2" style="width: 150px; height: 150px; object-fit: cover;">
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">اسم العميل / الشركة:</div>
                                <div class="col-md-8">{{ client.name }}</div>
                            </div>
                    {% if client.contact_person %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">الشخص المسؤول:</div>
                        <div class="col-md-8">{{ client.contact_person }}</div>
                    </div>
                    {% endif %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">البريد الإلكتروني:</div>
                        <div class="col-md-8">
                            <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">رقم الهاتف:</div>
                        <div class="col-md-8">
                            <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                        </div>
                    </div>
                    {% if client.address %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">العنوان:</div>
                        <div class="col-md-8">{{ client.address }}</div>
                    </div>
                    {% endif %}
                    {% if client.website %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">الموقع الإلكتروني:</div>
                        <div class="col-md-8">
                            <a href="{{ client.website }}" target="_blank">{{ client.website }}</a>
                        </div>
                    </div>
                    {% endif %}
                    {% if client.industry %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">المجال / الصناعة:</div>
                        <div class="col-md-8">{{ client.industry }}</div>
                    </div>
                    {% endif %}
                    {% if client.notes %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">ملاحظات:</div>
                        <div class="col-md-8">{{ client.notes }}</div>
                    </div>
                    {% endif %}
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">تاريخ الإضافة:</div>
                        <div class="col-md-8">{{ client.created_at.strftime('%Y-%m-%d') }}</div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Projects -->
            <div class="card shadow mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">مشاريع العميل</h5>
                    <div>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <a href="{{ url_for('project.create') }}?client_id={{ client.id }}" class="btn btn-sm btn-primary me-2">
                            <i class="fas fa-plus me-1"></i>إضافة مشروع جديد
                        </a>
                        {% endif %}
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#projectsFilterCollapse" aria-expanded="false" aria-controls="projectsFilterCollapse">
                            <i class="fas fa-filter me-1"></i>تصفية
                        </button>
                    </div>
                </div>

                <!-- Projects Filter -->
                <div class="collapse" id="projectsFilterCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="{{ url_for('client.view', id=client.id) }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="projects_status" class="form-label">الحالة</label>
                                <select class="form-select" id="projects_status" name="projects_status">
                                    <option value="all" {% if projects_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                    <option value="pending" {% if projects_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="in_progress" {% if projects_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                    <option value="completed" {% if projects_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if projects_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="projects_per_page" class="form-label">عدد النتائج</label>
                                <select class="form-select" id="projects_per_page" name="projects_per_page">
                                    <option value="10" {% if projects_per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if projects_per_page == 25 %}selected{% endif %}>25</option>
                                    <option value="50" {% if projects_per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if projects_per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">تطبيق</button>
                                <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    {% if projects.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المشروع</th>
                                    <th>الحالة</th>
                                    <th>الأولوية</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects.items %}
                                <tr>
                                    <td>{{ project.name }}</td>
                                    <td>
                                        {% if project.status == 'pending' %}
                                        <span class="badge bg-secondary">قيد الانتظار</span>
                                        {% elif project.status == 'in_progress' %}
                                        <span class="badge bg-primary">قيد التنفيذ</span>
                                        {% elif project.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif project.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if project.priority == 'low' %}
                                        <span class="badge bg-info">منخفضة</span>
                                        {% elif project.priority == 'medium' %}
                                        <span class="badge bg-warning">متوسطة</span>
                                        {% elif project.priority == 'high' %}
                                        <span class="badge bg-danger">عالية</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ project.start_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Projects Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if projects.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('client.view', id=client.id, projects_page=projects.prev_num, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in projects.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                    {% if page_num %}
                                        {% if page_num == projects.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('client.view', id=client.id, projects_page=page_num, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if projects.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('client.view', id=client.id, projects_page=projects.next_num, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        لا توجد مشاريع لهذا العميل حتى الآن.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Client Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إحصائيات العميل</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h3">{{ projects_count }}</div>
                            <div class="text-muted">إجمالي المشاريع</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3">{{ completed_projects_count }}</div>
                            <div class="text-muted">المشاريع المكتملة</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3">{{ in_progress_projects_count }}</div>
                            <div class="text-muted">المشاريع الجارية</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h3">{{ pending_projects_count }}</div>
                            <div class="text-muted">المشاريع المعلقة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">النشاط الأخير</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% if recent_projects %}
                            {% for project in recent_projects %}
                            <div class="timeline-item">
                                <div class="timeline-date">{{ project.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="timeline-content">
                                    <div class="timeline-title">تم إنشاء مشروع جديد</div>
                                    <div class="timeline-text">تم إنشاء مشروع "{{ project.name }}"</div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            لا يوجد نشاط حديث لهذا العميل.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
    .timeline {
        position: relative;
        padding: 0;
        list-style: none;
    }

    .timeline-item {
        position: relative;
        padding-left: 40px;
        margin-bottom: 20px;
    }

    .timeline-item:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #007bff;
    }

    .timeline-item:after {
        content: '';
        position: absolute;
        left: 5px;
        top: 12px;
        bottom: -20px;
        width: 2px;
        background-color: #e9ecef;
    }

    .timeline-item:last-child:after {
        display: none;
    }

    .timeline-date {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .timeline-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .timeline-text {
        color: #495057;
    }
</style>
{% endblock %}
