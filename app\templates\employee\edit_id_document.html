{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل وثيقة هوية</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة لصفحة الموظف
        </a>
        <a href="{{ url_for('employee.view_id_document', id=document.id) }}" class="btn btn-info">
            <i class="fas fa-eye me-1"></i>عرض الوثيقة
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">تعديل بيانات الوثيقة لـ {{ employee.get_full_name() }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('employee.edit_id_document', id=document.id) }}" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="document_type" class="form-label">نوع الوثيقة <span class="text-danger">*</span></label>
                    <select class="form-select" id="document_type" name="document_type" required>
                        <option value="" disabled>اختر نوع الوثيقة</option>
                        <option value="جواز سفر" {% if document.document_type == 'جواز سفر' %}selected{% endif %}>جواز سفر</option>
                        <option value="بطاقة هوية" {% if document.document_type == 'بطاقة هوية' %}selected{% endif %}>بطاقة هوية</option>
                        <option value="رخصة قيادة" {% if document.document_type == 'رخصة قيادة' %}selected{% endif %}>رخصة قيادة</option>
                        <option value="إقامة" {% if document.document_type == 'إقامة' %}selected{% endif %}>إقامة</option>
                        <option value="أخرى" {% if document.document_type not in ['جواز سفر', 'بطاقة هوية', 'رخصة قيادة', 'إقامة'] %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="document_number" class="form-label">رقم الوثيقة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="document_number" name="document_number" value="{{ document.document_number }}" required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                    <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ document.issue_date.strftime('%Y-%m-%d') if document.issue_date else '' }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" value="{{ document.expiry_date.strftime('%Y-%m-%d') if document.expiry_date else '' }}">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="issuing_country" class="form-label">بلد الإصدار</label>
                <input type="text" class="form-control" id="issuing_country" name="issuing_country" value="{{ document.issuing_country }}">
            </div>
            
            <div class="mb-3">
                <label for="document_file" class="form-label">ملف الوثيقة</label>
                <input type="file" class="form-control" id="document_file" name="document_file">
                <small class="text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الملف الحالي.</small>
                
                {% if document.document_file %}
                <div class="mt-2">
                    <p><strong>الملف الحالي:</strong> 
                        <a href="{{ url_for('static', filename=document.document_file) }}" target="_blank">
                            {{ document.document_file.split('/')[-1] }}
                        </a>
                    </p>
                </div>
                {% endif %}
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
