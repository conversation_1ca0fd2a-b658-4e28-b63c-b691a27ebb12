{% extends 'base.html' %}

{% block styles %}
<style>
    .form-control-color {
        width: 50px;
    }

    .animated-text {
        white-space: nowrap;
        animation: marquee 25s linear infinite;
        display: inline-block;
        padding-right: 100%;
    }

    @keyframes marquee {
        0% { transform: translateX(100%); }
        100% { transform: translateX(-100%); }
    }

    #announcement_preview {
        height: 40px;
        display: flex;
        align-items: center;
    }

    #announcement_preview_text {
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إعدادات النظام</h1>
    <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">إعدادات عامة</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header {% if maintenance_mode %}bg-warning{% else %}bg-success{% endif %}">
                                <h5 class="mb-0 text-white">حالة النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6>الوضع الحالي:</h6>
                                        {% if maintenance_mode %}
                                        <p class="text-warning mb-0">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <strong>وضع الصيانة</strong> - فقط المسؤولون يمكنهم تسجيل الدخول
                                        </p>
                                        {% else %}
                                        <p class="text-success mb-0">
                                            <i class="fas fa-check-circle me-1"></i>
                                            <strong>متصل</strong> - يمكن للجميع تسجيل الدخول
                                        </p>
                                        {% endif %}
                                    </div>
                                    <form action="{{ url_for('system.toggle_maintenance') }}" method="POST">
                                        <button type="submit" class="btn {% if maintenance_mode %}btn-success{% else %}btn-warning{% endif %}">
                                            {% if maintenance_mode %}
                                            <i class="fas fa-toggle-on me-1"></i>تفعيل النظام
                                            {% else %}
                                            <i class="fas fa-toggle-off me-1"></i>تفعيل وضع الصيانة
                                            {% endif %}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header {% if announcement_enabled %}bg-info{% else %}bg-secondary{% endif %}">
                                <h5 class="mb-0 text-white">شريط الإعلانات</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6>الحالة:</h6>
                                        {% if announcement_enabled %}
                                        <p class="text-info mb-0">
                                            <i class="fas fa-bullhorn me-1"></i>
                                            <strong>مفعل</strong> - شريط الإعلانات ظاهر للجميع
                                        </p>
                                        {% else %}
                                        <p class="text-secondary mb-0">
                                            <i class="fas fa-ban me-1"></i>
                                            <strong>غير مفعل</strong> - شريط الإعلانات مخفي
                                        </p>
                                        {% endif %}
                                    </div>
                                    <form action="{{ url_for('system.toggle_announcement') }}" method="POST">
                                        <button type="submit" class="btn {% if announcement_enabled %}btn-secondary{% else %}btn-info{% endif %}">
                                            {% if announcement_enabled %}
                                            <i class="fas fa-toggle-on me-1"></i>إخفاء الشريط
                                            {% else %}
                                            <i class="fas fa-toggle-off me-1"></i>إظهار الشريط
                                            {% endif %}
                                        </button>
                                    </form>
                                </div>

                                <hr>

                                <form action="{{ url_for('system.update_announcement') }}" method="POST">
                                    <div class="mb-3">
                                        <label for="announcement_text" class="form-label">نص الإعلان:</label>
                                        <textarea class="form-control" id="announcement_text" name="announcement_text" rows="2">{{ announcement_text }}</textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="announcement_bg_color" class="form-label">لون خلفية الشريط:</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="announcement_bg_color" name="announcement_bg_color" value="{{ announcement_bg_color }}">
                                                <input type="text" class="form-control" value="{{ announcement_bg_color }}" id="bg_color_text">
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="announcement_text_color" class="form-label">لون النص:</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="announcement_text_color" name="announcement_text_color" value="{{ announcement_text_color }}">
                                                <input type="text" class="form-control" value="{{ announcement_text_color }}" id="text_color_text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="announcement_animated" name="announcement_animated" {% if announcement_animated %}checked{% endif %}>
                                            <label class="form-check-label" for="announcement_animated">تفعيل النص المتحرك</label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">معاينة:</label>
                                        <div class="p-2 rounded overflow-hidden" id="announcement_preview" style="background-color: {{ announcement_bg_color }}; color: {{ announcement_text_color }}; text-align: center;">
                                            <div id="announcement_preview_text" class="{% if announcement_animated %}animated-text{% endif %}">
                                                {{ announcement_text }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup and Restore Section -->
        <div class="card shadow">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">النسخ الاحتياطي والاستعادة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">إنشاء نسخة احتياطية</h5>
                            </div>
                            <div class="card-body">
                                <p>
                                    <i class="fas fa-info-circle me-1 text-primary"></i>
                                    قم بإنشاء نسخة احتياطية كاملة من النظام، بما في ذلك قاعدة البيانات والملفات المرفوعة وإعدادات النظام.
                                </p>
                                <p class="text-muted">
                                    يوصى بإنشاء نسخة احتياطية بشكل دوري للحفاظ على بيانات النظام.
                                </p>
                                <div class="alert alert-info">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>نصيحة:</strong> قم بإنشاء نسخة احتياطية قبل إجراء أي تغييرات كبيرة على النظام.
                                </div>
                                <div class="text-center mt-3">
                                    <a href="{{ url_for('system.backup') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                    </a>

                                    {% if backup_log_exists %}
                                    <div class="mt-2">
                                        <a href="{{ url_for('system.view_log', log_type='backup') }}" class="btn btn-outline-primary">
                                            <i class="fas fa-file-alt me-1"></i> عرض سجل النسخ الاحتياطي
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">استعادة نسخة احتياطية</h5>
                            </div>
                            <div class="card-body">
                                <p>
                                    <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                                    استعادة النظام من نسخة احتياطية سابقة. سيؤدي هذا إلى استبدال جميع البيانات الحالية.
                                </p>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هذا الإجراء لا يمكن التراجع عنه.
                                </div>
                                <div class="text-center mt-3">
                                        <a href="{{ url_for('restore.restore_page') }}" class="btn btn-warning btn-lg">
                                            <i class="fas fa-upload me-1"></i>الذهاب إلى صفحة الاستعادة
                                        </a>

                                        {% if restore_log_exists or restore_error_log_exists %}
                                        <div class="mt-2">
                                            {% if restore_log_exists %}
                                            <a href="{{ url_for('system.view_log', log_type='restore') }}" class="btn btn-outline-warning">
                                                <i class="fas fa-file-alt me-1"></i> عرض سجل الاستعادة
                                            </a>
                                            {% endif %}

                                            {% if restore_error_log_exists %}
                                            <a href="{{ url_for('system.view_log', log_type='restore_error') }}" class="btn btn-outline-danger ms-2">
                                                <i class="fas fa-exclamation-triangle me-1"></i> عرض سجل أخطاء الاستعادة
                                            </a>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Management Section -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">إدارة الملفات المرفوعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <p>
                                            <i class="fas fa-folder-open me-1 text-success"></i>
                                            إدارة الملفات المرفوعة في النظام، مثل صور الموظفين والعملاء ومرفقات الاجتماعات والبريد الداخلي.
                                        </p>
                                        <p class="text-muted">
                                            يمكنك استخدام هذه الصفحة لعرض وحذف الملفات المرفوعة لتوفير مساحة على الخادم.
                                        </p>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <strong>تحذير:</strong> حذف الملفات المرفوعة قد يؤدي إلى فقدان بعض البيانات المرتبطة بها. تأكد من أنك لا تحتاج إلى هذه الملفات قبل حذفها.
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center d-flex align-items-center justify-content-center">
                                        <div>
                                            <a href="{{ url_for('system.manage_files') }}" class="btn btn-success btn-lg">
                                                <i class="fas fa-folder-open me-1"></i>إدارة الملفات المرفوعة
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Log Section -->
        <div class="card shadow mt-4">
            <div class="card-header bg-purple text-white" style="background-color: #6610f2;">
                <h5 class="mb-0">سجل النشاطات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p>
                            <i class="fas fa-history me-1 text-purple" style="color: #6610f2;"></i>
                            سجل النشاطات يحتفظ بسجل كامل لجميع الإجراءات التي تتم في النظام، مثل إنشاء وتعديل وحذف البيانات.
                        </p>
                        <p class="text-muted">
                            يمكنك استخدام سجل النشاطات لتتبع التغييرات ومراقبة أنشطة المستخدمين في النظام.
                        </p>
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-1"></i>
                            <strong>نصيحة:</strong> استخدم خيارات التصفية للبحث عن إجراءات محددة أو مستخدمين معينين.
                        </div>
                    </div>
                    <div class="col-md-4 text-center d-flex align-items-center justify-content-center">
                        <div>
                            <div class="display-4 mb-2">{{ activity_log_count }}</div>
                            <p class="mb-3">إجمالي النشاطات المسجلة</p>
                            <a href="{{ url_for('system.activity_log') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-list-alt me-1"></i>عرض سجل النشاطات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information about backup and images -->
        <div class="card shadow mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">معلومات عن النسخ الاحتياطي</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>ماذا يتضمن النسخ الاحتياطي؟</h5>
                    <p>النسخة الاحتياطية تشمل جميع البيانات في النظام، بما في ذلك:</p>
                    <ul>
                        <li><strong>قاعدة البيانات بالكامل:</strong> جميع بيانات الموظفين، العملاء، المشاريع، الفواتير، وغيرها.</li>
                        <li><strong>جميع الملفات المرفوعة:</strong> صور الموظفين، صور العملاء، مرفقات المشاريع، وغيرها.</li>
                        <li><strong>إعدادات النظام:</strong> شريط الإعلانات، وضع الصيانة، وغيرها من الإعدادات.</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h5><i class="fas fa-image me-2"></i>صور الموظفين والعملاء</h5>
                    <p>صور الموظفين والعملاء محفوظة بالطريقة التالية:</p>
                    <ul>
                        <li>يتم تخزين مسارات الصور في قاعدة البيانات في جداول المستخدمين والعملاء.</li>
                        <li>يتم تخزين الصور الفعلية في مجلدات على الخادم (uploads/profile_images و uploads/client_images).</li>
                        <li>عند إنشاء نسخة احتياطية، يتم نسخ كل من قاعدة البيانات والصور الفعلية.</li>
                        <li>عند استعادة النسخة الاحتياطية، يتم استعادة كل من قاعدة البيانات والصور الفعلية.</li>
                    </ul>
                    <p class="mb-0">هذا يضمن أن جميع الصور ستكون متاحة بعد استعادة النسخة الاحتياطية.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Update preview when text changes
        $('#announcement_text').on('input', function() {
            $('#announcement_preview_text').text($(this).val());
        });

        // Update background color
        $('#announcement_bg_color').on('input', function() {
            const color = $(this).val();
            $('#bg_color_text').val(color);
            $('#announcement_preview').css('background-color', color);
        });

        // Update text color
        $('#announcement_text_color').on('input', function() {
            const color = $(this).val();
            $('#text_color_text').val(color);
            $('#announcement_preview').css('color', color);
        });

        // Update color picker when text input changes
        $('#bg_color_text').on('input', function() {
            const color = $(this).val();
            $('#announcement_bg_color').val(color);
            $('#announcement_preview').css('background-color', color);
        });

        $('#text_color_text').on('input', function() {
            const color = $(this).val();
            $('#announcement_text_color').val(color);
            $('#announcement_preview').css('color', color);
        });

        // Toggle animation
        $('#announcement_animated').on('change', function() {
            if ($(this).is(':checked')) {
                $('#announcement_preview_text').addClass('animated-text');
            } else {
                $('#announcement_preview_text').removeClass('animated-text');
            }
        });

        // Backup and Restore functionality
        $('#confirmRestore').on('change', function() {
            if ($(this).is(':checked')) {
                $('#confirmRestoreBtn').prop('disabled', false);
            } else {
                $('#confirmRestoreBtn').prop('disabled', true);
            }
        });

        // Validate backup file
        $('#backup_file').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileExt = file.name.split('.').pop().toLowerCase();
                if (fileExt !== 'zip') {
                    alert('يرجى اختيار ملف بتنسيق ZIP فقط.');
                    $(this).val('');
                }
            }
        });
    });
</script>
{% endblock %}