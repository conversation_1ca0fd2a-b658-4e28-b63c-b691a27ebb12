# هيكل الواجهة الأمامية وتصميم API للنظام المالي المتكامل

## هيكل مجلدات الواجهة الأمامية (Frontend Structure)

```
app/
├── templates/
│   └── finance/
│       ├── enhanced_dashboard.html          # لوحة التحكم الرئيسية
│       ├── accounts/
│       │   ├── chart_of_accounts.html       # شجرة الحسابات
│       │   └── account_details.html         # تفاصيل حساب
│       ├── invoicing/
│       │   ├── invoice_list.html            # قائمة الفواتير
│       │   ├── create_invoice.html          # إنشاء فاتورة
│       │   ├── invoice_details.html         # تفاصيل فاتورة
│       │   └── payment_form.html            # نموذج تسجيل دفعة
│       ├── vendors/
│       │   ├── vendor_list.html             # قائمة الموردين
│       │   ├── create_vendor.html           # إضافة مورد
│       │   ├── vendor_bills.html            # فواتير الموردين
│       │   └── create_bill.html             # إنشاء فاتورة مورد
│       ├── reports/
│       │   ├── profit_loss.html             # تقرير قائمة الدخل
│       │   ├── balance_sheet.html           # تقرير الميزانية
│       │   ├── cash_flow.html               # تقرير التدفقات النقدية
│       │   └── aging_report.html            # تقرير أعمار الديون
│       └── journal/
│           ├── journal_entries.html         # قيود اليومية
│           └── create_entry.html            # إنشاء قيد يدوي
├── static/
│   ├── js/
│   │   └── finance/
│   │       ├── dashboard.js                 # JavaScript للوحة التحكم
│   │       ├── invoice-manager.js           # إدارة الفواتير
│   │       ├── payment-processor.js         # معالج الدفعات
│   │       ├── vendor-manager.js            # إدارة الموردين
│   │       ├── report-generator.js          # مولد التقارير
│   │       └── chart-of-accounts.js         # إدارة شجرة الحسابات
│   └── css/
│       └── finance/
│           ├── dashboard.css                # تنسيقات لوحة التحكم
│           ├── forms.css                    # تنسيقات النماذج
│           └── reports.css                  # تنسيقات التقارير
```

## تصميم واجهة برمجة التطبيقات (RESTful API Design)

### 1. وحدة المحاسبة العامة (Chart of Accounts)

```
GET    /api/v2/accounts                      # قائمة جميع الحسابات
POST   /api/v2/accounts                      # إنشاء حساب جديد
GET    /api/v2/accounts/{id}                 # تفاصيل حساب محدد
PUT    /api/v2/accounts/{id}                 # تحديث حساب
DELETE /api/v2/accounts/{id}                 # حذف حساب
GET    /api/v2/accounts/{id}/balance         # رصيد حساب محدد
GET    /api/v2/accounts/tree                 # شجرة الحسابات الهرمية
```

### 2. وحدة قيود اليومية (Journal Entries)

```
GET    /api/v2/journal-entries               # قائمة قيود اليومية
POST   /api/v2/journal-entries               # إنشاء قيد جديد
GET    /api/v2/journal-entries/{id}          # تفاصيل قيد محدد
PUT    /api/v2/journal-entries/{id}          # تحديث قيد
DELETE /api/v2/journal-entries/{id}          # حذف قيد
POST   /api/v2/journal-entries/{id}/post     # ترحيل قيد
POST   /api/v2/journal-entries/{id}/cancel   # إلغاء قيد
```

### 3. وحدة الفواتير والعملاء (Invoicing)

```
GET    /api/v2/invoices                      # قائمة الفواتير
POST   /api/v2/invoices                      # إنشاء فاتورة جديدة
GET    /api/v2/invoices/{id}                 # تفاصيل فاتورة
PUT    /api/v2/invoices/{id}                 # تحديث فاتورة
DELETE /api/v2/invoices/{id}                 # حذف فاتورة
POST   /api/v2/invoices/{id}/send            # إرسال فاتورة للعميل
POST   /api/v2/invoices/{id}/mark-paid       # تحديد فاتورة كمدفوعة
GET    /api/v2/invoices/overdue              # الفواتير المتأخرة
```

### 4. وحدة الدفعات (Payments)

```
GET    /api/v2/payments/received             # قائمة الدفعات المستلمة
POST   /api/v2/payments/receive              # تسجيل دفعة مستلمة
GET    /api/v2/payments/made                 # قائمة الدفعات المسددة
POST   /api/v2/payments/make                 # تسجيل دفعة مسددة
GET    /api/v2/payments/{id}                 # تفاصيل دفعة
PUT    /api/v2/payments/{id}                 # تحديث دفعة
DELETE /api/v2/payments/{id}                 # حذف دفعة
```

### 5. وحدة الموردين (Vendors)

```
GET    /api/v2/vendors                       # قائمة الموردين
POST   /api/v2/vendors                       # إضافة مورد جديد
GET    /api/v2/vendors/{id}                  # تفاصيل مورد
PUT    /api/v2/vendors/{id}                  # تحديث مورد
DELETE /api/v2/vendors/{id}                  # حذف مورد
GET    /api/v2/vendors/{id}/bills            # فواتير مورد محدد
GET    /api/v2/vendors/{id}/outstanding      # المبالغ المستحقة لمورد
```

### 6. وحدة فواتير الموردين (Vendor Bills)

```
GET    /api/v2/vendor-bills                  # قائمة فواتير الموردين
POST   /api/v2/vendor-bills                  # إنشاء فاتورة مورد
GET    /api/v2/vendor-bills/{id}             # تفاصيل فاتورة مورد
PUT    /api/v2/vendor-bills/{id}             # تحديث فاتورة مورد
DELETE /api/v2/vendor-bills/{id}             # حذف فاتورة مورد
POST   /api/v2/vendor-bills/{id}/approve     # اعتماد فاتورة مورد
```

### 7. وحدة التقارير المالية (Financial Reports)

```
GET    /api/v2/reports/profit-loss           # تقرير قائمة الدخل
GET    /api/v2/reports/balance-sheet         # تقرير الميزانية العمومية
GET    /api/v2/reports/cash-flow             # تقرير التدفقات النقدية
GET    /api/v2/reports/trial-balance         # ميزان المراجعة
GET    /api/v2/reports/aging-receivables     # تقرير أعمار المدينين
GET    /api/v2/reports/aging-payables        # تقرير أعمار الدائنين
GET    /api/v2/reports/general-ledger        # دفتر الأستاذ العام
```

### 8. وحدة لوحة التحكم (Dashboard)

```
GET    /api/v2/dashboard/kpis                # مؤشرات الأداء الرئيسية
GET    /api/v2/dashboard/charts              # بيانات الرسوم البيانية
GET    /api/v2/dashboard/recent-activities   # الأنشطة الحديثة
GET    /api/v2/dashboard/alerts              # التنبيهات المالية
```

## أمثلة على طلبات API مع البيانات

### 1. إنشاء فاتورة جديدة

```json
POST /api/v2/invoices
Content-Type: application/json

{
  "client_id": 1,
  "project_id": 5,
  "invoice_number": "INV-2024-001",
  "issue_date": "2024-01-15",
  "due_date": "2024-02-14",
  "payment_terms": 30,
  "items": [
    {
      "description": "تصميم موقع إلكتروني",
      "quantity": 1,
      "unit_price": 5000.00
    },
    {
      "description": "برمجة نظام إدارة المحتوى",
      "quantity": 1,
      "unit_price": 8000.00
    }
  ],
  "tax_rate": 15,
  "discount_amount": 500.00,
  "notes": "يرجى الدفع خلال 30 يوم من تاريخ الإصدار"
}
```

### 2. تسجيل دفعة مستلمة

```json
POST /api/v2/payments/receive
Content-Type: application/json

{
  "customer_id": 1,
  "invoice_id": 15,
  "payment_number": "PAY-2024-001",
  "payment_date": "2024-01-20",
  "amount": 12500.00,
  "payment_method": "bank_transfer",
  "reference_number": "TXN-*********",
  "notes": "دفعة كاملة للفاتورة INV-2024-001"
}
```

### 3. إنشاء فاتورة مورد

```json
POST /api/v2/vendor-bills
Content-Type: application/json

{
  "vendor_id": 3,
  "bill_number": "BILL-2024-001",
  "vendor_reference": "VENDOR-INV-456",
  "issue_date": "2024-01-10",
  "due_date": "2024-02-09",
  "items": [
    {
      "description": "اشتراك Adobe Creative Suite",
      "quantity": 1,
      "unit_price": 600.00,
      "account_id": 25  // حساب "اشتراكات البرامج"
    },
    {
      "description": "خدمات استضافة",
      "quantity": 1,
      "unit_price": 200.00,
      "account_id": 26  // حساب "مصروفات الاستضافة"
    }
  ],
  "tax_amount": 120.00
}
```

### 4. طلب تقرير قائمة الدخل

```
GET /api/v2/reports/profit-loss?start_date=2024-01-01&end_date=2024-01-31
```

الاستجابة:
```json
{
  "period": "2024-01-01 إلى 2024-01-31",
  "revenue_accounts": [
    {
      "account_code": "4010",
      "account_name": "إيرادات خدمات التصميم",
      "balance": 15000.00
    },
    {
      "account_code": "4020",
      "account_name": "إيرادات خدمات البرمجة",
      "balance": 25000.00
    }
  ],
  "total_revenue": 40000.00,
  "expense_accounts": [
    {
      "account_code": "5010",
      "account_name": "رواتب الموظفين",
      "balance": 12000.00
    },
    {
      "account_code": "5020",
      "account_name": "اشتراكات البرامج",
      "balance": 1500.00
    }
  ],
  "total_expenses": 13500.00,
  "net_profit": 26500.00
}
```

## مكونات JavaScript الرئيسية

### 1. مدير الفواتير (InvoiceManager)

```javascript
class InvoiceManager {
    constructor() {
        this.apiBase = '/api/v2';
    }
    
    async createInvoice(invoiceData) {
        const response = await fetch(`${this.apiBase}/invoices`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(invoiceData)
        });
        return await response.json();
    }
    
    async markAsPaid(invoiceId) {
        const response = await fetch(`${this.apiBase}/invoices/${invoiceId}/mark-paid`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': this.getCSRFToken()
            }
        });
        return await response.json();
    }
    
    getCSRFToken() {
        return document.querySelector('meta[name=csrf-token]').getAttribute('content');
    }
}
```

### 2. معالج الدفعات (PaymentProcessor)

```javascript
class PaymentProcessor {
    constructor() {
        this.apiBase = '/api/v2';
    }
    
    async receivePayment(paymentData) {
        const response = await fetch(`${this.apiBase}/payments/receive`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(paymentData)
        });
        return await response.json();
    }
    
    async getPaymentHistory(customerId) {
        const response = await fetch(`${this.apiBase}/payments/received?customer_id=${customerId}`);
        return await response.json();
    }
}
```

### 3. مولد التقارير (ReportGenerator)

```javascript
class ReportGenerator {
    constructor() {
        this.apiBase = '/api/v2';
    }
    
    async generateProfitLoss(startDate, endDate) {
        const response = await fetch(
            `${this.apiBase}/reports/profit-loss?start_date=${startDate}&end_date=${endDate}`
        );
        return await response.json();
    }
    
    async generateBalanceSheet(asOfDate) {
        const response = await fetch(
            `${this.apiBase}/reports/balance-sheet?as_of_date=${asOfDate}`
        );
        return await response.json();
    }
    
    renderProfitLossChart(data) {
        // استخدام Chart.js لرسم الرسوم البيانية
        const ctx = document.getElementById('profitLossChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['الإيرادات', 'المصروفات', 'صافي الربح'],
                datasets: [{
                    data: [data.total_revenue, data.total_expenses, data.net_profit],
                    backgroundColor: ['#28a745', '#dc3545', '#007bff']
                }]
            }
        });
    }
}
```
