{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل بيانات الموظف</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للتفاصيل
        </a>
        <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-1"></i>قائمة الموظفين
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">تعديل بيانات {{ employee.get_full_name() }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('employee.edit', id=employee.id) }}" enctype="multipart/form-data">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                {% if current_user.has_role('admin') %}
                                <input type="text" class="form-control" id="username" name="username" value="{{ employee.username }}">
                                <small class="text-muted">يمكن للمسؤول تغيير اسم المستخدم</small>
                                {% else %}
                                <input type="text" class="form-control" id="username" value="{{ employee.username }}" readonly>
                                <small class="text-muted">لا يمكن تغيير اسم المستخدم</small>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ employee.email }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <small class="text-muted">اتركها فارغة إذا لم ترغب في تغيير كلمة المرور</small>
                            </div>
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head') %}
                                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                <!-- Fields that only admins and managers can edit -->
                                <div class="mb-3">
                                    <label for="full_name_ar" class="form-label">الاسم رباعي كامل بالعربي</label>
                                    <input type="text" class="form-control" id="full_name_ar" name="full_name_ar" value="{{ employee.full_name_ar or '' }}">
                                    <small class="text-muted">أدخل الاسم الرباعي الكامل بالعربية</small>
                                </div>
                                <div class="mb-3">
                                    <label for="full_name_en" class="form-label">الاسم رباعي كامل بالإنجليزية</label>
                                    <input type="text" class="form-control" id="full_name_en" name="full_name_en" value="{{ employee.full_name_en or '' }}">
                                    <small class="text-muted">أدخل الاسم الرباعي الكامل بالإنجليزية</small>
                                </div>
                                {% endif %}

                                <!-- Fields that admins, managers, and department heads can edit -->
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">الاسم الأول</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="{{ employee.first_name or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">الاسم الأخير</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="{{ employee.last_name or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="first_name_en" class="form-label">الاسم الأول (بالإنجليزية)</label>
                                    <input type="text" class="form-control" id="first_name_en" name="first_name_en" value="{{ employee.first_name_en or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="last_name_en" class="form-label">الاسم الأخير (بالإنجليزية)</label>
                                    <input type="text" class="form-control" id="last_name_en" name="last_name_en" value="{{ employee.last_name_en or '' }}">
                                </div>

                                <div class="mb-3">
                                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" value="{{ employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else '' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="nationality" class="form-label">الجنسية</label>
                                    <input type="text" class="form-control" id="nationality" name="nationality" value="{{ employee.nationality or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="phone" name="phone" value="{{ employee.phone or '' }}">
                                </div>
                            {% else %}
                                <!-- Read-only fields for regular users -->
                                <div class="mb-3">
                                    <label class="form-label">الاسم رباعي كامل بالعربي</label>
                                    <input type="text" class="form-control" value="{{ employee.full_name_ar or '' }}" readonly>
                                    <input type="hidden" name="full_name_ar" value="{{ employee.full_name_ar or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الاسم رباعي كامل بالإنجليزية</label>
                                    <input type="text" class="form-control" value="{{ employee.full_name_en or '' }}" readonly>
                                    <input type="hidden" name="full_name_en" value="{{ employee.full_name_en or '' }}">
                                </div>

                                <!-- Hidden fields for first and last name for regular users -->
                                <input type="hidden" name="first_name" value="{{ employee.first_name or '' }}">
                                <input type="hidden" name="last_name" value="{{ employee.last_name or '' }}">
                                <input type="hidden" name="first_name_en" value="{{ employee.first_name_en or '' }}">
                                <input type="hidden" name="last_name_en" value="{{ employee.last_name_en or '' }}">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الميلاد</label>
                                    <input type="text" class="form-control" value="{{ employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else '' }}" readonly>
                                    <input type="hidden" name="birth_date" value="{{ employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else '' }}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الجنسية</label>
                                    <input type="text" class="form-control" value="{{ employee.nationality or '' }}" readonly>
                                    <input type="hidden" name="nationality" value="{{ employee.nationality or '' }}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" value="{{ employee.phone or '' }}" readonly>
                                    <input type="hidden" name="phone" value="{{ employee.phone or '' }}">
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    لا يمكن تعديل البيانات الشخصية إلا بواسطة المدير أو المسؤول. يمكنك فقط تغيير كلمة المرور والصورة الشخصية.
                                </div>
                            {% endif %}

                            <div class="mb-3">
                                <label for="profile_image" class="form-label">الصورة الشخصية</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                <small class="text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الصورة الحالية</small>
                                <div class="mt-2">
                                    <p>الصورة الحالية:</p>
                                    {% if employee.profile_image and employee.profile_image != 'default.jpg' %}
                                    <img src="{{ url_for('static', filename=employee.profile_image) }}" alt="{{ employee.get_full_name() }}" class="img-thumbnail" style="max-width: 150px;">
                                    {% else %}
                                    <img src="{{ url_for('static', filename='uploads/default.jpg') }}" alt="{{ employee.get_full_name() }}" class="img-thumbnail" style="max-width: 150px;">
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department, Roles and Status -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">القسم والصلاحيات</h6>
                        </div>
                        <div class="card-body">
                            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">-- اختر القسم --</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" {% if employee.department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <input type="text" class="form-control" value="{{ employee.department.name if employee.department else 'غير محدد' }}" readonly>
                                <input type="hidden" name="department_id" value="{{ employee.department_id or '' }}">
                            </div>
                            {% endif %}

                            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                            <div class="mb-3">
                                <label class="form-label">الصلاحيات</label>
                                {% if current_user.has_role('manager') and employee.username == 'GolDeN' %}
                                <div class="alert alert-warning">
                                    لا يمكن للمدير تعديل صلاحيات المستخدم GolDeN
                                </div>
                                {% else %}
                                <div class="card">
                                    <div class="card-body">
                                        {% for role in roles %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="role_{{ role.id }}" name="roles" value="{{ role.id }}"
                                                {% if role in employee.roles %}checked{% endif %}
                                                {% if current_user.has_role('manager') and role.name in ['admin', 'manager'] and role in employee.roles %}
                                                    disabled
                                                {% endif %}>
                                            <label class="form-check-label" for="role_{{ role.id }}">
                                                {{ role.name }}
                                                {% if role.description %}
                                                <small class="text-muted d-block">{{ role.description }}</small>
                                                {% endif %}
                                                {% if current_user.has_role('manager') and role.name in ['admin', 'manager'] and role in employee.roles %}
                                                <small class="text-danger d-block">لا يمكن للمدير إزالة هذه الصلاحية</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                        {% endfor %}

                                        {% if current_user.has_role('manager') %}
                                        <div class="alert alert-info mt-3">
                                            <small>ملاحظة: المدير يمكنه فقط تعيين صلاحيات رئيس قسم وما دونها</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if employee.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">نشط</label>
                                </div>
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <div>
                                    {% if employee.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </div>
                                <input type="hidden" name="is_active" value="{{ 'on' if employee.is_active else '' }}">
                                <small class="text-muted">لا يمكن تغيير حالة الحساب إلا بواسطة المدير أو المسؤول</small>
                            </div>
                            {% endif %}

                            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                            <div class="mb-3">
                                <label for="bank_account" class="form-label">حساب التحصيل المالي</label>
                                <textarea class="form-control" id="bank_account" name="bank_account" rows="3">{{ employee.bank_account or '' }}</textarea>
                                <small class="text-muted">أدخل معلومات الحساب البنكي للموظف</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
