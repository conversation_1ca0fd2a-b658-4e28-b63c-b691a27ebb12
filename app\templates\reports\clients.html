{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير العملاء</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='clients') }}" method="POST">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="has_projects" class="form-label">المشاريع</label>
                                <select class="form-select" id="has_projects" name="has_projects">
                                    <option value="all" selected>الكل</option>
                                    <option value="with_projects">عملاء لديهم مشاريع</option>
                                    <option value="without_projects">عملاء بدون مشاريع</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="search" class="form-label">بحث</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="بحث بالاسم أو البريد الإلكتروني">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم العميل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>العنوان</th>
                                    <th>عدد المشاريع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in clients %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ client.name }}</td>
                                    <td>{{ client.email or 'غير محدد' }}</td>
                                    <td>{{ client.phone or 'غير محدد' }}</td>
                                    <td>{{ client.address or 'غير محدد' }}</td>
                                    <td>{{ client.projects|length }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#has_projects, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
    });
</script>
{% endblock %}
