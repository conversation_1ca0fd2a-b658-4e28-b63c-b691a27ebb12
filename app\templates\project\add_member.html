{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة أعضاء إلى مشروع {{ project.name }}</h1>
    <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لقائمة الأعضاء
    </a>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">اختر الموظفين لإضافتهم إلى المشروع</h5>
    </div>
    <div class="card-body">
        {% if available_users %}
        <form method="POST" action="{{ url_for('project.add_member', id=project.id) }}">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 50px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all">
                                </div>
                            </th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الصلاحيات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in available_users %}
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" type="checkbox" id="user_{{ user.id }}" name="user_ids" value="{{ user.id }}">
                                </div>
                            </td>
                            <td>{{ user.get_full_name() }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.department.name if user.department else 'غير محدد' }}</td>
                            <td>
                                {% for role in user.roles %}
                                <span class="badge bg-secondary me-1">{{ role.name }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة المحددين</button>
            </div>
        </form>
        {% else %}
        <div class="alert alert-info">
            لا يوجد موظفين متاحين للإضافة إلى هذا المشروع.
        </div>
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
            <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-secondary">العودة لقائمة الأعضاء</a>
        </div>
        {% endif %}
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all checkbox functionality
        const selectAllCheckbox = document.getElementById('select-all');
        const userCheckboxes = document.querySelectorAll('.user-checkbox');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                
                userCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = isChecked;
                });
            });
            
            // Update "select all" checkbox when individual checkboxes change
            userCheckboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    const allChecked = Array.from(userCheckboxes).every(function(cb) {
                        return cb.checked;
                    });
                    
                    const anyChecked = Array.from(userCheckboxes).some(function(cb) {
                        return cb.checked;
                    });
                    
                    selectAllCheckbox.checked = allChecked;
                    selectAllCheckbox.indeterminate = anyChecked && !allChecked;
                });
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
