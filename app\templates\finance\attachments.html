{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إدارة المرفقات</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إدارة المرفقات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">مرفقات المعاملات المالية</h5>
                </div>
                <div class="card-body">
                    {% if attachments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>نوع الملف</th>
                                    <th>حجم الملف</th>
                                    <th>تاريخ الرفع</th>
                                    <th>المعاملة المرتبطة</th>
                                    <th>تم الرفع بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attachment in attachments %}
                                <tr>
                                    <td>{{ attachment.filename }}</td>
                                    <td>{{ attachment.file_type or 'غير معروف' }}</td>
                                    <td>{{ (attachment.file_size / 1024)|round(2) }} KB</td>
                                    <td>{{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('finance.view_transaction', transaction_id=attachment.transaction_id) }}">
                                            عرض المعاملة
                                        </a>
                                    </td>
                                    <td>{{ attachment.uploaded_by.first_name }} {{ attachment.uploaded_by.last_name }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('static', filename=attachment.file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="{{ url_for('finance.download_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-download"></i> تنزيل
                                            </a>
                                            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.delete_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المرفق؟');">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        لا توجد مرفقات حالياً.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
