import os
import sqlite3
from app import create_app, db
from app.models import User, Role, Department, Client, Project, Task, Transaction, Invoice, Notification, ProjectMessage

# Delete the existing database
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Deleted existing database: {db_path}")

# Create a new empty database file
conn = sqlite3.connect(db_path)
conn.close()
print(f"Created new empty database file: {db_path}")

# Create the app
app = create_app()

# Create all tables
with app.app_context():
    db.create_all()
    print("Created all database tables")

    # Create default roles
    admin_role = Role(name='admin', description='المسؤول الرئيسي للنظام مع كامل الصلاحيات')
    manager_role = Role(name='manager', description='مدير مع صلاحيات إدارية واسعة')
    department_head_role = Role(name='department_head', description='رئيس قسم مع صلاحيات إدارة القسم')
    employee_role = Role(name='employee', description='موظف عادي')
    finance_role = Role(name='finance', description='موظف مالية مع صلاحيات إدارة المالية')
    sales_role = Role(name='sales', description='موظف مبيعات مع صلاحيات إدارة العملاء')

    db.session.add_all([admin_role, manager_role, department_head_role, employee_role, finance_role, sales_role])
    db.session.commit()
    print("Created default roles")

    # Create default admin user
    admin = User(
        username='GolDeN',
        email='<EMAIL>',
        first_name='مدير',
        last_name='النظام',
        is_active=True,
        education='',
        experience='',
        skills='',
        certifications='',
        languages=''
    )
    admin.set_password('GolDeN2252005')
    admin.roles.append(admin_role)

    db.session.add(admin)
    db.session.commit()
    print("Created default admin user (username: GolDeN, password: GolDeN2252005)")

    print("Database setup completed successfully!")

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
