from flask import Flask
from app import create_app, db
from app.models.project import Project
from datetime import datetime, timezone

app = create_app()

with app.app_context():
    # Get a project to test with
    project = Project.query.first()
    
    if project:
        print(f"Testing with project: {project.name} (ID: {project.id})")
        print(f"Current invoice_approval_date: {project.invoice_approval_date}")
        
        # Set a test date
        test_date = datetime.now(timezone.utc)
        print(f"Setting invoice_approval_date to: {test_date}")
        
        # Update the project
        project.invoice_approval_date = test_date
        db.session.commit()
        
        # Verify the update
        updated_project = Project.query.get(project.id)
        print(f"Updated invoice_approval_date: {updated_project.invoice_approval_date}")
        
        if updated_project.invoice_approval_date:
            print("SUCCESS: invoice_approval_date was updated successfully!")
        else:
            print("ERROR: invoice_approval_date was not updated.")
    else:
        print("No projects found in the database.")
