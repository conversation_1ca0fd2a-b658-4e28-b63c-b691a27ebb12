{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل التحديث</h1>
    <a href="{{ url_for('project.updates', id=project.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للتحديثات
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">تعديل تحديث للمشروع: {{ project.name }}</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('project.edit_update', update_id=update.id) }}" method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="title" name="title" value="{{ update.title }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">المحتوى</label>
                        <textarea class="form-control" id="content" name="content" rows="10" required>{{ update.content }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="links" class="form-label">الروابط (اختياري، افصل بين الروابط بفاصلة)</label>
                        <textarea class="form-control" id="links" name="links" rows="3" placeholder="https://example.com, https://another-example.com">{{ update.links }}</textarea>
                        <small class="form-text text-muted">يمكنك إضافة رابط واحد أو أكثر، مفصولة بفواصل</small>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_pinned" name="is_pinned" {% if update.is_pinned %}checked{% endif %}>
                        <label class="form-check-label" for="is_pinned">تثبيت هذا التحديث</label>
                        <small class="form-text text-muted d-block">التحديثات المثبتة تظهر في أعلى صفحة التحديثات.</small>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="send_notification" name="send_notification">
                        <label class="form-check-label" for="send_notification">إرسال إشعار لأعضاء المشروع</label>
                        <small class="form-text text-muted d-block">سيتم إرسال إشعار لجميع أعضاء المشروع عن هذا التحديث</small>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
