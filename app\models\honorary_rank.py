from datetime import datetime
from app import db

# Association table for user honorary ranks
UserHonoraryRank = db.Table('user_honorary_ranks',
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON><PERSON>('users.id'), primary_key=True),
    db.<PERSON>umn('honorary_rank_id', db.<PERSON>ger, db.<PERSON>('honorary_ranks.id'), primary_key=True),
    db.<PERSON>('assigned_at', db.DateTime, default=datetime.utcnow),
    db.<PERSON>umn('assigned_by_id', db.Integer, db.<PERSON>ey('users.id'))
)

class HonoraryRank(db.Model):
    __tablename__ = 'honorary_ranks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(20), default='#007bff')  # Default color (bootstrap primary)
    icon = db.Column(db.String(50), default='fa-award')  # Default icon
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_honorary_ranks')
    
    def __repr__(self):
        return f'<HonoraryRank {self.name}>'
