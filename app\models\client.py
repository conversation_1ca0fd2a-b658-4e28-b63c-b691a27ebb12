from datetime import datetime
from app import db

class Client(db.Model):
    __tablename__ = 'clients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    company = db.Column(db.String(100))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    address = db.Column(db.String(255))
    website = db.Column(db.String(255))
    notes = db.Column(db.Text)
    profile_image = db.Column(db.String(255), default='default.jpg')  # Path to image file (legacy support)
    profile_image_data = db.Column(db.LargeBinary)  # Binary image data
    profile_image_mime = db.Column(db.String(64))  # MIME type of the image
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Invoices for this client
    invoices = db.relationship('Invoice', backref='client', lazy='dynamic')

    def __repr__(self):
        return f'<Client {self.name}>'

    def get_total_projects(self):
        return len(self.projects)

    def get_active_projects(self):
        return [project for project in self.projects if project.status == 'in_progress']

    def get_total_spent(self):
        from app.models.finance import Invoice
        paid_invoices = Invoice.query.filter_by(client_id=self.id, status='paid').all()
        return sum(invoice.total_amount for invoice in paid_invoices)

    def get_outstanding_amount(self):
        from app.models.finance import Invoice
        unpaid_invoices = Invoice.query.filter_by(client_id=self.id, status='unpaid').all()
        return sum(invoice.total_amount for invoice in unpaid_invoices)

    def set_profile_image(self, image_data, mime_type):
        """
        تخزين بيانات الصورة في قاعدة البيانات

        Args:
            image_data: بيانات الصورة الثنائية
            mime_type: نوع الملف (مثل image/jpeg)
        """
        self.profile_image_data = image_data
        self.profile_image_mime = mime_type

    def get_profile_image_data(self):
        """
        استرجاع بيانات الصورة من قاعدة البيانات

        Returns:
            tuple: (بيانات الصورة الثنائية, نوع الملف) أو None إذا لم تكن هناك صورة
        """
        if self.profile_image_data and self.profile_image_mime:
            return (self.profile_image_data, self.profile_image_mime)
        return None

    def has_profile_image_in_db(self):
        """
        التحقق مما إذا كان العميل لديه صورة مخزنة في قاعدة البيانات

        Returns:
            bool: True إذا كان العميل لديه صورة مخزنة في قاعدة البيانات
        """
        return self.profile_image_data is not None and len(self.profile_image_data) > 0
