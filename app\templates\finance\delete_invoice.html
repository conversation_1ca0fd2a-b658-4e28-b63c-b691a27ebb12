{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف الفاتورة</h1>
    <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للفاتورة
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>

                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف الفاتورة التالية؟</h4>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ invoice.invoice_number }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>العميل:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ invoice.client.name if invoice.client else 'غير محدد' }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>المشروع:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ invoice.project.name if invoice.project else 'غير محدد' }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>تاريخ الإصدار:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ invoice.issue_date.strftime('%Y-%m-%d') }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>المبلغ الإجمالي:</strong>
                            </div>
                            <div class="col-md-9">
                                ${{ invoice.total_amount }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>الحالة:</strong>
                            </div>
                            <div class="col-md-9">
                                {% if invoice.status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوع</span>
                                {% elif invoice.status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                                {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-secondary">ملغي</span>
                                {% endif %}
                            </div>
                        </div>

                        {% if invoice.status == 'paid' and not current_user.has_role('admin') %}
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> هذه الفاتورة مدفوعة بالفعل.
                            <p class="mt-2 mb-0">فقط المسؤول يمكنه حذف الفواتير المدفوعة.</p>
                        </div>
                        {% elif invoice.status == 'paid' and current_user.has_role('admin') %}
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> أنت على وشك حذف فاتورة مدفوعة.
                            <p class="mt-2 mb-0">سيؤدي هذا إلى حذف المعاملات المالية المرتبطة بهذه الفاتورة أيضًا.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <form action="{{ url_for('finance.delete_invoice', id=invoice.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>

                        {% if invoice.status != 'paid' or current_user.has_role('admin') %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف الفاتورة
                        </button>
                        {% else %}
                        <button type="button" class="btn btn-danger" disabled>
                            <i class="fas fa-trash me-1"></i>حذف الفاتورة
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
