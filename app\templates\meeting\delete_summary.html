{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">حذف ملخص الاجتماع</h1>
        <div>
            <a href="{{ url_for('meeting.view', id=summary.meeting_id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى الاجتماع
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تأكيد الحذف</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">تحذير!</h5>
                        <p>هل أنت متأكد من رغبتك في حذف هذا الملخص؟ هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>

                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="card-subtitle mb-1 text-primary">{{ summary.created_by.get_full_name() }}</h6>
                                    <small class="text-muted">{{ summary.created_at.strftime('%Y-%m-%d %I:%M %p') }}</small>
                                </div>
                            </div>
                            <p class="card-text">{{ summary.content|nl2br|safe }}</p>
                        </div>
                    </div>

                    <form action="{{ url_for('meeting.delete_summary', summary_id=summary.id) }}" method="POST">
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>تأكيد الحذف
                            </button>
                            <a href="{{ url_for('meeting.view', id=summary.meeting_id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
