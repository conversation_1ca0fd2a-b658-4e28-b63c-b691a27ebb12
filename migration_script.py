# ملف الهجرة لإنشاء النظام المالي المتكامل
"""
Migration script to create enhanced accounting system
Run this after creating a new migration file with:
flask db revision -m "Create enhanced accounting system"
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

def upgrade():
    # 1. إنشاء جدول شجرة الحسابات
    op.create_table('chart_of_accounts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('account_code', sa.String(length=20), nullable=False),
        sa.Column('account_name', sa.String(length=255), nullable=False),
        sa.Column('account_type', sa.Enum('Asset', 'Liability', 'Equity', 'Revenue', 'Expense'), nullable=False),
        sa.Column('parent_account_id', sa.Integer(), nullable=True),
        sa.Column('balance', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['parent_account_id'], ['chart_of_accounts.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('account_code')
    )

    # 2. إنشاء جدول قيود اليومية
    op.create_table('journal_entries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('entry_number', sa.String(length=50), nullable=False),
        sa.Column('entry_date', sa.Date(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('source_document_type', sa.String(length=50), nullable=True),
        sa.Column('source_document_id', sa.Integer(), nullable=True),
        sa.Column('total_debit', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('total_credit', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('status', sa.Enum('draft', 'posted', 'cancelled'), nullable=True),
        sa.Column('created_by_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('entry_number')
    )

    # 3. إنشاء جدول بنود قيود اليومية
    op.create_table('journal_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('journal_entry_id', sa.Integer(), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('debit', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('credit', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['account_id'], ['chart_of_accounts.id'], ),
        sa.ForeignKeyConstraint(['journal_entry_id'], ['journal_entries.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    # 4. إنشاء جدول الموردين
    op.create_table('vendors',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('company', sa.String(length=255), nullable=True),
        sa.Column('email', sa.String(length=120), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('address', sa.Text(), nullable=True),
        sa.Column('tax_id', sa.String(length=50), nullable=True),
        sa.Column('payment_terms', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 5. إنشاء جدول فواتير الموردين
    op.create_table('vendor_bills',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('bill_number', sa.String(length=100), nullable=False),
        sa.Column('vendor_reference', sa.String(length=100), nullable=True),
        sa.Column('issue_date', sa.Date(), nullable=False),
        sa.Column('due_date', sa.Date(), nullable=False),
        sa.Column('subtotal', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('tax_amount', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('total_amount', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('amount_due', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('status', sa.Enum('draft', 'to_pay', 'paid', 'cancelled'), nullable=True),
        sa.Column('journal_entry_id', sa.Integer(), nullable=True),
        sa.Column('created_by_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['journal_entry_id'], ['journal_entries.id'], ),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # 6. إنشاء جدول بنود فواتير الموردين
    op.create_table('vendor_bill_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('bill_id', sa.Integer(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('quantity', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('unit_price', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('subtotal', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['account_id'], ['chart_of_accounts.id'], ),
        sa.ForeignKeyConstraint(['bill_id'], ['vendor_bills.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    # 7. إنشاء جدول الدفعات المستلمة
    op.create_table('payments_received',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('customer_id', sa.Integer(), nullable=False),
        sa.Column('invoice_id', sa.Integer(), nullable=True),
        sa.Column('payment_number', sa.String(length=50), nullable=False),
        sa.Column('payment_date', sa.Date(), nullable=False),
        sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('payment_method', sa.Enum('cash', 'bank_transfer', 'check', 'credit_card'), nullable=False),
        sa.Column('reference_number', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('journal_entry_id', sa.Integer(), nullable=True),
        sa.Column('created_by_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['customer_id'], ['clients.id'], ),
        sa.ForeignKeyConstraint(['invoice_id'], ['invoices.id'], ),
        sa.ForeignKeyConstraint(['journal_entry_id'], ['journal_entries.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('payment_number')
    )

    # 8. إنشاء جدول الدفعات المسددة
    op.create_table('payments_made',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('bill_id', sa.Integer(), nullable=True),
        sa.Column('payment_number', sa.String(length=50), nullable=False),
        sa.Column('payment_date', sa.Date(), nullable=False),
        sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('payment_method', sa.Enum('cash', 'bank_transfer', 'check'), nullable=False),
        sa.Column('reference_number', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('journal_entry_id', sa.Integer(), nullable=True),
        sa.Column('created_by_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['bill_id'], ['vendor_bills.id'], ),
        sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['journal_entry_id'], ['journal_entries.id'], ),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('payment_number')
    )

    # 9. تحديث جدول الفواتير الحالي
    with op.batch_alter_table('invoices', schema=None) as batch_op:
        batch_op.add_column(sa.Column('invoice_type', sa.Enum('sales', 'service'), nullable=True))
        batch_op.add_column(sa.Column('payment_terms', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('amount_due', sa.Numeric(precision=15, scale=2), nullable=True))
        batch_op.add_column(sa.Column('journal_entry_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('tax_amount', sa.Numeric(precision=15, scale=2), nullable=True))
        batch_op.add_column(sa.Column('discount_amount', sa.Numeric(precision=15, scale=2), nullable=True))
        batch_op.create_foreign_key('fk_invoices_journal_entry', 'journal_entries', ['journal_entry_id'], ['id'])

    # 10. تحديث جدول العملاء الحالي
    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.add_column(sa.Column('credit_limit', sa.Numeric(precision=15, scale=2), nullable=True))
        batch_op.add_column(sa.Column('payment_terms', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('tax_id', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('is_active', sa.Boolean(), nullable=True))

    # 11. إنشاء جدول السنوات المالية
    op.create_table('fiscal_years',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=False),
        sa.Column('is_current', sa.Boolean(), nullable=True),
        sa.Column('status', sa.Enum('open', 'closed'), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 12. إنشاء جدول معدلات الضرائب
    op.create_table('tax_rates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('rate', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    # حذف الجداول بالترتيب العكسي لتجنب مشاكل Foreign Keys
    op.drop_table('tax_rates')
    op.drop_table('fiscal_years')
    op.drop_table('payments_made')
    op.drop_table('payments_received')
    op.drop_table('vendor_bill_items')
    op.drop_table('vendor_bills')
    op.drop_table('vendors')
    op.drop_table('journal_items')
    op.drop_table('journal_entries')
    op.drop_table('chart_of_accounts')
    
    # إزالة الحقول المضافة للجداول الموجودة
    with op.batch_alter_table('invoices', schema=None) as batch_op:
        batch_op.drop_constraint('fk_invoices_journal_entry', type_='foreignkey')
        batch_op.drop_column('discount_amount')
        batch_op.drop_column('tax_amount')
        batch_op.drop_column('journal_entry_id')
        batch_op.drop_column('amount_due')
        batch_op.drop_column('payment_terms')
        batch_op.drop_column('invoice_type')
    
    with op.batch_alter_table('clients', schema=None) as batch_op:
        batch_op.drop_column('is_active')
        batch_op.drop_column('tax_id')
        batch_op.drop_column('payment_terms')
        batch_op.drop_column('credit_limit')


# ملف منفصل لإدراج البيانات الأساسية
def seed_initial_data():
    """
    إدراج البيانات الأساسية للنظام المالي
    يجب تشغيل هذا بعد تطبيق الهجرة
    """
    from app import db
    from enhanced_models import ChartOfAccounts, FiscalYear, TaxRate
    from datetime import date
    
    # 1. إنشاء شجرة الحسابات الأساسية
    accounts = [
        # الأصول (Assets)
        {'code': '1000', 'name': 'الأصول', 'type': 'Asset', 'parent': None},
        {'code': '1010', 'name': 'النقد في البنك', 'type': 'Asset', 'parent': '1000'},
        {'code': '1020', 'name': 'حسابات العملاء', 'type': 'Asset', 'parent': '1000'},
        {'code': '1030', 'name': 'المخزون', 'type': 'Asset', 'parent': '1000'},
        {'code': '1040', 'name': 'المعدات والأجهزة', 'type': 'Asset', 'parent': '1000'},
        
        # الخصوم (Liabilities)
        {'code': '2000', 'name': 'الخصوم', 'type': 'Liability', 'parent': None},
        {'code': '2010', 'name': 'حسابات الموردين', 'type': 'Liability', 'parent': '2000'},
        {'code': '2020', 'name': 'ضرائب مستحقة', 'type': 'Liability', 'parent': '2000'},
        {'code': '2030', 'name': 'قروض قصيرة الأجل', 'type': 'Liability', 'parent': '2000'},
        
        # حقوق الملكية (Equity)
        {'code': '3000', 'name': 'حقوق الملكية', 'type': 'Equity', 'parent': None},
        {'code': '3010', 'name': 'رأس المال', 'type': 'Equity', 'parent': '3000'},
        {'code': '3020', 'name': 'الأرباح المحتجزة', 'type': 'Equity', 'parent': '3000'},
        
        # الإيرادات (Revenue)
        {'code': '4000', 'name': 'الإيرادات', 'type': 'Revenue', 'parent': None},
        {'code': '4010', 'name': 'إيرادات خدمات التصميم', 'type': 'Revenue', 'parent': '4000'},
        {'code': '4020', 'name': 'إيرادات خدمات البرمجة', 'type': 'Revenue', 'parent': '4000'},
        {'code': '4030', 'name': 'إيرادات خدمات المونتاج', 'type': 'Revenue', 'parent': '4000'},
        {'code': '4040', 'name': 'إيرادات إدارة الحسابات', 'type': 'Revenue', 'parent': '4000'},
        
        # المصروفات (Expenses)
        {'code': '5000', 'name': 'المصروفات', 'type': 'Expense', 'parent': None},
        {'code': '5010', 'name': 'رواتب الموظفين', 'type': 'Expense', 'parent': '5000'},
        {'code': '5020', 'name': 'اشتراكات البرامج', 'type': 'Expense', 'parent': '5000'},
        {'code': '5030', 'name': 'إيجار المكتب', 'type': 'Expense', 'parent': '5000'},
        {'code': '5040', 'name': 'مصروفات التسويق', 'type': 'Expense', 'parent': '5000'},
        {'code': '5050', 'name': 'مصروفات الاتصالات', 'type': 'Expense', 'parent': '5000'},
        {'code': '5060', 'name': 'مصروفات الاستضافة', 'type': 'Expense', 'parent': '5000'},
    ]
    
    # إنشاء الحسابات
    account_map = {}
    for acc_data in accounts:
        parent_id = None
        if acc_data['parent']:
            parent_id = account_map.get(acc_data['parent'])
        
        account = ChartOfAccounts(
            account_code=acc_data['code'],
            account_name=acc_data['name'],
            account_type=acc_data['type'],
            parent_account_id=parent_id,
            balance=0,
            is_active=True
        )
        db.session.add(account)
        db.session.flush()
        account_map[acc_data['code']] = account.id
    
    # 2. إنشاء السنة المالية الحالية
    current_year = date.today().year
    fiscal_year = FiscalYear(
        name=str(current_year),
        start_date=date(current_year, 1, 1),
        end_date=date(current_year, 12, 31),
        is_current=True,
        status='open'
    )
    db.session.add(fiscal_year)
    
    # 3. إنشاء معدلات الضرائب الافتراضية
    tax_rates = [
        {'name': 'ضريبة القيمة المضافة', 'rate': 15.00},
        {'name': 'ضريبة الخدمات', 'rate': 5.00},
        {'name': 'معفى من الضريبة', 'rate': 0.00},
    ]
    
    for tax_data in tax_rates:
        tax_rate = TaxRate(
            name=tax_data['name'],
            rate=tax_data['rate'],
            is_active=True
        )
        db.session.add(tax_rate)
    
    db.session.commit()
    print("تم إدراج البيانات الأساسية بنجاح!")

if __name__ == '__main__':
    # تشغيل إدراج البيانات الأساسية
    seed_initial_data()
