{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل قسم</h1>
    <div>
        <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للتفاصيل
        </a>
        <a href="{{ url_for('department.index') }}" class="btn btn-secondary">
            <i class="fas fa-list me-1"></i>قائمة الأقسام
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">تعديل بيانات قسم {{ department.name }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('department.edit', id=department.id) }}">
            <div class="mb-3">
                <label for="name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" value="{{ department.name }}" required>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف القسم</label>
                <textarea class="form-control" id="description" name="description" rows="4">{{ department.description or '' }}</textarea>
            </div>
            
            <div class="mb-3">
                <label for="head_id" class="form-label">رئيس القسم</label>
                <select class="form-select" id="head_id" name="head_id">
                    <option value="">-- اختر رئيس القسم --</option>
                    {% for head in potential_heads %}
                    <option value="{{ head.id }}" {% if department.head_id == head.id %}selected{% endif %}>{{ head.get_full_name() }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
