import os
import base64
from io import Bytes<PERSON>
from PIL import Image
from flask import current_app
from app import db

def save_image_to_db(file, max_size=(800, 800)):
    """
    Save an image to the database as binary data
    
    Args:
        file: The uploaded file object
        max_size: Maximum dimensions (width, height) to resize the image to
        
    Returns:
        Binary data of the image
    """
    try:
        # Open the image using PIL
        img = Image.open(file)
        
        # Resize the image if it's larger than max_size
        if img.width > max_size[0] or img.height > max_size[1]:
            img.thumbnail(max_size, Image.LANCZOS)
        
        # Convert the image to binary data
        buffer = BytesIO()
        img_format = img.format if img.format else 'JPEG'
        img.save(buffer, format=img_format)
        img_data = buffer.getvalue()
        
        return img_data
    except Exception as e:
        current_app.logger.error(f"Error saving image to database: {e}")
        return None

def get_image_data_url(img_data, mime_type='image/jpeg'):
    """
    Convert binary image data to a data URL for display in HTML
    
    Args:
        img_data: Binary image data
        mime_type: MIME type of the image
        
    Returns:
        Data URL string
    """
    if not img_data:
        return None
    
    try:
        base64_data = base64.b64encode(img_data).decode('utf-8')
        return f"data:{mime_type};base64,{base64_data}"
    except Exception as e:
        current_app.logger.error(f"Error converting image to data URL: {e}")
        return None

def save_image_to_file(img_data, directory, filename):
    """
    Save binary image data to a file
    
    Args:
        img_data: Binary image data
        directory: Directory to save the file in
        filename: Name of the file
        
    Returns:
        Path to the saved file relative to the static directory
    """
    if not img_data:
        return None
    
    try:
        # Ensure directory exists
        os.makedirs(directory, exist_ok=True)
        
        # Save the image to a file
        file_path = os.path.join(directory, filename)
        with open(file_path, 'wb') as f:
            f.write(img_data)
        
        # Return the path relative to the static directory
        static_dir = os.path.join(current_app.root_path, 'static')
        rel_path = os.path.relpath(file_path, static_dir)
        
        return rel_path
    except Exception as e:
        current_app.logger.error(f"Error saving image to file: {e}")
        return None

def get_mime_type(img_data):
    """
    Determine the MIME type of an image from its binary data
    
    Args:
        img_data: Binary image data
        
    Returns:
        MIME type string
    """
    if not img_data:
        return None
    
    try:
        # Check for JPEG
        if img_data[0:3] == b'\xff\xd8\xff':
            return 'image/jpeg'
        # Check for PNG
        elif img_data[0:8] == b'\x89PNG\r\n\x1a\n':
            return 'image/png'
        # Check for GIF
        elif img_data[0:6] in (b'GIF87a', b'GIF89a'):
            return 'image/gif'
        # Default
        else:
            return 'application/octet-stream'
    except Exception as e:
        current_app.logger.error(f"Error determining MIME type: {e}")
        return 'application/octet-stream'
