{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>تعديل المعاملة المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.transactions') }}">المعاملات المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل المعاملة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تعديل المعاملة المالية</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('finance.edit_transaction', id=transaction.id) }}" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="{{ transaction.amount }}" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">نوع المعاملة</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="income" {% if transaction.transaction_type == 'income' %}selected{% endif %}>إيراد</option>
                                    <option value="expense" {% if transaction.transaction_type == 'expense' %}selected{% endif %}>مصروف</option>
                                </select>
                            </div>
                        </div>

                        <!-- Alternative Currency Section -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="alt_amount" class="form-label">المبلغ بعملة أخرى (اختياري)</label>
                                <input type="number" step="0.01" class="form-control" id="alt_amount" name="alt_amount" value="{{ transaction.alt_amount }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="currency_id" class="form-label">العملة</label>
                                <select class="form-select" id="currency_id" name="currency_id">
                                    <option value="">-- اختر العملة --</option>
                                    {% for currency in currencies %}
                                    <option value="{{ currency.id }}" {% if transaction.currency_id == currency.id %}selected{% endif %}>{{ currency.name }} ({{ currency.symbol }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <input type="text" class="form-control" id="category" name="category" value="{{ transaction.category }}" list="categories">
                                <datalist id="categories">
                                    <option value="رواتب">
                                    <option value="إيجار">
                                    <option value="مرافق">
                                    <option value="معدات">
                                    <option value="تسويق">
                                    <option value="خدمات">
                                    <option value="أخرى">
                                </datalist>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" value="{{ transaction.date.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="project_id" class="form-label">المشروع (اختياري)</label>
                                <select class="form-select" id="project_id" name="project_id">
                                    <option value="">-- بدون مشروع --</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}" {% if transaction.project_id == project.id %}selected{% endif %}>{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <input type="text" class="form-control" id="description" name="description" value="{{ transaction.description }}" required>
                            </div>
                        </div>

                        <!-- Supervisor and Recipient Section -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="supervisor_id" class="form-label">الموظف المشرف (اختياري)</label>
                                <select class="form-select" id="supervisor_id" name="supervisor_id">
                                    <option value="">-- اختر الموظف المشرف --</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}" {% if transaction.supervisor_id == user.id %}selected{% endif %}>{{ user.first_name }} {{ user.last_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="recipient" class="form-label">الجهة المرسل إليها (اختياري)</label>
                                <input type="text" class="form-control" id="recipient" name="recipient" value="{{ transaction.recipient }}">
                            </div>
                        </div>

                        <!-- Attachments Section -->
                        <div class="mb-3">
                            <label for="attachments" class="form-label">المرفقات (اختياري)</label>
                            <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                            <div class="form-text">يمكنك تحميل ملفات متعددة</div>

                            {% if transaction.attachments.count() > 0 %}
                            <div class="mt-2">
                                <p class="mb-1">المرفقات الحالية:</p>
                                <ul class="list-group">
                                    {% for attachment in transaction.attachments %}
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {{ attachment.filename }}
                                        <div class="btn-group">
                                            <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="{{ url_for('finance.download_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-download"></i> تنزيل
                                            </a>
                                        </div>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Verification Links Section -->
                        <div class="mb-3">
                            <label class="form-label">روابط الإثبات (اختياري)</label>
                            <div id="verification-links-container">
                                {% if transaction.verification_links.count() > 0 %}
                                    {% for link in transaction.verification_links %}
                                    <div class="verification-link-item mb-2 row">
                                        <div class="col-md-5">
                                            <input type="url" class="form-control" name="verification_links[]" value="{{ link.url }}" placeholder="أدخل الرابط هنا">
                                            <input type="hidden" name="verification_link_ids[]" value="{{ link.id }}">
                                        </div>
                                        <div class="col-md-5">
                                            <input type="text" class="form-control" name="verification_descriptions[]" value="{{ link.description }}" placeholder="وصف الرابط (اختياري)">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                <div class="verification-link-item mb-2 row">
                                    <div class="col-md-5">
                                        <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" id="add-link-btn">
                                <i class="fas fa-plus me-1"></i>إضافة رابط آخر
                            </button>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ transaction.notes }}</textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle verification links
        const addLinkBtn = document.getElementById('add-link-btn');
        const linksContainer = document.getElementById('verification-links-container');

        // Add new link field
        addLinkBtn.addEventListener('click', function() {
            const linkItem = document.createElement('div');
            linkItem.className = 'verification-link-item mb-2 row';
            linkItem.innerHTML = `
                <div class="col-md-5">
                    <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                </div>
            `;
            linksContainer.appendChild(linkItem);

            // Add event listener to the new remove button
            const removeBtn = linkItem.querySelector('.remove-link');
            removeBtn.addEventListener('click', function() {
                linksContainer.removeChild(linkItem);
            });
        });

        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-link').forEach(button => {
            button.addEventListener('click', function() {
                const linkItem = this.closest('.verification-link-item');
                linksContainer.removeChild(linkItem);
            });
        });
    });
</script>
{% endblock %}
