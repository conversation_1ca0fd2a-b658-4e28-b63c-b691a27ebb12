from app import db, create_app
app = create_app()
app.app_context().push()
from app.models.user import User

# Check the current status of the GolDeN account
user = User.query.filter_by(username='GolDeN').first()
if user:
    print(f"User found: {user.username}")
    print(f"Current status: {'Active' if user.is_active else 'Inactive'}")
    
    # Activate the account
    user.is_active = True
    db.session.commit()
    
    # Verify the change
    print(f"New status: {'Active' if user.is_active else 'Inactive'}")
    print("Account has been successfully activated!")
else:
    print("User with username 'GolDeN' not found.")
