{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إدارة المشاريع</h1>
    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
    <a href="{{ url_for('project.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>إضافة مشروع جديد
    </a>
    {% endif %}
</div>

<!-- Search and Filter Form -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">بحث وتصفية</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('project.index') }}" class="row g-3">
            <div class="col-md-5">
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث باسم المشروع أو الوصف" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% if search_query %}
                    <a href="{{ url_for('project.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                    <option value="all" {% if status_filter == 'all' or not status_filter %}selected{% endif %}>جميع المشاريع</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                    <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 مشروع</option>
                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 مشروع</option>
                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 مشروع</option>
                </select>
            </div>
        </form>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة المشاريع</h5>
    </div>
    <div class="card-body">
        {% if projects.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم المشروع</th>
                        <th>العميل</th>
                        <th>القسم</th>
                        <th>مدير المشروع</th>
                        <th>الحالة</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                        <th>التقدم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for project in projects.items %}
                    <tr class="project-item" data-status="{{ project.status }}">
                        <td>{{ project.name }}</td>
                        <td>{{ project.client.name if project.client else 'غير محدد' }}</td>
                        <td>{{ project.department.name if project.department else 'غير محدد' }}</td>
                        <td>{{ project.manager.get_full_name() if project.manager else 'غير محدد' }}</td>
                        <td>
                            {% if project.status == 'pending' %}
                            <span class="badge bg-secondary">معلق</span>
                            {% elif project.status == 'in_progress' %}
                            <span class="badge bg-primary">قيد التنفيذ</span>
                            {% elif project.status == 'completed' %}
                            <span class="badge bg-success">مكتمل</span>
                            {% elif project.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>{{ project.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                        <td>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar project-progress" role="progressbar"
                                     data-percentage="{{ project.get_completion_percentage() }}"
                                     style="width: {{ project.get_completion_percentage() }}%;"
                                     aria-valuenow="{{ project.get_completion_percentage() }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted">{{ project.get_completion_percentage() }}%</small>
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                    current_user.id == project.manager_id or
                                    (current_user.has_role('department_head') and current_user.managed_department and current_user.managed_department.id == project.department_id) %}
                                <a href="{{ url_for('project.edit', id=project.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                <a href="{{ url_for('project.delete', id=project.id) }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    عرض {{ projects.items|length }} من {{ projects.total }} مشروع
                    {% if search_query %}
                    <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                    {% endif %}
                    {% if status_filter and status_filter != 'all' %}
                    <span class="text-muted">
                        (الحالة:
                        {% if status_filter == 'pending' %}معلق{% endif %}
                        {% if status_filter == 'in_progress' %}قيد التنفيذ{% endif %}
                        {% if status_filter == 'completed' %}مكتمل{% endif %}
                        {% if status_filter == 'cancelled' %}ملغي{% endif %}
                        )
                    </span>
                    {% endif %}
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if projects.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('project.index', page=projects.prev_num, per_page=current_per_page, search=search_query, status=status_filter) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% set start_page = projects.page - 2 if projects.page > 2 else 1 %}
                        {% set end_page = start_page + 4 if start_page + 4 <= projects.pages else projects.pages %}
                        {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        <li class="page-item {% if page_num == projects.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('project.index', page=page_num, per_page=current_per_page, search=search_query, status=status_filter) }}">{{ page_num }}</a>
                        </li>
                        {% endfor %}

                        {% if projects.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('project.index', page=projects.next_num, per_page=current_per_page, search=search_query, status=status_filter) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query or status_filter %}
            لا توجد نتائج مطابقة للبحث.
            <a href="{{ url_for('project.index') }}" class="alert-link">عرض جميع المشاريع</a>
            {% else %}
            لا يوجد مشاريع حاليًا.
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
{% endblock %}
