{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>حذف المعاملة المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.transactions') }}">المعاملات المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حذف المعاملة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">تأكيد حذف المعاملة المالية</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد من رغبتك في حذف هذه المعاملة؟
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold">تفاصيل المعاملة</h6>
                            <p><strong>المبلغ:</strong> ${{ transaction.amount }}</p>
                            <p>
                                <strong>النوع:</strong>
                                {% if transaction.transaction_type == 'income' %}
                                <span class="badge bg-success">إيراد</span>
                                {% else %}
                                <span class="badge bg-danger">مصروف</span>
                                {% endif %}
                            </p>
                            <p><strong>الفئة:</strong> {{ transaction.category or 'غير محدد' }}</p>
                            <p><strong>التاريخ:</strong> {{ transaction.date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">معلومات إضافية</h6>
                            <p><strong>الوصف:</strong> {{ transaction.description }}</p>
                            <p><strong>المشروع:</strong> {{ transaction.project.name if transaction.project else 'غير مرتبط بمشروع' }}</p>
                            <p><strong>تاريخ التسجيل:</strong> {{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            <p><strong>سجل بواسطة:</strong> {{ transaction.recorded_by.username if transaction.recorded_by else 'غير معروف' }}</p>
                        </div>
                    </div>
                    
                    <form action="{{ url_for('finance.delete_transaction', id=transaction.id) }}" method="POST">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
