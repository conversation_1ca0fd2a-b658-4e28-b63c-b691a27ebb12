{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تعديل السيرة الذاتية</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للملف الشخصي
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card shadow">
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='img/' + employee.profile_image) }}" alt="{{ employee.get_full_name() }}" class="profile-image mb-3">
                <h4>{{ employee.get_full_name() }}</h4>
                <p class="text-muted">{{ employee.username }}</p>

                <div class="d-flex justify-content-center mb-3">
                    {% for role in employee.roles %}
                    <span class="badge bg-primary me-1">{{ role.name }}</span>
                    {% endfor %}
                </div>

                <p class="mb-1">
                    <i class="fas fa-envelope me-2"></i>{{ employee.email }}
                </p>
                {% if employee.department %}
                <p class="mb-1">
                    <i class="fas fa-building me-2"></i>{{ employee.department.name }}
                </p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">تعديل معلومات السيرة الذاتية</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('employee.edit_cv', id=employee.id) }}">
                    <div class="mb-4">
                        <label for="cv" class="form-label fw-bold">
                            <i class="fas fa-file-alt me-2"></i>السيرة الذاتية
                        </label>
                        <textarea class="form-control" id="cv" name="cv" rows="15" placeholder="أدخل السيرة الذاتية الكاملة هنا..."></textarea>
                        <small class="text-muted">يمكنك إدخال معلومات عن التعليم، الخبرات العملية، المهارات، الشهادات، اللغات وأي معلومات أخرى ذات صلة</small>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
