{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف المشروع</h1>
    <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لصفحة المشروع
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى حذف جميع المهام والملفات المرتبطة بالمشروع.
                </div>

                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف المشروع التالي؟</h4>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ project.name }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الوصف:</strong> {{ project.description }}</p>
                                <p><strong>العميل:</strong> {{ project.client.name if project.client else 'غير محدد' }}</p>
                                <p><strong>القسم:</strong> {{ project.department.name if project.department else 'غير محدد' }}</p>
                                <p><strong>مدير المشروع:</strong> {{ project.manager.get_full_name() if project.manager else 'غير محدد' }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>تاريخ البدء:</strong> {{ project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد' }}</p>
                                <p><strong>تاريخ الانتهاء:</strong> {{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</p>
                                <p><strong>الحالة:</strong>
                                    {% if project.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif project.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif project.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif project.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </p>
                                <p><strong>الميزانية:</strong> {{ project.budget|format_currency if project.budget else 'غير محدد' }}</p>
                            </div>
                        </div>

                        <div class="mt-3">
                            <p><strong>عدد المهام:</strong> {{ tasks_count }}</p>
                            <p><strong>عدد الملفات:</strong> {{ files_count }}</p>
                            <p><strong>عدد التحديثات:</strong> {{ updates_count }}</p>
                        </div>
                    </div>
                </div>

                <form action="{{ url_for('project.delete', id=project.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف المشروع
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
