{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إدارة الأقسام</h1>
    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
    <a href="{{ url_for('department.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>إضافة قسم جديد
    </a>
    {% endif %}
</div>

<div class="row">
    {% if departments %}
    {% for department in departments %}
    <div class="col-md-4 mb-4">
        <div class="card shadow department-card">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ department.name }}</h5>
            </div>
            <div class="card-body">
                <p class="card-text">{{ department.description or 'لا يوجد وصف متاح.' }}</p>
                
                <div class="d-flex justify-content-between mb-3">
                    <div>
                        <span class="badge bg-info">{{ department.get_member_count() }} موظف</span>
                    </div>
                    <div>
                        <span class="badge bg-primary">{{ department.get_active_projects_count() }} مشروع نشط</span>
                    </div>
                </div>
                
                <p class="mb-1">
                    <strong>رئيس القسم:</strong> 
                    {% if department.head %}
                    <a href="{{ url_for('employee.view', id=department.head.id) }}">{{ department.head.get_full_name() }}</a>
                    {% else %}
                    <span class="text-muted">غير معين</span>
                    {% endif %}
                </p>
                
                <p class="mb-3">
                    <strong>تاريخ الإنشاء:</strong> {{ department.created_at.strftime('%Y-%m-%d') }}
                </p>
                
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض
                    </a>
                    <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-secondary">
                        <i class="fas fa-users me-1"></i>الأعضاء
                    </a>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <a href="{{ url_for('department.edit', id=department.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-12">
        <div class="alert alert-info">
            لا يوجد أقسام حاليًا.
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
