{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف ملف</h1>
    <a href="{{ url_for('project.files', id=project_file.project_id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لقائمة الملفات
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>

                <h4 class="mb-3">
                    {% if project_file.file_type == "links" %}
                    هل أنت متأكد من رغبتك في حذف الروابط التالية؟
                    {% else %}
                    هل أنت متأكد من رغبتك في حذف الملف التالي؟
                    {% endif %}
                </h4>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ project_file.filename }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>المشروع:</strong> {{ project_file.project.name }}</p>
                                <p><strong>نوع الملف:</strong> {{ project_file.file_type or 'غير معروف' }}</p>
                                <p><strong>حجم الملف:</strong> {{ (project_file.file_size / 1024)|round(2) }} KB</p>
                                {% if project_file.notes %}
                                <p><strong>ملاحظات:</strong> {{ project_file.notes }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <p><strong>تاريخ الرفع:</strong> {{ project_file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                <p><strong>تم الرفع بواسطة:</strong> {{ project_file.uploaded_by.get_full_name() if project_file.uploaded_by else 'غير معروف' }}</p>
                                {% if project_file.filepath %}
                                <p>
                                    <strong>معاينة:</strong>
                                    <a href="{{ url_for('project.download_file', file_id=project_file.id) }}">
                                        {{ project_file.filename }}
                                    </a>
                                </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <form action="{{ url_for('project.delete_file', file_id=project_file.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('project.files', id=project_file.project_id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>
                            {% if project_file.file_type == "links" %}
                            حذف الروابط
                            {% else %}
                            حذف الملف
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
