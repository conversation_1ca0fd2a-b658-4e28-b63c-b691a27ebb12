import sqlite3
import os

# Get the path to the database file
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
print(f"Database path: {db_path}")

# Connect to the SQLite database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Get list of tables
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print("Tables in the database:")
for table in tables:
    print(f"- {table[0]}")

# Check if projects table exists
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='projects';")
if cursor.fetchone():
    # Get columns in projects table
    cursor.execute("PRAGMA table_info(projects)")
    columns = cursor.fetchall()
    print("\nColumns in projects table:")
    for column in columns:
        print(f"- {column[1]} ({column[2]})")

    # Try to add the column if it doesn't exist
    if 'invoice_approval_date' not in [col[1] for col in columns]:
        try:
            cursor.execute("ALTER TABLE projects ADD COLUMN invoice_approval_date DATETIME")
            conn.commit()
            print("\nAdded invoice_approval_date column to projects table")
        except sqlite3.OperationalError as e:
            print(f"\nError adding column: {e}")
    else:
        print("\ninvoice_approval_date column already exists")
else:
    print("\nThe 'projects' table does not exist.")

conn.close()
