{% extends 'base.html' %}

{% block styles %}
{% endblock %}

{% block content %}
<h1 class="mb-4">لوحة التحكم</h1>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-md-12 mb-4">
        <div class="row">
            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <!-- Admin/Manager Statistics -->
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المشاريع</h6>
                                <h2 class="mb-0">{{ stats.total_projects }}</h2>
                            </div>
                            <i class="fas fa-project-diagram fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">المشاريع النشطة</h6>
                                <h2 class="mb-0">{{ stats.active_projects }}</h2>
                            </div>
                            <i class="fas fa-tasks fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي العملاء</h6>
                                <h2 class="mb-0">{{ stats.total_clients }}</h2>
                            </div>
                            <i class="fas fa-user-tie fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">المهام المعلقة</h6>
                                <h2 class="mb-0">{{ stats.pending_tasks }}</h2>
                            </div>
                            <i class="fas fa-clipboard-list fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            {% if current_user.has_role('admin') or current_user.has_role('finance') %}
            <div class="col-md-4 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي الدخل</h6>
                                <h2 class="mb-0">${{ stats.total_income|round(2) }}</h2>
                            </div>
                            <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المصروفات</h6>
                                <h2 class="mb-0">${{ stats.total_expenses|round(2) }}</h2>
                            </div>
                            <i class="fas fa-file-invoice-dollar fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-dark text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">الرصيد الحالي</h6>
                                <h2 class="mb-0">${{ stats.balance|round(2) }}</h2>
                            </div>
                            <i class="fas fa-wallet fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% else %}
            <!-- Regular Employee Statistics -->
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">مشاريعي</h6>
                                <h2 class="mb-0">{{ stats.my_projects }}</h2>
                            </div>
                            <i class="fas fa-project-diagram fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">المشاريع النشطة</h6>
                                <h2 class="mb-0">{{ stats.my_active_projects }}</h2>
                            </div>
                            <i class="fas fa-tasks fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المهام</h6>
                                <h2 class="mb-0">{{ stats.my_tasks }}</h2>
                            </div>
                            <i class="fas fa-clipboard-list fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">المهام المعلقة</h6>
                                <h2 class="mb-0">{{ stats.my_pending_tasks }}</h2>
                            </div>
                            <i class="fas fa-clipboard-check fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-8">
        <!-- Recent Projects -->
        <div class="card mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-project-diagram me-2"></i>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    أحدث المشاريع
                    {% else %}
                    مشاريعي الحالية
                    {% endif %}
                </h5>
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#projectsFilterCollapse" aria-expanded="false" aria-controls="projectsFilterCollapse">
                    <i class="fas fa-filter me-1"></i>تصفية
                </button>
                {% endif %}
            </div>

            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <!-- Projects Filter -->
            <div class="collapse" id="projectsFilterCollapse">
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ url_for('dashboard.index') }}" class="row g-3">
                        <!-- Keep existing client filters -->
                        <input type="hidden" name="clients_page" value="{{ request.args.get('clients_page', 1) }}">
                        <input type="hidden" name="clients_per_page" value="{{ clients_per_page }}">

                        <div class="col-md-6">
                            <label for="projects_status" class="form-label">الحالة</label>
                            <select class="form-select" id="projects_status" name="projects_status">
                                <option value="all" {% if projects_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                <option value="pending" {% if projects_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="in_progress" {% if projects_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                <option value="completed" {% if projects_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                <option value="cancelled" {% if projects_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="projects_per_page" class="form-label">عدد النتائج</label>
                            <select class="form-select" id="projects_per_page" name="projects_per_page">
                                <option value="5" {% if projects_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if projects_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="15" {% if projects_per_page == 15 %}selected{% endif %}>15</option>
                                <option value="20" {% if projects_per_page == 20 %}selected{% endif %}>20</option>
                            </select>
                        </div>
                        <div class="col-12 d-flex">
                            <button type="submit" class="btn btn-primary">تطبيق</button>
                            <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <div class="card-body">
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    {% if recent_projects.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المشروع</th>
                                    <th>الحالة</th>
                                    <th>الأولوية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in recent_projects.items %}
                                <tr>
                                    <td>{{ project.name }}</td>
                                    <td>
                                        {% if project.status == 'pending' %}
                                        <span class="badge bg-secondary">معلق</span>
                                        {% elif project.status == 'in_progress' %}
                                        <span class="badge bg-primary">قيد التنفيذ</span>
                                        {% elif project.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif project.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if project.priority == 'low' %}
                                        <span class="badge bg-info">منخفضة</span>
                                        {% elif project.priority == 'medium' %}
                                        <span class="badge bg-warning">متوسطة</span>
                                        {% elif project.priority == 'high' %}
                                        <span class="badge bg-danger">عالية</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Projects Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_projects.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', projects_page=recent_projects.prev_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, clients_page=recent_clients.page, clients_per_page=clients_per_page) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_projects.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_projects.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('dashboard.index', projects_page=page_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, clients_page=recent_clients.page, clients_per_page=clients_per_page) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_projects.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', projects_page=recent_projects.next_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, clients_page=recent_clients.page, clients_per_page=clients_per_page) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد مشاريع حالية.</p>
                    {% endif %}
                {% else %}
                    <!-- Employee Projects Section -->
                    <div class="card-header bg-light d-flex justify-content-between align-items-center p-0 mb-3">
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#employeeProjectsFilterCollapse" aria-expanded="false" aria-controls="employeeProjectsFilterCollapse">
                            <i class="fas fa-filter me-1"></i>تصفية
                        </button>
                    </div>

                    <!-- Employee Projects Filter -->
                    <div class="collapse" id="employeeProjectsFilterCollapse">
                        <div class="card-body border-bottom">
                            <form method="GET" action="{{ url_for('dashboard.index') }}" class="row g-3">
                                <!-- Keep existing task filters -->
                                <input type="hidden" name="tasks_page" value="{{ request.args.get('tasks_page', 1) }}">
                                <input type="hidden" name="tasks_per_page" value="{{ tasks_per_page }}">
                                <input type="hidden" name="tasks_status" value="{{ tasks_status_filter }}">
                                <input type="hidden" name="tasks_priority" value="{{ tasks_priority_filter }}">

                                <div class="col-md-6">
                                    <label for="projects_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="projects_status" name="projects_status">
                                        <option value="all" {% if projects_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                        <option value="pending" {% if projects_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                        <option value="in_progress" {% if projects_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                        <option value="completed" {% if projects_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                        <option value="cancelled" {% if projects_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="projects_per_page" class="form-label">عدد النتائج</label>
                                    <select class="form-select" id="projects_per_page" name="projects_per_page">
                                        <option value="5" {% if projects_per_page == 5 %}selected{% endif %}>5</option>
                                        <option value="10" {% if projects_per_page == 10 %}selected{% endif %}>10</option>
                                        <option value="15" {% if projects_per_page == 15 %}selected{% endif %}>15</option>
                                        <option value="20" {% if projects_per_page == 20 %}selected{% endif %}>20</option>
                                    </select>
                                </div>
                                <div class="col-12 d-flex">
                                    <button type="submit" class="btn btn-primary">تطبيق</button>
                                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                                </div>
                            </form>
                        </div>
                    </div>

                    {% if recent_projects.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم المشروع</th>
                                    <th>الحالة</th>
                                    <th>الأولوية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in recent_projects.items %}
                                <tr>
                                    <td>{{ project.name }}</td>
                                    <td>
                                        {% if project.status == 'pending' %}
                                        <span class="badge bg-secondary">معلق</span>
                                        {% elif project.status == 'in_progress' %}
                                        <span class="badge bg-primary">قيد التنفيذ</span>
                                        {% elif project.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif project.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if project.priority == 'low' %}
                                        <span class="badge bg-info">منخفضة</span>
                                        {% elif project.priority == 'medium' %}
                                        <span class="badge bg-warning">متوسطة</span>
                                        {% elif project.priority == 'high' %}
                                        <span class="badge bg-danger">عالية</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Employee Projects Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_projects.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', projects_page=recent_projects.prev_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=recent_tasks.page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_projects.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_projects.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('dashboard.index', projects_page=page_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=recent_tasks.page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_projects.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', projects_page=recent_projects.next_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=recent_tasks.page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد مشاريع حالية.</p>
                    {% endif %}
                {% endif %}
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('project.index') }}" class="btn btn-primary btn-sm">عرض جميع المشاريع</a>
            </div>
        </div>

        <!-- Recent Tasks or Clients -->
        <div class="card mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <i class="fas fa-user-tie me-2"></i>أحدث العملاء
                    {% else %}
                    <i class="fas fa-tasks me-2"></i>مهامي الحالية
                    {% endif %}
                </h5>
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#clientsFilterCollapse" aria-expanded="false" aria-controls="clientsFilterCollapse">
                    <i class="fas fa-filter me-1"></i>تصفية
                </button>
                {% endif %}
            </div>

            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <!-- Clients Filter -->
            <div class="collapse" id="clientsFilterCollapse">
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ url_for('dashboard.index') }}" class="row g-3">
                        <!-- Keep existing project filters -->
                        <input type="hidden" name="projects_page" value="{{ request.args.get('projects_page', 1) }}">
                        <input type="hidden" name="projects_per_page" value="{{ projects_per_page }}">
                        <input type="hidden" name="projects_status" value="{{ projects_status_filter }}">

                        <div class="col-md-12">
                            <label for="clients_per_page" class="form-label">عدد النتائج</label>
                            <select class="form-select" id="clients_per_page" name="clients_per_page">
                                <option value="5" {% if clients_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if clients_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="15" {% if clients_per_page == 15 %}selected{% endif %}>15</option>
                                <option value="20" {% if clients_per_page == 20 %}selected{% endif %}>20</option>
                            </select>
                        </div>
                        <div class="col-12 d-flex">
                            <button type="submit" class="btn btn-primary">تطبيق</button>
                            <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <div class="card-body">
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    {% if recent_clients.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>الشركة</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in recent_clients.items %}
                                <tr>
                                    <td>{{ client.name }}</td>
                                    <td>{{ client.company or 'غير محدد' }}</td>
                                    <td>{{ client.email or 'غير محدد' }}</td>
                                    <td>{{ client.phone or 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Clients Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_clients.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', clients_page=recent_clients.prev_num, clients_per_page=clients_per_page, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_clients.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_clients.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('dashboard.index', clients_page=page_num, clients_per_page=clients_per_page, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_clients.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', clients_page=recent_clients.next_num, clients_per_page=clients_per_page, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا يوجد عملاء حاليين.</p>
                    {% endif %}
                {% else %}
                    <!-- Employee Tasks Section -->
                    <div class="card-header bg-light d-flex justify-content-between align-items-center p-0 mb-3">
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#employeeTasksFilterCollapse" aria-expanded="false" aria-controls="employeeTasksFilterCollapse">
                            <i class="fas fa-filter me-1"></i>تصفية
                        </button>
                    </div>

                    <!-- Employee Tasks Filter -->
                    <div class="collapse" id="employeeTasksFilterCollapse">
                        <div class="card-body border-bottom">
                            <form method="GET" action="{{ url_for('dashboard.index') }}" class="row g-3">
                                <!-- Keep existing project filters -->
                                <input type="hidden" name="projects_page" value="{{ request.args.get('projects_page', 1) }}">
                                <input type="hidden" name="projects_per_page" value="{{ projects_per_page }}">
                                <input type="hidden" name="projects_status" value="{{ projects_status_filter }}">

                                <div class="col-md-4">
                                    <label for="tasks_status" class="form-label">الحالة</label>
                                    <select class="form-select" id="tasks_status" name="tasks_status">
                                        <option value="all" {% if tasks_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                        <option value="pending" {% if tasks_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                        <option value="in_progress" {% if tasks_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                        <option value="completed" {% if tasks_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                        <option value="cancelled" {% if tasks_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="tasks_priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="tasks_priority" name="tasks_priority">
                                        <option value="all" {% if tasks_priority_filter == 'all' %}selected{% endif %}>الكل</option>
                                        <option value="low" {% if tasks_priority_filter == 'low' %}selected{% endif %}>منخفضة</option>
                                        <option value="medium" {% if tasks_priority_filter == 'medium' %}selected{% endif %}>متوسطة</option>
                                        <option value="high" {% if tasks_priority_filter == 'high' %}selected{% endif %}>عالية</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="tasks_per_page" class="form-label">عدد النتائج</label>
                                    <select class="form-select" id="tasks_per_page" name="tasks_per_page">
                                        <option value="5" {% if tasks_per_page == 5 %}selected{% endif %}>5</option>
                                        <option value="10" {% if tasks_per_page == 10 %}selected{% endif %}>10</option>
                                        <option value="15" {% if tasks_per_page == 15 %}selected{% endif %}>15</option>
                                        <option value="20" {% if tasks_per_page == 20 %}selected{% endif %}>20</option>
                                    </select>
                                </div>
                                <div class="col-12 d-flex">
                                    <button type="submit" class="btn btn-primary">تطبيق</button>
                                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                                </div>
                            </form>
                        </div>
                    </div>

                    {% if recent_tasks.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>عنوان المهمة</th>
                                    <th>المشروع</th>
                                    <th>الحالة</th>
                                    <th>الأولوية</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in recent_tasks.items %}
                                <tr>
                                    <td>{{ task.title }}</td>
                                    <td>{{ task.project.name }}</td>
                                    <td>
                                        {% if task.status == 'pending' %}
                                        <span class="badge bg-secondary">معلق</span>
                                        {% elif task.status == 'in_progress' %}
                                        <span class="badge bg-primary">قيد التنفيذ</span>
                                        {% elif task.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                        {% elif task.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.priority == 'low' %}
                                        <span class="badge bg-info">منخفضة</span>
                                        {% elif task.priority == 'medium' %}
                                        <span class="badge bg-warning text-dark">متوسطة</span>
                                        {% elif task.priority == 'high' %}
                                        <span class="badge bg-danger">عالية</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}</td>
                                    <td>
                                        <a href="{{ url_for('project.tasks', id=task.project_id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Employee Tasks Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_tasks.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', tasks_page=recent_tasks.prev_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_tasks.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_tasks.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('dashboard.index', tasks_page=page_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_tasks.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.index', tasks_page=recent_tasks.next_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=recent_projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد مهام حالية.</p>
                    {% endif %}
                {% endif %}
            </div>
            <div class="card-footer text-end">
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <a href="{{ url_for('client.index') }}" class="btn btn-primary btn-sm">عرض جميع العملاء</a>
                {% else %}
                <a href="{{ url_for('project.index') }}" class="btn btn-primary btn-sm">عرض جميع المهام</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Notifications -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i>الإشعارات الجديدة
                </h5>
            </div>
            <div class="card-body p-0">
                {% if notifications %}
                <ul class="list-group list-group-flush">
                    {% for notification in notifications %}
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ notification.title }}</h6>
                                <p class="mb-1 text-muted small">{{ notification.message }}</p>
                                <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <form action="{{ url_for('notification.mark_read', id=notification.id) }}" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-check"></i>
                                </button>
                            </form>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <div class="p-3">
                    <p class="text-muted mb-0">لا توجد إشعارات جديدة.</p>
                </div>
                {% endif %}
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('notification.index') }}" class="btn btn-primary btn-sm">عرض جميع الإشعارات</a>
            </div>
        </div>



        <!-- Quick Links -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i>روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row dashboard-quick-links">
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('project.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-project-diagram me-1"></i>المشاريع
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('employee.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users me-1"></i>الموظفين
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('calendar.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-calendar-alt me-1"></i>التقويم
                        </a>
                    </div>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('sales') or current_user.has_role('finance') %}
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('client.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-tie me-1"></i>العملاء
                        </a>
                    </div>
                    {% endif %}
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('finance.invoices') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-file-invoice-dollar me-1"></i>الفواتير
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('employee.view', id=current_user.id) }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-circle me-1"></i>بيانات الموظف
                        </a>
                    </div>
                    {% if current_user.has_role('admin') %}
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('department.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-building me-1"></i>الأقسام
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('system.config') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-cogs me-1"></i>إعدادات النظام
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}