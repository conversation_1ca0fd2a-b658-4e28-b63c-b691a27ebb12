{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>عرض المعاملة المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.transactions') }}">المعاملات المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">عرض المعاملة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل المعاملة</h5>
                    <div>
                        <a href="{{ url_for('finance.edit_transaction', id=transaction.id) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        {% if current_user.has_role('admin') %}
                        <a href="{{ url_for('finance.delete_transaction', id=transaction.id) }}" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> حذف
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <p class="mb-1 text-muted">المبلغ:</p>
                                <h4 class="mb-0">{{ transaction.amount }} $</h4>
                            </div>

                            {% if transaction.alt_amount and transaction.currency %}
                            <div class="mb-4">
                                <p class="mb-1 text-muted">المبلغ بعملة أخرى:</p>
                                <h4 class="mb-0">{{ transaction.alt_amount }} {{ transaction.currency.symbol }}</h4>
                                <small class="text-muted">({{ transaction.currency.name }})</small>
                            </div>
                            {% endif %}

                            <div class="mb-4">
                                <p class="mb-1 text-muted">نوع المعاملة:</p>
                                <p class="mb-0">
                                    {% if transaction.transaction_type == 'income' %}
                                    <span class="badge bg-success">إيراد</span>
                                    {% else %}
                                    <span class="badge bg-danger">مصروف</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="mb-4">
                                <p class="mb-1 text-muted">الفئة:</p>
                                <p class="mb-0">{{ transaction.category or 'غير محدد' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <p class="mb-1 text-muted">التاريخ:</p>
                                <p class="mb-0">{{ transaction.date.strftime('%Y-%m-%d') }}</p>
                            </div>
                            <div class="mb-4">
                                <p class="mb-1 text-muted">تم التسجيل بواسطة:</p>
                                <p class="mb-0">{{ transaction.recorded_by.first_name }} {{ transaction.recorded_by.last_name }}</p>
                            </div>

                            {% if transaction.supervisor %}
                            <div class="mb-4">
                                <p class="mb-1 text-muted">الموظف المشرف:</p>
                                <p class="mb-0">{{ transaction.supervisor.first_name }} {{ transaction.supervisor.last_name }}</p>
                            </div>
                            {% endif %}

                            {% if transaction.recipient %}
                            <div class="mb-4">
                                <p class="mb-1 text-muted">الجهة المرسل إليها:</p>
                                <p class="mb-0">{{ transaction.recipient }}</p>
                            </div>
                            {% endif %}

                            {% if transaction.project %}
                            <div class="mb-4">
                                <p class="mb-1 text-muted">المشروع:</p>
                                <p class="mb-0">{{ transaction.project.name }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-4">
                                <p class="mb-1 text-muted">الوصف:</p>
                                <p class="mb-0">{{ transaction.description }}</p>
                            </div>
                            {% if transaction.notes %}
                            <div class="mb-4">
                                <p class="mb-1 text-muted">ملاحظات:</p>
                                <p class="mb-0">{{ transaction.notes }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Attachments Section -->
                    {% if transaction.attachments.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="mb-3">المرفقات</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم الملف</th>
                                            <th>نوع الملف</th>
                                            <th>حجم الملف</th>
                                            <th>تاريخ الرفع</th>
                                            <th>تم الرفع بواسطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for attachment in transaction.attachments %}
                                        <tr>
                                            <td>{{ attachment.filename }}</td>
                                            <td>{{ attachment.file_type or 'غير معروف' }}</td>
                                            <td>{{ (attachment.file_size / 1024)|round(2) }} KB</td>
                                            <td>{{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ attachment.uploaded_by.first_name }} {{ attachment.uploaded_by.last_name }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <a href="{{ url_for('finance.download_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i> تنزيل
                                                    </a>
                                                    {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                                    <a href="{{ url_for('finance.delete_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المرفق؟');">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Verification Links Section -->
                    {% if transaction.verification_links.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5 class="mb-3">روابط الإثبات</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرابط</th>
                                            <th>الوصف</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>تمت الإضافة بواسطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for link in transaction.verification_links %}
                                        <tr>
                                            <td>{{ link.url }}</td>
                                            <td>{{ link.description or 'بدون وصف' }}</td>
                                            <td>{{ link.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ link.added_by.first_name }} {{ link.added_by.last_name }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ link.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-external-link-alt"></i> فتح الرابط
                                                    </a>
                                                    {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                                    <a href="{{ url_for('finance.delete_transaction_verification_link', link_id=link.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الرابط؟');">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
