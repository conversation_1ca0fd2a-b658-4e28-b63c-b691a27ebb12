from datetime import datetime
from app import db

class ActivityLog(db.Model):
    __tablename__ = 'activity_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'))
    user = db.relationship('User', backref='activity_logs')
    
    action = db.Column(db.String(50), nullable=False)  # create, update, delete
    entity_type = db.Column(db.String(50), nullable=False)  # user, project, task, etc.
    entity_id = db.Column(db.Integer)
    description = db.Column(db.Text, nullable=False)
    details = db.Column(db.Text)  # JSON string with detailed changes
    ip_address = db.Column(db.String(50))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<ActivityLog {self.action} {self.entity_type} {self.entity_id}>'
