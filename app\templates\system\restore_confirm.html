{% extends 'base.html' %}

{% block title %}تأكيد استعادة النسخة الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">تأكيد استعادة النسخة الاحتياطية</h1>
        <a href="{{ url_for('restore.restore_page') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى صفحة الاستعادة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-warning text-dark">
                    <h6 class="m-0 font-weight-bold">تأكيد استعادة النسخة الاحتياطية</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيؤدي استعادة النسخة الاحتياطية إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <p>أنت على وشك استعادة النسخة الاحتياطية التالية:</p>
                    <ul>
                        <li><strong>اسم الملف:</strong> {{ filename }}</li>
                        <li><strong>حجم الملف:</strong> {{ filesize }}</li>
                    </ul>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يرجى التأكد من أن النسخة الاحتياطية صحيحة وأنك ترغب في استعادتها. سيتم استبدال جميع البيانات الحالية.
                    </div>

                    <form action="{{ url_for('restore.restore_confirmed') }}" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="temp_file_path" value="{{ temp_file_path }}">

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('restore.restore_page') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-check me-1"></i>تأكيد استعادة النسخة الاحتياطية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
