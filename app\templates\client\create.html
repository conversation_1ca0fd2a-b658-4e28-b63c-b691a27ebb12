{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إضافة عميل جديد</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('client.index') }}">العملاء</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إضافة عميل جديد</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات العميل</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('client.create') }}" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العميل / الشركة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="contact_person" class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="website" name="website" placeholder="https://">
                        </div>

                        <div class="mb-3">
                            <label for="industry" class="form-label">المجال / الصناعة</label>
                            <input type="text" class="form-control" id="industry" name="industry">
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="profile_image" class="form-label">الصورة الشخصية / شعار الشركة</label>
                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                            <small class="text-muted">اختر صورة شخصية للعميل أو شعار الشركة (اختياري)</small>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('client.index') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">إضافة العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
