from app import db
from datetime import datetime, date

class CalendarEvent:
    """
    Clase para representar eventos en el calendario.
    Esta clase no es un modelo de base de datos, sino una clase de utilidad
    para representar eventos en el calendario.
    """

    # Tipos de eventos
    TYPE_PROJECT = 'project'
    TYPE_TASK = 'task'
    TYPE_LEAVE = 'leave'
    TYPE_MEETING = 'meeting'

    def __init__(self, id, title, start_date, end_date=None, event_type=None, description=None, url=None, all_day=False):
        """
        Inicializa un nuevo evento de calendario.

        Args:
            id: ID único del evento
            title: Título del evento
            start_date: Fecha de inicio del evento
            end_date: Fecha de fin del evento (opcional)
            event_type: Tipo de evento (project, task, leave, meeting)
            description: Descripción del evento (opcional)
            url: URL para ver más detalles del evento (opcional)
            all_day: Si el evento dura todo el día (opcional)
        """
        self.id = id
        self.title = title
        self.start_date = start_date
        self.end_date = end_date or start_date
        self.event_type = event_type
        self.description = description
        self.url = url
        self.all_day = all_day

        # Establecer color según el tipo de evento
        if event_type == self.TYPE_PROJECT:
            self.color = '#1765A0'  # Azul (color de la empresa)
        elif event_type == self.TYPE_TASK:
            self.color = '#EABF54'  # Dorado (color de la empresa)
        elif event_type == self.TYPE_LEAVE:
            self.color = '#28a745'  # Verde
        elif event_type == self.TYPE_MEETING:
            self.color = '#dc3545'  # Rojo
        else:
            self.color = '#6c757d'  # Gris

    def to_dict(self):
        """
        Convierte el evento a un diccionario para ser serializado a JSON.
        """
        return {
            'id': self.id,
            'title': self.title,
            'start': self.start_date.isoformat() if hasattr(self.start_date, 'isoformat') else self.start_date,
            'end': self.end_date.isoformat() if hasattr(self.end_date, 'isoformat') else self.end_date,
            'allDay': self.all_day,
            'url': self.url,
            'backgroundColor': self.color,
            'borderColor': self.color,
            'extendedProps': {
                'type': self.event_type,
                'description': self.description
            }
        }

    @classmethod
    def get_user_events(cls, user_id, start_date=None, end_date=None):
        """
        Obtiene todos los eventos para un usuario en un rango de fechas.

        Args:
            user_id: ID del usuario
            start_date: Fecha de inicio del rango (opcional)
            end_date: Fecha de fin del rango (opcional)

        Returns:
            Lista de objetos CalendarEvent
        """
        from app.models.project import Project
        from app.models.task import Task
        from app.models.leave import LeaveRequest
        from app.models.meeting import Meeting

        events = []

        # Obtener proyectos del usuario
        try:
            # Obtener el usuario actual
            from app.models.user import User
            user = User.query.get(user_id)

            if user:
                # Obtener los proyectos a los que pertenece el usuario
                projects = user.projects

                for project in projects:
                    # Crear evento para el proyecto
                    events.append(CalendarEvent(
                        id=f"project_{project.id}",
                        title=f"مشروع: {project.name}",
                        start_date=project.start_date or datetime.now(),
                        end_date=project.end_date or datetime.now(),
                        event_type=cls.TYPE_PROJECT,
                        description=project.description,
                        url=f"/projects/view/{project.id}",
                        all_day=True
                    ))
        except Exception as e:
            print(f"Error al obtener proyectos: {e}")

        # Obtener tareas del usuario
        try:
            tasks = Task.query.filter_by(assignee_id=user_id).all()
            for task in tasks:
                # Crear evento para la tarea
                events.append(CalendarEvent(
                    id=f"task_{task.id}",
                    title=f"مهمة: {task.title}",
                    start_date=task.due_date or datetime.now(),
                    end_date=task.due_date or datetime.now(),
                    event_type=cls.TYPE_TASK,
                    description=task.description,
                    url=f"/projects/view/{task.project_id}#task-{task.id}",
                    all_day=True
                ))
        except Exception as e:
            print(f"Error al obtener tareas: {e}")

        # Obtener solicitudes de permiso del usuario
        try:
            leaves = LeaveRequest.query.filter_by(user_id=user_id).all()
            for leave in leaves:
                # Mapear estado a texto
                status_text = {
                    'pending': 'قيد الانتظار',
                    'approved': 'تمت الموافقة',
                    'rejected': 'مرفوض'
                }.get(leave.status, leave.status)

                # Crear evento para la solicitud de permiso
                events.append(CalendarEvent(
                    id=f"leave_{leave.id}",
                    title=f"إجازة: {status_text}",
                    start_date=leave.start_date,
                    end_date=leave.end_date,
                    event_type=cls.TYPE_LEAVE,
                    description=leave.reason,
                    url=f"/leave/view/{leave.id}",
                    all_day=True
                ))
        except Exception as e:
            print(f"Error al obtener solicitudes de permiso: {e}")

        # Obtener reuniones a las que asiste el usuario
        try:
            # Obtener el usuario actual
            from app.models.user import User
            user = User.query.get(user_id)

            if user:
                # Obtener las reuniones a las que asiste el usuario
                meetings = user.meetings

                for meeting in meetings:
                    # Crear hora de inicio y fin
                    start_datetime = datetime.combine(meeting.date, meeting.start_time) if meeting.date and meeting.start_time else datetime.now()
                    end_datetime = datetime.combine(meeting.date, meeting.end_time) if meeting.date and meeting.end_time else start_datetime

                    # Crear evento para la reunión
                    events.append(CalendarEvent(
                        id=f"meeting_{meeting.id}",
                        title=f"اجتماع: {meeting.title}",
                        start_date=start_datetime,
                        end_date=end_datetime,
                        event_type=cls.TYPE_MEETING,
                        description=meeting.description,
                        url=f"/meetings/view/{meeting.id}",
                        all_day=False
                    ))
        except Exception as e:
            print(f"Error al obtener reuniones: {e}")

        return events
