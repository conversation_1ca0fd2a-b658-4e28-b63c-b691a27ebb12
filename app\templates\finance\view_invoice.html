{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>تفاصيل الفاتورة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.invoices') }}">الفواتير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ invoice.invoice_number }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">فاتورة #{{ invoice.invoice_number }}</h5>
                    <div>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        {% if invoice.status != 'paid' %}
                        <a href="{{ url_for('finance.edit_invoice', id=invoice.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#markPaidModal">
                            <i class="fas fa-check me-1"></i>تحديد كمدفوع
                        </button>
                        <a href="{{ url_for('finance.delete_invoice', id=invoice.id) }}" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                        {% endif %}
                        <button type="button" class="btn btn-info" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold">معلومات الفاتورة</h6>
                            <p><strong>رقم الفاتورة:</strong> {{ invoice.invoice_number }}</p>
                            <p><strong>تاريخ الإصدار:</strong> {{ invoice.issue_date.strftime('%Y-%m-%d') }}</p>
                            <p><strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد' }}</p>
                            <p><strong>تاريخ الموافقة:</strong> {{ invoice.approval_date.strftime('%Y-%m-%d') if invoice.approval_date else 'غير محدد' }}</p>
                            <p>
                                <strong>الحالة:</strong>
                                {% if invoice.status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                                {% elif invoice.status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                                {% elif invoice.status == 'overdue' %}
                                <span class="badge bg-danger">متأخر</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ invoice.status }}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">معلومات العميل</h6>
                            {% if invoice.client %}
                            <p><strong>الاسم:</strong> {{ invoice.client.name }}</p>
                            <p><strong>الشركة:</strong> {{ invoice.client.company or 'غير محدد' }}</p>
                            <p><strong>البريد الإلكتروني:</strong> {{ invoice.client.email or 'غير محدد' }}</p>
                            <p><strong>الهاتف:</strong> {{ invoice.client.phone or 'غير محدد' }}</p>
                            {% else %}
                            <p>لا توجد معلومات عن العميل</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold">تفاصيل المشروع</h6>
                            {% if invoice.project %}
                            <p><strong>اسم المشروع:</strong> {{ invoice.project.name }}</p>
                            <p><strong>الوصف:</strong> {{ invoice.project.description or 'غير محدد' }}</p>
                            {% else %}
                            <p>لا يوجد مشروع مرتبط بهذه الفاتورة</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="fw-bold">بنود الفاتورة</h6>
                            {% if invoice.items %}
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>الوصف</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>المشرف</th>
                                            <th>ربح الشركة</th>
                                            <th>ربح الموظف</th>
                                            <th>المجموع</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الاستلام</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in invoice.items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ item.description }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>${{ item.unit_price }}</td>
                                            <td>{{ item.supervisor.username if item.supervisor else 'غير محدد' }}</td>
                                            <td>
                                                {% if item.company_profit_type == 'percentage' %}
                                                {{ item.company_profit_value }}%
                                                {% else %}
                                                ${{ item.company_profit_value }}
                                                {% endif %}
                                                (${{ "%.2f"|format(item.calculate_company_profit()) }})
                                            </td>
                                            <td>${{ "%.2f"|format(item.employee_profit) }}</td>
                                            <td>${{ "%.2f"|format(item.total_price) }}</td>
                                            <td>
                                                {% if item.status == 'مستلم' %}
                                                <span class="badge bg-success">{{ item.status }}</span>
                                                {% else %}
                                                <span class="badge bg-warning">{{ item.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ item.receipt_date.strftime('%Y-%m-%d') if item.receipt_date else 'غير محدد' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="6" class="text-end"><strong>المجموع الفرعي</strong></td>
                                            <td><strong>${{ subtotal }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td colspan="6" class="text-end">
                                                <strong>عمولة التحويل</strong>
                                                <small class="text-muted">
                                                    ({% if invoice.transfer_fee_type == 'percentage' %}{{ invoice.transfer_fee_value }}%{% else %}${{ invoice.transfer_fee_value }}{% endif %})
                                                </small>
                                            </td>
                                            <td><strong>${{ transfer_fee }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td colspan="6" class="text-end">
                                                <strong>الضريبة</strong>
                                                <small class="text-muted">
                                                    ({% if invoice.tax_type == 'percentage' %}{{ invoice.tax_value }}%{% else %}${{ invoice.tax_value }}{% endif %})
                                                </small>
                                            </td>
                                            <td><strong>${{ tax }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td colspan="6" class="text-end">
                                                <strong>الخصم</strong>
                                                <small class="text-muted">
                                                    ({% if invoice.discount_type == 'percentage' %}{{ invoice.discount_value }}%{% else %}${{ invoice.discount_value }}{% endif %})
                                                </small>
                                            </td>
                                            <td><strong>${{ discount }}</strong></td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td colspan="6" class="text-end"><strong>المجموع الكلي</strong></td>
                                            <td><strong>${{ total }}</strong></td>
                                        </tr>
                                        <tr class="table-success">
                                            <td colspan="6" class="text-end"><strong>إجمالي ربح الشركة</strong></td>
                                            <td><strong>${{ company_profit }}</strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                لا توجد بنود في هذه الفاتورة.
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if invoice.attachments.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold">المرفقات</h6>
                            <ul class="list-group">
                                {% for attachment in invoice.attachments %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>{{ attachment.filename }}</span>
                                    <div>
                                        <a href="{{ url_for('finance.download_invoice_attachment', attachment_id=attachment.id) }}" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-download"></i> تنزيل
                                        </a>
                                        <span class="badge bg-primary rounded-pill ms-2">{{ attachment.uploaded_at.strftime('%Y-%m-%d') }}</span>
                                    </div>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    {% if invoice.verification_links.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold">روابط الإثبات</h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرابط</th>
                                            <th>الوصف</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>تمت الإضافة بواسطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for link in invoice.verification_links %}
                                        <tr>
                                            <td>{{ link.url }}</td>
                                            <td>{{ link.description or 'بدون وصف' }}</td>
                                            <td>{{ link.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ link.added_by.first_name }} {{ link.added_by.last_name }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ link.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-external-link-alt"></i> فتح الرابط
                                                    </a>
                                                    {% if current_user.has_role('admin') or current_user.has_role('finance') %}
                                                    <a href="{{ url_for('finance.delete_invoice_verification_link', link_id=link.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الرابط؟');">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if invoice.notes %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold">ملاحظات</h6>
                            <p>{{ invoice.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-end">
                    <a href="{{ url_for('finance.invoices') }}" class="btn btn-secondary">العودة إلى قائمة الفواتير</a>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Mark Paid Modal -->
<div class="modal fade" id="markPaidModal" tabindex="-1" aria-labelledby="markPaidModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="markPaidModalLabel">تأكيد تحديد الفاتورة كمدفوعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في تحديد هذه الفاتورة كمدفوعة؟
                <br>
                <span class="text-warning">سيتم إنشاء معاملة مالية تلقائيًا بقيمة الفاتورة.</span>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('finance.mark_invoice_paid', id=invoice.id) }}" method="POST">
                    <button type="submit" class="btn btn-success">تحديد كمدفوع</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
