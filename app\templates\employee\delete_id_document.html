{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف وثيقة هوية</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة لصفحة الموظف
        </a>
        <a href="{{ url_for('employee.view_id_document', id=document.id) }}" class="btn btn-info">
            <i class="fas fa-eye me-1"></i>عرض الوثيقة
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد حذف وثيقة هوية</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> هل أنت متأكد من رغبتك في حذف هذه الوثيقة؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>

        <div class="mb-4">
            <h5>بيانات الوثيقة:</h5>
            <p><strong>الموظف:</strong> {{ employee.get_full_name() }}</p>
            <p><strong>نوع الوثيقة:</strong> {{ document.document_type }}</p>
            <p><strong>رقم الوثيقة:</strong> {{ document.document_number }}</p>
            <p><strong>تاريخ الإصدار:</strong> {{ document.issue_date.strftime('%Y-%m-%d') if document.issue_date else 'غير محدد' }}</p>
            <p><strong>تاريخ الانتهاء:</strong> {{ document.expiry_date.strftime('%Y-%m-%d') if document.expiry_date else 'غير محدد' }}</p>

            {% if document.document_file %}
            <p><strong>الملف المرفق:</strong>
                <a href="{{ url_for('employee.download_id_document', id=document.id) }}">
                    {% set file_parts = document.document_file.split('|') %}
                    {% if file_parts|length > 1 %}
                        {{ file_parts[1] }}
                    {% else %}
                        {{ document.document_file.split('/')[-1] }}
                    {% endif %}
                </a>
            </p>
            {% endif %}
        </div>

        <form method="POST" action="{{ url_for('employee.delete_id_document', id=document.id) }}">
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
