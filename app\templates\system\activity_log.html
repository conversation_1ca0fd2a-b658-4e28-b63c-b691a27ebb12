{% extends 'base.html' %}

{% block styles %}
<style>
    .filter-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .log-table th {
        position: sticky;
        top: 0;
        background-color: #fff;
        z-index: 10;
    }

    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .per-page-selector {
        width: auto;
    }

    .export-buttons {
        margin-bottom: 15px;
    }

    .export-buttons .btn {
        margin-right: 5px;
    }

    .action-badge {
        font-size: 0.8rem;
    }

    .action-create {
        background-color: #28a745;
    }

    .action-update {
        background-color: #ffc107;
        color: #212529;
    }

    .action-delete {
        background-color: #dc3545;
    }

    .action-login {
        background-color: #17a2b8;
    }

    .action-logout {
        background-color: #6c757d;
    }

    .action-other {
        background-color: #6610f2;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>سجل النشاطات</h1>
    <div>
        <a href="{{ url_for('system.config') }}" class="btn btn-secondary">
            <i class="fas fa-cog me-1"></i>إعدادات النظام
        </a>
        <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تصفية النتائج</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('system.activity_log') }}" class="filter-form">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="action" class="form-label">الإجراء</label>
                    <select class="form-select" id="action" name="action">
                        <option value="">الكل</option>
                        {% for action_name in actions %}
                        <option value="{{ action_name }}" {% if current_action == action_name %}selected{% endif %}>{{ action_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="entity_type" class="form-label">نوع الكيان</label>
                    <select class="form-select" id="entity_type" name="entity_type">
                        <option value="">الكل</option>
                        {% for entity_name in entity_types %}
                        <option value="{{ entity_name }}" {% if current_entity_type == entity_name %}selected{% endif %}>{{ entity_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="user_id" class="form-label">المستخدم</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">الكل</option>
                        {% for user in users %}
                        <option value="{{ user.id }}" {% if current_user_id|string == user.id|string %}selected{% endif %}>{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="per_page" class="form-label">عدد النتائج في الصفحة</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="25" {% if current_per_page == 25 %}selected{% endif %}>25</option>
                        <option value="50" {% if current_per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if current_per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ current_start_date }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ current_end_date }}">
                </div>
                <div class="col-md-6 d-flex align-items-end mb-3">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i>تصفية
                    </button>
                    <a href="{{ url_for('system.activity_log') }}" class="btn btn-secondary">
                        <i class="fas fa-undo me-1"></i>إعادة ضبط
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Action Buttons -->
<div class="export-buttons">
    <form id="deleteLogsForm" action="{{ url_for('system.delete_logs') }}" method="POST">
        <input type="hidden" name="selected_logs" id="selectedLogsInput">
        <button type="button" id="deleteSelectedBtn" class="btn btn-danger" disabled>
            <i class="fas fa-trash-alt me-1"></i>حذف السجلات المحددة
        </button>
        <a href="{{ url_for('system.export_activity_log', action=current_action, entity_type=current_entity_type, user_id=current_user_id, start_date=current_start_date, end_date=current_end_date) }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>تصدير إلى Excel
        </a>
    </form>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف السجلات المحددة؟</p>
                <p>عدد السجلات المحددة: <span id="selectedCount">0</span></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash-alt me-1"></i>تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Activity Log Table -->
<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">سجل النشاطات ({{ logs.total }})</h5>
    </div>
    <div class="card-body">
        {% if logs.items %}
        <div class="table-responsive">
            <table class="table table-hover log-table">
                <thead>
                    <tr>
                        <th>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll">
                            </div>
                        </th>
                        <th>#</th>
                        <th>التاريخ والوقت</th>
                        <th>المستخدم</th>
                        <th>الإجراء</th>
                        <th>نوع الكيان</th>
                        <th>معرف الكيان</th>
                        <th>الوصف</th>
                        <th>عنوان IP</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs.items %}
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input log-checkbox" type="checkbox" name="log_ids" value="{{ log.id }}">
                            </div>
                        </td>
                        <td>{{ log.id }}</td>
                        <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            {% if log.user %}
                            <a href="{{ url_for('employee.view', id=log.user.id) }}">{{ log.user.username }}</a>
                            {% else %}
                            غير مسجل
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge action-badge {% if log.action == 'create' %}action-create{% elif log.action == 'update' %}action-update{% elif log.action == 'delete' %}action-delete{% elif log.action == 'login' %}action-login{% elif log.action == 'logout' %}action-logout{% else %}action-other{% endif %}">
                                {{ log.action }}
                            </span>
                        </td>
                        <td>{{ log.entity_type }}</td>
                        <td>{{ log.entity_id }}</td>
                        <td>{{ log.description }}</td>
                        <td>{{ log.ip_address or 'غير معروف' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div>
                عرض {{ logs.items|length }} من {{ logs.total }} سجل
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if logs.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('system.activity_log', page=logs.prev_num, per_page=current_per_page, action=current_action, entity_type=current_entity_type, user_id=current_user_id, start_date=current_start_date, end_date=current_end_date) }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% set start_page = logs.page - 2 if logs.page > 2 else 1 %}
                    {% set end_page = start_page + 4 if start_page + 4 <= logs.pages else logs.pages %}
                    {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                    {% for page_num in range(start_page, end_page + 1) %}
                    <li class="page-item {% if page_num == logs.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('system.activity_log', page=page_num, per_page=current_per_page, action=current_action, entity_type=current_entity_type, user_id=current_user_id, start_date=current_start_date, end_date=current_end_date) }}">{{ page_num }}</a>
                    </li>
                    {% endfor %}

                    {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('system.activity_log', page=logs.next_num, per_page=current_per_page, action=current_action, entity_type=current_entity_type, user_id=current_user_id, start_date=current_start_date, end_date=current_end_date) }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>لا توجد سجلات نشاط متطابقة مع معايير البحث.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Select all checkbox functionality
        $('#selectAll').change(function() {
            $('.log-checkbox').prop('checked', $(this).prop('checked'));
            updateDeleteButton();
        });

        // Individual checkbox change
        $('.log-checkbox').change(function() {
            updateDeleteButton();

            // If any checkbox is unchecked, uncheck "Select All" checkbox
            if (!$(this).prop('checked')) {
                $('#selectAll').prop('checked', false);
            }

            // If all checkboxes are checked, check "Select All" checkbox
            if ($('.log-checkbox:checked').length === $('.log-checkbox').length) {
                $('#selectAll').prop('checked', true);
            }
        });

        // Update delete button state
        function updateDeleteButton() {
            const selectedCount = $('.log-checkbox:checked').length;
            $('#deleteSelectedBtn').prop('disabled', selectedCount === 0);
            $('#selectedCount').text(selectedCount);
        }

        // Show delete confirmation modal
        $('#deleteSelectedBtn').click(function() {
            const selectedCount = $('.log-checkbox:checked').length;
            $('#selectedCount').text(selectedCount);
            $('#deleteConfirmModal').modal('show');
        });

        // Handle delete confirmation
        $('#confirmDeleteBtn').click(function() {
            // Collect selected log IDs
            const selectedLogs = [];
            $('.log-checkbox:checked').each(function() {
                selectedLogs.push($(this).val());
            });

            // Set the hidden input value
            $('#selectedLogsInput').val(JSON.stringify(selectedLogs));

            // Submit the form
            $('#deleteLogsForm').submit();
        });
    });
</script>
{% endblock %}