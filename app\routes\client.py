from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
import datetime
import os
from werkzeug.utils import secure_filename
from app import db
from app.models.client import Client
from app.models.project import Project
from app.models.finance import Invoice
from app.config import UPLOAD_FOLDER

client_bp = Blueprint('client', __name__, url_prefix='/clients')

@client_bp.route('/')
@login_required
def index():
    # Check if user has permission to view clients
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.has_role('sales') or current_user.has_role('finance')):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')

    # Build the query
    query = Client.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Client.name.like(search_term)) |
            (Client.email.like(search_term)) |
            (Client.phone.like(search_term)) |
            (Client.company.like(search_term))
        )

    # Order by name
    query = query.order_by(Client.name)

    # Paginate the results
    clients = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('client/index.html',
                          title='Clients',
                          clients=clients,
                          search_query=search_query,
                          current_per_page=per_page)

@client_bp.route('/view/<int:id>')
@login_required
def view(id):
    # Check if user has permission to view client details
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.has_role('sales') or current_user.has_role('finance')):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    client = Client.query.get_or_404(id)

    # Get pagination parameters for projects
    projects_page = request.args.get('projects_page', 1, type=int)
    projects_per_page = request.args.get('projects_per_page', 10, type=int)
    projects_status_filter = request.args.get('projects_status', 'all')

    # Build projects query
    projects_query = Project.query.filter_by(client_id=id)

    # Apply status filter to projects
    if projects_status_filter != 'all':
        projects_query = projects_query.filter(Project.status == projects_status_filter)

    # Order projects by creation date (newest first)
    projects_query = projects_query.order_by(Project.created_at.desc())

    # Paginate projects
    projects = projects_query.paginate(page=projects_page, per_page=projects_per_page, error_out=False)

    # Get client invoices
    invoices = Invoice.query.filter_by(client_id=id).all()

    # Get recent projects (sorted by creation date, limited to 5)
    recent_projects = Project.query.filter_by(client_id=id).order_by(Project.created_at.desc()).limit(5).all()

    # Calculate project statistics
    projects_count = projects_query.count()
    completed_projects_count = projects_query.filter(Project.status == 'completed').count()
    in_progress_projects_count = projects_query.filter(Project.status == 'in_progress').count()
    pending_projects_count = projects_query.filter(Project.status == 'pending').count()

    return render_template('client/view.html', title=f'Client: {client.name}',
                          client=client, projects=projects, invoices=invoices,
                          recent_projects=recent_projects,
                          projects_count=projects_count,
                          completed_projects_count=completed_projects_count,
                          in_progress_projects_count=in_progress_projects_count,
                          pending_projects_count=pending_projects_count,
                          # Project pagination and filtering parameters
                          projects_status_filter=projects_status_filter,
                          projects_per_page=projects_per_page)

@client_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has permission to create clients
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('sales')):
        flash('You do not have permission to create clients', 'danger')
        return redirect(url_for('client.index'))

    if request.method == 'POST':
        name = request.form.get('name')
        company = request.form.get('company')
        email = request.form.get('email')
        phone = request.form.get('phone')
        address = request.form.get('address')
        website = request.form.get('website')
        notes = request.form.get('notes')

        # Check if email already exists
        if email and Client.query.filter_by(email=email).first():
            flash('Email already exists', 'danger')
            return redirect(url_for('client.create'))

        # Create new client
        client = Client(
            name=name,
            company=company,
            email=email,
            phone=phone,
            address=address,
            website=website,
            notes=notes
        )

        # Handle profile image upload
        if 'profile_image' in request.files and request.files['profile_image'].filename:
            file = request.files['profile_image']
            filename = secure_filename(file.filename)

            # Create a unique filename to avoid overwriting
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            profile_images_dir = os.path.join(UPLOAD_FOLDER, 'client_images')
            os.makedirs(profile_images_dir, exist_ok=True)

            # Save the file
            file_path = os.path.join(profile_images_dir, unique_filename)
            file.save(file_path)

            # Update the database with the new image path
            client.profile_image = f'uploads/client_images/{unique_filename}'

        db.session.add(client)
        db.session.commit()

        flash('تم إنشاء العميل بنجاح', 'success')
        return redirect(url_for('client.view', id=client.id))

    return render_template('client/create.html', title='Create Client')

@client_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Check if user has permission to edit clients
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('sales')):
        flash('You do not have permission to edit clients', 'danger')
        return redirect(url_for('client.index'))

    client = Client.query.get_or_404(id)

    if request.method == 'POST':
        client.name = request.form.get('name')
        client.company = request.form.get('company')

        # Check if email changed and already exists
        new_email = request.form.get('email')
        if new_email != client.email and new_email and Client.query.filter_by(email=new_email).first():
            flash('Email already exists', 'danger')
            return redirect(url_for('client.edit', id=id))

        client.email = new_email
        client.phone = request.form.get('phone')
        client.address = request.form.get('address')
        client.website = request.form.get('website')
        client.notes = request.form.get('notes')

        # Handle profile image upload
        if 'profile_image' in request.files and request.files['profile_image'].filename:
            file = request.files['profile_image']
            filename = secure_filename(file.filename)

            # Create a unique filename to avoid overwriting
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            profile_images_dir = os.path.join(UPLOAD_FOLDER, 'client_images')
            os.makedirs(profile_images_dir, exist_ok=True)

            # Save the file
            file_path = os.path.join(profile_images_dir, unique_filename)
            file.save(file_path)

            # Update the database with the new image path
            client.profile_image = f'uploads/client_images/{unique_filename}'

        db.session.commit()

        flash('تم تحديث بيانات العميل بنجاح', 'success')
        return redirect(url_for('client.view', id=id))

    return render_template('client/edit.html', title=f'Edit Client: {client.name}', client=client)

@client_bp.route('/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete(id):
    # Only admin, manager, or sales can delete clients
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('sales')):
        flash('لا تملك صلاحية حذف العملاء', 'danger')
        return redirect(url_for('client.index'))

    client = Client.query.get_or_404(id)

    # Check if client has projects
    projects_count = Project.query.filter_by(client_id=id).count()

    # Check if client has invoices
    invoices_count = Invoice.query.filter_by(client_id=id).count()

    if request.method == 'POST':
        # Double check that client has no projects or invoices
        if projects_count > 0:
            flash('لا يمكن حذف العميل المرتبط بمشاريع', 'danger')
            return redirect(url_for('client.view', id=id))

        if invoices_count > 0:
            flash('لا يمكن حذف العميل المرتبط بفواتير', 'danger')
            return redirect(url_for('client.view', id=id))

        db.session.delete(client)
        db.session.commit()

        flash('تم حذف العميل بنجاح', 'success')
        return redirect(url_for('client.index'))

    return render_template('client/delete.html', title='حذف العميل',
                          client=client, projects_count=projects_count,
                          invoices_count=invoices_count)

@client_bp.route('/<int:id>/projects')
@login_required
def projects(id):
    # Check if user has permission to view client projects
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.has_role('sales') or current_user.has_role('finance')):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    client = Client.query.get_or_404(id)

    # Filter projects based on user role
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers can see all projects
        projects = Project.query.filter_by(client_id=id).all()
    else:
        # Regular users can't see cancelled projects
        projects = Project.query.filter_by(client_id=id).filter(Project.status != 'cancelled').all()

    return render_template('client/projects.html', title=f'{client.name} Projects',
                          client=client, projects=projects)

@client_bp.route('/<int:id>/invoices')
@login_required
def invoices(id):
    # Check if user has permission to view client invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.has_role('sales') or current_user.has_role('finance')):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    client = Client.query.get_or_404(id)
    invoices = Invoice.query.filter_by(client_id=id).all()

    return render_template('client/invoices.html', title=f'{client.name} Invoices',
                          client=client, invoices=invoices)
