from flask import Blueprint, render_template, redirect, url_for, request, current_app, flash
from flask_login import login_required, current_user
import os
import shutil
import zipfile
import tempfile
import json
import sqlite3
import time
import datetime
from werkzeug.utils import secure_filename

from app import db
from app.models.system_config import SystemConfig
from app.models.activity_log import ActivityLog
from app.utils.activity_logger import log_activity
from app.utils.decorators import admin_required

restore_bp = Blueprint('restore', __name__)

@restore_bp.route('/system/restore', methods=['GET'])
@login_required
@admin_required
def restore_page():
    """Display the main restore page"""
    # Get recent restore logs
    restore_logs = []
    logs = ActivityLog.query.filter(
        (ActivityLog.action == 'restore_success') |
        (ActivityLog.action == 'restore_failed')
    ).order_by(ActivityLog.timestamp.desc()).limit(10).all()

    for log in logs:
        # Ensure the user is loaded within the session
        user_name = 'النظام'
        if log.user_id:
            from app.models.user import User
            user = db.session.get(User, log.user_id)
            if user:
                user_name = user.username

        restore_logs.append({
            'id': log.id,
            'timestamp': log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'user': user_name,
            'success': log.action == 'restore_success',
            'description': log.description
        })

    return render_template('system/restore_page.html', restore_logs=restore_logs)

@restore_bp.route('/system/upload_backup', methods=['POST'])
@login_required
@admin_required
def upload_backup():
    """Handle backup file upload and show confirmation page"""
    if 'backup_file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('restore.restore_page'))

    backup_file = request.files['backup_file']

    if backup_file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('restore.restore_page'))

    if not backup_file.filename.endswith('.zip'):
        flash('يجب أن يكون الملف بتنسيق ZIP', 'danger')
        return redirect(url_for('restore.restore_page'))

    # Create a temporary directory to store the file
    temp_dir = tempfile.mkdtemp()
    backup_path = os.path.join(temp_dir, secure_filename(backup_file.filename))

    # Save the uploaded file
    backup_file.save(backup_path)

    # Log the upload
    log_activity(
        action='restore_upload',
        entity_type='system',
        entity_id=0,
        description=f'تم رفع ملف نسخة احتياطية: {backup_file.filename}'
    )

    # Get file size in a human-readable format
    file_size = os.path.getsize(backup_path)
    if file_size < 1024:
        filesize = f"{file_size} بايت"
    elif file_size < 1024 * 1024:
        filesize = f"{file_size / 1024:.1f} كيلوبايت"
    else:
        filesize = f"{file_size / (1024 * 1024):.1f} ميجابايت"

    # Show confirmation page before proceeding with restore
    return render_template('system/restore_confirm.html',
                          title='تأكيد استعادة النسخة الاحتياطية',
                          filename=backup_file.filename,
                          filesize=filesize,
                          temp_file_path=backup_path)

@restore_bp.route('/system/restore_confirmed', methods=['POST'])
@login_required
@admin_required
def restore_confirmed():
    """Process the confirmed restore operation"""

    # Get the temporary file path from the form
    temp_file_path = request.form.get('temp_file_path')
    if not temp_file_path or not os.path.exists(temp_file_path):
        return render_template('system/restore_error.html',
                              title='خطأ في استعادة النسخة الاحتياطية',
                              error_message='ملف النسخة الاحتياطية غير موجود أو تم حذفه',
                              log_content="✗ ملف النسخة الاحتياطية غير موجود أو تم حذفه")

    # Log the restore activity
    log_activity(
        action='restore_confirmed',
        entity_type='system',
        entity_id=0,
        description='قام المستخدم بتأكيد استعادة النظام من نسخة احتياطية'
    )

    # Initialize variables
    temp_dir = os.path.dirname(temp_file_path)
    backup_path = temp_file_path
    restore_log = ["✓ بدء عملية استعادة النسخة الاحتياطية"]

    try:
        # Validate the zip file
        if not zipfile.is_zipfile(backup_path):
            error_msg = 'الملف المحدد ليس ملف ZIP صالح'
            restore_log.append(f"✗ {error_msg}")

            # Log validation failure
            log_activity(
                action='restore_failed',
                entity_type='system',
                entity_id=0,
                description='فشل التحقق من صحة ملف النسخة الاحتياطية: الملف ليس بتنسيق ZIP'
            )

            # Save restore log
            log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt')
            with open(log_file_path, 'w', encoding='utf-8') as log_file:
                log_file.write("\n".join(restore_log))

            # Render error page
            return render_template('system/restore_error.html',
                                  title='خطأ في استعادة النسخة الاحتياطية',
                                  error_message=error_msg,
                                  log_content="\n".join(restore_log))

        # Extract the zip file
        extract_dir = os.path.join(temp_dir, 'extract')
        os.makedirs(extract_dir, exist_ok=True)

        with zipfile.ZipFile(backup_path, 'r') as zipf:
            # Check if this is a valid backup file
            required_files = ['database/sparkle.db', 'config/system_config.json']
            missing_files = [f for f in required_files if f not in zipf.namelist()]

            if missing_files:
                error_msg = f'ملف النسخة الاحتياطية غير صالح. الملفات المفقودة: {", ".join(missing_files)}'
                restore_log.append(f"✗ {error_msg}")

                # Log validation failure
                log_activity(
                    action='restore_failed',
                    entity_type='system',
                    entity_id=0,
                    description=f'فشل التحقق من صحة ملف النسخة الاحتياطية: ملفات مفقودة: {", ".join(missing_files)}'
                )

                # Save restore log
                log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt')
                with open(log_file_path, 'w', encoding='utf-8') as log_file:
                    log_file.write("\n".join(restore_log))

                # Render error page
                return render_template('system/restore_error.html',
                                      title='خطأ في استعادة النسخة الاحتياطية',
                                      error_message=error_msg,
                                      log_content="\n".join(restore_log))

            # Check if backup log exists
            has_log = 'backup_log.txt' in zipf.namelist()

            # Extract all files
            zipf.extractall(extract_dir)
            restore_log.append("✓ تم استخراج ملفات النسخة الاحتياطية بنجاح")

            # Read backup log if it exists
            if has_log:
                with open(os.path.join(extract_dir, 'backup_log.txt'), 'r', encoding='utf-8') as log_file:
                    backup_log_content = log_file.read()
                    restore_log.append("معلومات النسخة الاحتياطية:")
                    restore_log.append(backup_log_content)

            # Verify database structure
            db_path = os.path.join(extract_dir, 'database', 'sparkle.db')
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # Get all tables in the database
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    restore_log.append(f"\nالتحقق من هيكل قاعدة البيانات:")
                    restore_log.append(f"  - عدد الجداول في قاعدة البيانات: {len(tables)}")

                    # Check for required tables
                    required_tables = [
                        "users", "departments", "projects", "tasks", "clients",
                        "invoices", "transactions", "notifications", "leave_requests",
                        "meetings", "activity_logs"
                    ]

                    missing_tables = [table for table in required_tables if table not in tables]
                    if missing_tables:
                        restore_log.append(f"  ⚠️ تحذير: الجداول التالية مفقودة: {', '.join(missing_tables)}")
                    else:
                        restore_log.append(f"  ✓ جميع الجداول الأساسية موجودة")

                    conn.close()
                except Exception as e:
                    restore_log.append(f"  ⚠️ خطأ أثناء التحقق من هيكل قاعدة البيانات: {str(e)}")

        # 1. Restore the database
        db_backup_path = os.path.join(extract_dir, 'database', 'sparkle.db')
        db_path = os.path.join(current_app.root_path, 'sparkle.db')

        if os.path.exists(db_backup_path):
            # Verify database contains image paths
            conn = sqlite3.connect(db_backup_path)
            cursor = conn.cursor()

            # Check if users table has profile_image and profile_image_data columns
            cursor.execute("PRAGMA table_info(users)")
            user_columns = [column[1] for column in cursor.fetchall()]
            has_user_profile_image = 'profile_image' in user_columns
            has_user_profile_image_data = 'profile_image_data' in user_columns
            has_user_profile_image_mime = 'profile_image_mime' in user_columns

            # Check if clients table has profile_image and profile_image_data columns
            cursor.execute("PRAGMA table_info(clients)")
            client_columns = [column[1] for column in cursor.fetchall()]
            has_client_profile_image = 'profile_image' in client_columns
            has_client_profile_image_data = 'profile_image_data' in client_columns
            has_client_profile_image_mime = 'profile_image_mime' in client_columns

            # Check image columns
            if has_user_profile_image and has_client_profile_image:
                # Check user profile images (file paths)
                cursor.execute("SELECT COUNT(*) FROM users WHERE profile_image IS NOT NULL AND profile_image != 'default.jpg'")
                user_images_file_count = cursor.fetchone()[0]

                # Check client profile images (file paths)
                cursor.execute("SELECT COUNT(*) FROM clients WHERE profile_image IS NOT NULL AND profile_image != 'default.jpg'")
                client_images_file_count = cursor.fetchone()[0]

                restore_log.append(f"✓ قاعدة البيانات تحتوي على {user_images_file_count} صورة للموظفين و {client_images_file_count} صورة للعملاء (ملفات)")
            else:
                restore_log.append("⚠ قاعدة البيانات لا تحتوي على أعمدة الصور المطلوبة (ملفات)")
                if not has_user_profile_image:
                    restore_log.append("  - عمود profile_image مفقود في جدول users")
                if not has_client_profile_image:
                    restore_log.append("  - عمود profile_image مفقود في جدول clients")

            # Check binary image data columns
            if has_user_profile_image_data and has_client_profile_image_data:
                # Check user profile images (binary data)
                cursor.execute("SELECT COUNT(*) FROM users WHERE profile_image_data IS NOT NULL")
                user_images_data_count = cursor.fetchone()[0]

                # Check client profile images (binary data)
                cursor.execute("SELECT COUNT(*) FROM clients WHERE profile_image_data IS NOT NULL")
                client_images_data_count = cursor.fetchone()[0]

                restore_log.append(f"✓ قاعدة البيانات تحتوي على {user_images_data_count} صورة للموظفين و {client_images_data_count} صورة للعملاء (بيانات ثنائية)")
            else:
                restore_log.append("⚠ قاعدة البيانات لا تحتوي على أعمدة الصور المطلوبة (بيانات ثنائية)")
                if not has_user_profile_image_data:
                    restore_log.append("  - عمود profile_image_data مفقود في جدول users")
                if not has_client_profile_image_data:
                    restore_log.append("  - عمود profile_image_data مفقود في جدول clients")

                # Add columns if they don't exist (will be applied after restore)
                restore_log.append("ℹ سيتم إضافة الأعمدة المفقودة بعد استعادة قاعدة البيانات")

            conn.close()

            # Check if we need to update the database schema after restore
            need_schema_update = False
            if not has_user_profile_image_data or not has_client_profile_image_data:
                need_schema_update = True
                restore_log.append("ℹ سيتم تحديث هيكل قاعدة البيانات بعد الاستعادة لإضافة دعم تخزين الصور")

            # Close the current database connection
            db.session.close()
            db.engine.dispose()

            # Wait a moment to ensure connections are closed
            time.sleep(1)

            try:
                # Replace the database file
                shutil.copy2(db_backup_path, db_path)
                restore_log.append("✓ تم استعادة قاعدة البيانات بنجاح")

                # Update database schema if needed
                if need_schema_update:
                    try:
                        # Connect to the restored database
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()

                        # Check and add missing columns to users table
                        cursor.execute("PRAGMA table_info(users)")
                        user_columns = [column[1] for column in cursor.fetchall()]

                        if 'profile_image_data' not in user_columns:
                            cursor.execute("ALTER TABLE users ADD COLUMN profile_image_data BLOB")
                            restore_log.append("✓ تم إضافة عمود profile_image_data إلى جدول users")

                        if 'profile_image_mime' not in user_columns:
                            cursor.execute("ALTER TABLE users ADD COLUMN profile_image_mime VARCHAR(64)")
                            restore_log.append("✓ تم إضافة عمود profile_image_mime إلى جدول users")

                        # Check and add missing columns to clients table
                        cursor.execute("PRAGMA table_info(clients)")
                        client_columns = [column[1] for column in cursor.fetchall()]

                        if 'profile_image_data' not in client_columns:
                            cursor.execute("ALTER TABLE clients ADD COLUMN profile_image_data BLOB")
                            restore_log.append("✓ تم إضافة عمود profile_image_data إلى جدول clients")

                        if 'profile_image_mime' not in client_columns:
                            cursor.execute("ALTER TABLE clients ADD COLUMN profile_image_mime VARCHAR(64)")
                            restore_log.append("✓ تم إضافة عمود profile_image_mime إلى جدول clients")

                        # Commit changes and close connection
                        conn.commit()
                        conn.close()

                        restore_log.append("✓ تم تحديث هيكل قاعدة البيانات بنجاح")
                    except Exception as e:
                        restore_log.append(f"⚠ حدث خطأ أثناء تحديث هيكل قاعدة البيانات: {str(e)}")
                        # Continue with the restore process, but log the warning

            except PermissionError:
                error_msg = 'لا يمكن استبدال ملف قاعدة البيانات. يرجى التأكد من إغلاق جميع الاتصالات وإعادة المحاولة.'
                restore_log.append(f"✗ {error_msg}")

                # Save restore log
                log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt')
                with open(log_file_path, 'w', encoding='utf-8') as log_file:
                    log_file.write("\n".join(restore_log))

                # Render error page
                return render_template('system/restore_error.html',
                                      title='خطأ في استعادة النسخة الاحتياطية',
                                      error_message=error_msg,
                                      log_content="\n".join(restore_log))
        else:
            error_msg = 'ملف قاعدة البيانات غير موجود في النسخة الاحتياطية'
            restore_log.append(f"✗ {error_msg}")
            # Continue with the restore process, but log the warning

        # 2. Restore uploaded files
        uploads_backup_dir = os.path.join(extract_dir, 'static', 'uploads')
        uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')

        if os.path.exists(uploads_backup_dir):
            try:
                # Count files before restoration
                profile_images_count = 0
                client_images_count = 0

                # Check if profile images directory exists in backup
                profile_images_dir = os.path.join(uploads_backup_dir, 'profile_images')
                if os.path.exists(profile_images_dir):
                    profile_images_count = len([f for f in os.listdir(profile_images_dir) if os.path.isfile(os.path.join(profile_images_dir, f))])

                # Check if client images directory exists in backup
                client_images_dir = os.path.join(uploads_backup_dir, 'client_images')
                if os.path.exists(client_images_dir):
                    client_images_count = len([f for f in os.listdir(client_images_dir) if os.path.isfile(os.path.join(client_images_dir, f))])

                restore_log.append(f"النسخة الاحتياطية تحتوي على {profile_images_count} صورة للموظفين و {client_images_count} صورة للعملاء")

                # Remove existing uploads directory
                if os.path.exists(uploads_dir):
                    # First try to remove files one by one to handle permission errors
                    for root, dirs, files in os.walk(uploads_dir, topdown=False):
                        for file in files:
                            try:
                                os.remove(os.path.join(root, file))
                            except PermissionError:
                                # Skip files that are in use
                                restore_log.append(f"⚠ تم تخطي حذف الملف (قيد الاستخدام): {os.path.join(root, file)}")
                                continue
                        for dir in dirs:
                            try:
                                os.rmdir(os.path.join(root, dir))
                            except (PermissionError, OSError):
                                # Skip directories that are in use or not empty
                                continue

                    # Try to remove the main directory
                    try:
                        os.rmdir(uploads_dir)
                    except (PermissionError, OSError):
                        # If we can't remove it, just continue
                        pass

                # Create the uploads directory if it doesn't exist
                os.makedirs(uploads_dir, exist_ok=True)

                # Copy files from backup to the uploads directory
                files_copied = 0
                files_skipped = 0

                for root, dirs, files in os.walk(uploads_backup_dir):
                    # Create all directories
                    for dir in dirs:
                        src_dir = os.path.join(root, dir)
                        dst_dir = os.path.join(uploads_dir, os.path.relpath(src_dir, uploads_backup_dir))
                        os.makedirs(dst_dir, exist_ok=True)

                    # Copy all files
                    for file in files:
                        src_file = os.path.join(root, file)
                        dst_file = os.path.join(uploads_dir, os.path.relpath(src_file, uploads_backup_dir))
                        try:
                            shutil.copy2(src_file, dst_file)
                            files_copied += 1
                        except PermissionError:
                            # Skip files that can't be copied
                            files_skipped += 1
                            restore_log.append(f"⚠ تم تخطي نسخ الملف (قيد الاستخدام): {os.path.relpath(src_file, uploads_backup_dir)}")
                            continue

                restore_log.append(f"✓ تم استعادة الملفات المرفوعة بنجاح ({files_copied} ملف تم نسخه، {files_skipped} ملف تم تخطيه)")

                # Verify profile images directory exists
                if not os.path.exists(os.path.join(uploads_dir, 'profile_images')):
                    os.makedirs(os.path.join(uploads_dir, 'profile_images'), exist_ok=True)
                    restore_log.append("⚠ تم إنشاء مجلد صور الموظفين لأنه غير موجود في النسخة الاحتياطية")

                # Verify client images directory exists
                if not os.path.exists(os.path.join(uploads_dir, 'client_images')):
                    os.makedirs(os.path.join(uploads_dir, 'client_images'), exist_ok=True)
                    restore_log.append("⚠ تم إنشاء مجلد صور العملاء لأنه غير موجود في النسخة الاحتياطية")

            except Exception as e:
                error_msg = f'حدث خطأ أثناء استعادة الملفات المرفوعة: {str(e)}'
                restore_log.append(f"✗ {error_msg}")
                # Continue with the restore process, but log the warning
        else:
            error_msg = 'مجلد الملفات المرفوعة غير موجود في النسخة الاحتياطية'
            restore_log.append(f"✗ {error_msg}")
            # Continue with the restore process, but log the warning

        # 3. Restore system configuration
        config_backup_path = os.path.join(extract_dir, 'config', 'system_config.json')

        if os.path.exists(config_backup_path):
            try:
                with open(config_backup_path, 'r') as f:
                    config_data = json.load(f)

                # Update the configuration in the database
                for key, value in config_data.items():
                    SystemConfig.set(key, value)

                restore_log.append(f"✓ تم استعادة إعدادات النظام بنجاح ({len(config_data)} إعداد)")
            except Exception as e:
                error_msg = f'حدث خطأ أثناء استعادة إعدادات النظام: {str(e)}'
                restore_log.append(f"✗ {error_msg}")
                # Continue with the restore process, but log the warning
        else:
            error_msg = 'ملف إعدادات النظام غير موجود في النسخة الاحتياطية'
            restore_log.append(f"✗ {error_msg}")
            # Continue with the restore process, but log the warning

        # Save restore log
        log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_log.txt')
        with open(log_file_path, 'w', encoding='utf-8') as log_file:
            log_file.write("\n".join(restore_log))

        # Log successful restore
        try:
            log_activity(
                action='restore_success',
                entity_type='system',
                entity_id=0,
                description='تمت استعادة النظام بنجاح من نسخة احتياطية'
            )
        except Exception as e:
            restore_log.append(f"⚠ خطأ في تسجيل النشاط: {str(e)}")

        # Create a fresh session for rendering the template
        from flask_login import logout_user
        logout_user()  # Log out the current user since the session is invalid

        # Render success page with minimal template
        return render_template('system/minimal_success.html',
                              title='استعادة النسخة الاحتياطية',
                              log_content="\n".join(restore_log))

    except Exception as e:
        error_msg = f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}'
        restore_log.append(f"✗ {error_msg}")

        # Try to save restore log even if there's an error
        try:
            log_file_path = os.path.join(current_app.root_path, 'static', 'uploads', 'restore_error_log.txt')
            with open(log_file_path, 'w', encoding='utf-8') as log_file:
                log_file.write("\n".join(restore_log))
        except:
            pass

        # Log failed restore
        try:
            log_activity(
                action='restore_failed',
                entity_type='system',
                entity_id=0,
                description=f'فشلت عملية استعادة النظام: {str(e)}'
            )
        except:
            pass

        # Create a fresh session for rendering the template
        from flask_login import logout_user
        logout_user()  # Log out the current user since the session is invalid

        # Render error page with minimal template
        return render_template('system/minimal_error.html',
                              title='خطأ في استعادة النسخة الاحتياطية',
                              error_message=error_msg,
                              log_content="\n".join(restore_log))

    finally:
        # Clean up the temporary directory
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except PermissionError:
                # If we can't delete the temp directory now, it will be cleaned up later
                pass
