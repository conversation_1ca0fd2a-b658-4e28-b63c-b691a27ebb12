import sqlite3
import os

def run_migration():
    print("Running migration: add_meeting_summaries.py")
    
    # Connect to the database
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    # Create meeting_summaries table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meeting_summaries'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE meeting_summaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                meeting_id INTEGER NOT NULL,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (meeting_id) REFERENCES meetings (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
            ''')
            print("Created meeting_summaries table")
        except sqlite3.OperationalError as e:
            print(f"Error creating meeting_summaries table: {e}")
    else:
        print("meeting_summaries table already exists")
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print("Migration completed: add_meeting_summaries.py")

if __name__ == "__main__":
    run_migration()
