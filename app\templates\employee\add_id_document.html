{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة وثيقة هوية</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة لبيانات الموظف
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">بيانات الوثيقة لـ {{ employee.get_full_name() }}</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('employee.add_id_document', id=employee.id) }}" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="document_type" class="form-label">نوع الوثيقة <span class="text-danger">*</span></label>
                    <select class="form-select" id="document_type" name="document_type" required>
                        <option value="" selected disabled>اختر نوع الوثيقة</option>
                        <option value="جواز سفر">جواز سفر</option>
                        <option value="بطاقة هوية">بطاقة هوية</option>
                        <option value="رخصة قيادة">رخصة قيادة</option>
                        <option value="إقامة">إقامة</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="document_number" class="form-label">رقم الوثيقة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="document_number" name="document_number" required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                    <input type="date" class="form-control" id="issue_date" name="issue_date">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="issuing_country" class="form-label">بلد الإصدار</label>
                <input type="text" class="form-control" id="issuing_country" name="issuing_country">
            </div>
            
            <div class="mb-3">
                <label for="document_file" class="form-label">ملف الوثيقة</label>
                <input type="file" class="form-control" id="document_file" name="document_file">
                <small class="text-muted">يمكنك رفع نسخة من الوثيقة (PDF، صورة، إلخ)</small>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
