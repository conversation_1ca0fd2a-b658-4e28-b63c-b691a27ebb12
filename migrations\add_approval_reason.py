import os
import sqlite3
import sys

def find_db_files():
    """Find all SQLite database files in the current directory and subdirectories."""
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return db_files

def migrate_db(db_path):
    """Add approval_reason column to leave_requests table."""
    print(f"Attempting to migrate database: {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if the leave_requests table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='leave_requests'")
    if not cursor.fetchone():
        print(f"No 'leave_requests' table found in {db_path}")
        conn.close()
        return False
    
    # Check if the column already exists
    cursor.execute("PRAGMA table_info(leave_requests)")
    columns = [column[1] for column in cursor.fetchall()]
    
    if 'approval_reason' not in columns:
        try:
            # Add the new column
            cursor.execute("ALTER TABLE leave_requests ADD COLUMN approval_reason TEXT")
            print(f"Added 'approval_reason' column to leave_requests table in {db_path}")
            conn.commit()
            conn.close()
            return True
        except sqlite3.OperationalError as e:
            print(f"Error adding column: {e}")
            conn.close()
            return False
    else:
        print(f"Column 'approval_reason' already exists in leave_requests table in {db_path}")
        conn.close()
        return True

if __name__ == '__main__':
    db_files = find_db_files()
    
    if not db_files:
        print("No database files found")
        sys.exit(1)
    
    print(f"Found {len(db_files)} database files: {db_files}")
    
    success = False
    for db_file in db_files:
        if migrate_db(db_file):
            success = True
    
    if success:
        print("Migration completed successfully for at least one database")
        sys.exit(0)
    else:
        print("Migration failed for all databases")
        sys.exit(1)
