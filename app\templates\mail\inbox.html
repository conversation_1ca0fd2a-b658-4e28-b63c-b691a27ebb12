{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ title }}</h1>
    <a href="{{ url_for('mail.compose') }}" class="btn btn-primary">
        <i class="fas fa-pen me-1"></i>رسالة جديدة
    </a>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="list-group">
            <a href="{{ url_for('mail.inbox') }}" class="list-group-item list-group-item-action {% if folder == 'inbox' %}active{% endif %}">
                <i class="fas fa-inbox me-2"></i>البريد الوارد
            </a>
            <a href="{{ url_for('mail.sent') }}" class="list-group-item list-group-item-action {% if folder == 'sent' %}active{% endif %}">
                <i class="fas fa-paper-plane me-2"></i>البريد المرسل
            </a>
            <a href="{{ url_for('mail.drafts') }}" class="list-group-item list-group-item-action {% if folder == 'drafts' %}active{% endif %}">
                <i class="fas fa-save me-2"></i>المسودات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <form method="GET" action="{{ url_for('mail.' + folder) }}" class="mb-0">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بالموضوع أو المحتوى{% if folder == 'inbox' %} أو اسم المرسل{% elif folder == 'sent' %} أو اسم المستلم{% endif %}" value="{{ search_query }}">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                                {% if search_query %}
                                <a href="{{ url_for('mail.' + folder) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="has_attachments" name="has_attachments" onchange="this.form.submit()">
                                <option value="" {% if not has_attachments %}selected{% endif %}>جميع الرسائل</option>
                                <option value="yes" {% if has_attachments == 'yes' %}selected{% endif %}>مع مرفقات</option>
                                <option value="no" {% if has_attachments == 'no' %}selected{% endif %}>بدون مرفقات</option>
                            </select>
                        </div>
                        {% if folder == 'inbox' %}
                        <div class="col-md-3">
                            <select class="form-select" id="read_status" name="read_status" onchange="this.form.submit()">
                                <option value="" {% if not read_status %}selected{% endif %}>جميع الرسائل</option>
                                <option value="read" {% if read_status == 'read' %}selected{% endif %}>مقروءة</option>
                                <option value="unread" {% if read_status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                            </select>
                        </div>
                        {% else %}
                        <div class="col-md-3">
                            <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 رسالة</option>
                                <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 رسالة</option>
                                <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 رسالة</option>
                            </select>
                        </div>
                        {% endif %}
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">من تاريخ</span>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">إلى تاريخ</span>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                            </div>
                        </div>
                        {% if folder == 'inbox' %}
                        <div class="col-md-2">
                            <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25</option>
                                <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50</option>
                                <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100</option>
                            </select>
                        </div>
                        {% endif %}
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter"></i> تصفية
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('mail.' + folder) }}" class="btn btn-secondary w-100">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-body p-0">
                {% if mails.items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                {% if folder == 'inbox' %}
                                <th>المرسل</th>
                                {% else %}
                                <th>المستلم</th>
                                {% endif %}
                                <th>الموضوع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mail in mails.items %}
                            <tr class="{% if folder == 'inbox' and not mail.is_read_by(current_user.id) %}fw-bold{% endif %}">
                                {% if folder == 'inbox' %}
                                <td>{{ mail.sender.first_name }} {{ mail.sender.last_name }}</td>
                                {% else %}
                                <td>{{ mail.get_all_recipients_names() }}</td>
                                {% endif %}
                                <td>
                                    <a href="{{ url_for('mail.view', id=mail.id) }}" class="text-decoration-none text-dark">
                                        {% if mail.attachments %}
                                        <i class="fas fa-paperclip text-muted me-1"></i>
                                        {% endif %}
                                        {{ mail.subject }}
                                    </a>
                                </td>
                                <td>{{ mail.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('mail.view', id=mail.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if folder == 'inbox' %}
                                        <a href="{{ url_for('mail.compose', reply_to=mail.id) }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('mail.compose', forward=mail.id) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-share"></i>
                                        </a>
                                        <a href="{{ url_for('mail.confirm_delete', id=mail.id) }}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
                    <div>
                        عرض {{ mails.items|length }} من {{ mails.total }} رسالة
                        {% if search_query %}
                        <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                        {% endif %}
                        {% if has_attachments %}
                        <span class="text-muted">
                            (المرفقات:
                            {% if has_attachments == 'yes' %}مع مرفقات{% endif %}
                            {% if has_attachments == 'no' %}بدون مرفقات{% endif %}
                            )
                        </span>
                        {% endif %}
                        {% if read_status %}
                        <span class="text-muted">
                            (الحالة:
                            {% if read_status == 'read' %}مقروءة{% endif %}
                            {% if read_status == 'unread' %}غير مقروءة{% endif %}
                            )
                        </span>
                        {% endif %}
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination mb-0">
                            {% if mails.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('mail.' + folder, page=mails.prev_num, per_page=current_per_page, search=search_query, has_attachments=has_attachments, read_status=read_status, date_from=date_from, date_to=date_to) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% set start_page = mails.page - 2 if mails.page > 2 else 1 %}
                            {% set end_page = start_page + 4 if start_page + 4 <= mails.pages else mails.pages %}
                            {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            <li class="page-item {% if page_num == mails.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('mail.' + folder, page=page_num, per_page=current_per_page, search=search_query, has_attachments=has_attachments, read_status=read_status, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}

                            {% if mails.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('mail.' + folder, page=mails.next_num, per_page=current_per_page, search=search_query, has_attachments=has_attachments, read_status=read_status, date_from=date_from, date_to=date_to) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% else %}
                <div class="text-center p-5">
                    <i class="fas fa-envelope-open fa-4x text-muted mb-3"></i>
                    <h4>لا توجد رسائل</h4>
                    {% if search_query or has_attachments or read_status or date_from or date_to %}
                    <p class="text-muted">لا توجد نتائج مطابقة للبحث</p>
                    <a href="{{ url_for('mail.' + folder) }}" class="btn btn-secondary mt-2">
                        <i class="fas fa-redo me-1"></i>عرض جميع الرسائل
                    </a>
                    {% else %}
                    <p class="text-muted">لا توجد رسائل في هذا المجلد حالياً</p>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
