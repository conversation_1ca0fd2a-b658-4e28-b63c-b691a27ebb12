{% extends 'base.html' %}

{% block title %}استعادة النسخة الاحتياطية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">استعادة النسخة الاحتياطية</h1>
        <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى لوحة التحكم
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-primary text-white">
                    <h6 class="m-0 font-weight-bold">استعادة النظام من نسخة احتياطية</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيؤدي استعادة النسخة الاحتياطية إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومات:</strong> يرجى اختيار ملف النسخة الاحتياطية الذي تم إنشاؤه من خلال نظام Sparkle Media Agency. يجب أن يكون الملف بتنسيق ZIP.
                    </div>

                    <form action="{{ url_for('restore.upload_backup') }}" method="POST" enctype="multipart/form-data" class="mt-4">
                        <div class="form-group">
                            <label for="backup_file" class="font-weight-bold">اختر ملف النسخة الاحتياطية:</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="backup_file" name="backup_file" required accept=".zip">
                                <label class="custom-file-label" for="backup_file" data-browse="استعراض">اختر ملف...</label>
                            </div>
                            <small class="form-text text-muted">يجب أن يكون الملف بتنسيق ZIP ويحتوي على بيانات النظام.</small>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>رفع ملف النسخة الاحتياطية
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <h5 class="mb-3">إرشادات استعادة النسخة الاحتياطية:</h5>
                    <ol>
                        <li>تأكد من أن لديك نسخة احتياطية صالحة تم إنشاؤها من خلال نظام Sparkle Media Agency.</li>
                        <li>قم برفع ملف النسخة الاحتياطية باستخدام النموذج أعلاه.</li>
                        <li>راجع تفاصيل النسخة الاحتياطية في صفحة التأكيد قبل المتابعة.</li>
                        <li>انتظر حتى تكتمل عملية الاستعادة. قد تستغرق العملية بضع دقائق حسب حجم البيانات.</li>
                        <li>بعد اكتمال العملية، سيتم عرض تقرير مفصل عن نتيجة الاستعادة.</li>
                    </ol>

                    <div class="alert alert-secondary mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>نصيحة:</strong> يُفضل إجراء عملية الاستعادة عندما لا يكون هناك مستخدمين آخرين يستخدمون النظام لتجنب أي تعارض في البيانات.
                    </div>
                </div>
            </div>

            {% if restore_logs %}
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-info text-white">
                    <h6 class="m-0 font-weight-bold">سجل عمليات الاستعادة السابقة</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المستخدم</th>
                                    <th>النتيجة</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in restore_logs %}
                                <tr>
                                    <td>{{ log.timestamp }}</td>
                                    <td>{{ log.user }}</td>
                                    <td>
                                        {% if log.success %}
                                        <span class="badge bg-success text-white">نجاح</span>
                                        {% else %}
                                        <span class="badge bg-danger text-white">فشل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#logModal{{ log.id }}">
                                            <i class="fas fa-eye"></i> عرض التفاصيل
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript to update file input label -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelector('.custom-file-input').addEventListener('change', function(e) {
            var fileName = e.target.files[0].name;
            var nextSibling = e.target.nextElementSibling;
            nextSibling.innerText = fileName;
        });
    });
</script>
{% endblock %}
