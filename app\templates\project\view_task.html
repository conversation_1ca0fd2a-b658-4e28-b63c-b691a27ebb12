{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل المهمة</h1>
        <div>
            <a href="{{ url_for('project.tasks', id=task.project_id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى قائمة المهام
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">{{ task.title }}</h6>
                    <div>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') or
                            current_user.id == task.project.manager_id or
                            (current_user.has_role('department_head') and current_user.managed_department.id == task.project.department_id) %}
                        <a href="{{ url_for('project.edit_task', task_id=task.id) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{{ url_for('project.delete_task', task_id=task.id) }}" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6 class="font-weight-bold">المشروع:</h6>
                                <p>
                                    <a href="{{ url_for('project.view', id=task.project_id) }}">
                                        {{ task.project.name }}
                                    </a>
                                </p>
                            </div>
                            <div class="mb-3">
                                <h6 class="font-weight-bold">الحالة:</h6>
                                <p>
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                    
                                    {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                        current_user.id == task.project.manager_id or current_user.id == task.assignee_id %}
                                    <form id="task-status-form-{{ task.id }}" action="{{ url_for('project.update_task_status', task_id=task.id) }}" method="POST" class="d-inline ms-2">
                                        <select class="form-select form-select-sm d-inline-block w-auto task-status-select" 
                                                name="status" 
                                                data-task-id="{{ task.id }}"
                                                aria-label="تغيير الحالة">
                                            <option value="pending" {% if task.status == 'pending' %}selected{% endif %}>معلق</option>
                                            <option value="in_progress" {% if task.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                            <option value="completed" {% if task.status == 'completed' %}selected{% endif %}>مكتمل</option>
                                            <option value="cancelled" {% if task.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                        </select>
                                    </form>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="mb-3">
                                <h6 class="font-weight-bold">المسؤول:</h6>
                                <p>{{ task.assignee.get_full_name() if task.assignee else 'غير محدد' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <h6 class="font-weight-bold">تاريخ البدء:</h6>
                                <p>{{ task.start_date.strftime('%Y-%m-%d') if task.start_date else 'غير محدد' }}</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="font-weight-bold">تاريخ الاستحقاق:</h6>
                                <p>
                                    {{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}
                                    {% if task.is_overdue() %}
                                    <span class="badge bg-danger ms-1">متأخر</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="mb-3">
                                <h6 class="font-weight-bold">الأولوية:</h6>
                                <p>
                                    {% if task.priority == 'low' %}
                                    <span class="badge bg-info">منخفضة</span>
                                    {% elif task.priority == 'medium' %}
                                    <span class="badge bg-warning">متوسطة</span>
                                    {% elif task.priority == 'high' %}
                                    <span class="badge bg-danger">عالية</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="font-weight-bold">الوصف:</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                {{ task.description|nl2br|safe if task.description else 'لا يوجد وصف' }}
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="font-weight-bold">تاريخ الإنشاء:</h6>
                        <p>{{ task.created_at.strftime('%Y-%m-%d %I:%M %p') }}</p>
                    </div>

                    {% if task.updated_at and task.updated_at != task.created_at %}
                    <div class="mb-4">
                        <h6 class="font-weight-bold">آخر تحديث:</h6>
                        <p>{{ task.updated_at.strftime('%Y-%m-%d %I:%M %p') }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit task status form when status changes
        const taskStatusSelects = document.querySelectorAll('.task-status-select');
        taskStatusSelects.forEach(select => {
            select.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                document.getElementById(`task-status-form-${taskId}`).submit();
            });
        });
    });
</script>
{% endblock %}
