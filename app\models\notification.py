from datetime import datetime
from app import db

class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(20))  # task, project, system, etc.
    is_read = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Link fields
    link_url = db.Column(db.String(255))
    link_text = db.Column(db.String(100))

    # Relationships
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))

    # Optional related entities
    related_task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))
    related_task = db.relationship('Task', backref='notifications')

    related_project_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('projects.id'))
    related_project = db.relationship('Project', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.id}: {self.title}>'

    def mark_as_read(self):
        self.is_read = True
