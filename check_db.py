import sqlite3
import os

def check_database():
    # Get the path to the database file
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
    print(f'Database path: {db_path}')

    # Connect to the SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f'Tables: {tables}')

        # Check invoices table structure
        if 'invoices' in tables:
            print("\nInvoices table structure:")
            cursor.execute("PRAGMA table_info(invoices)")
            columns = cursor.fetchall()
            for column in columns:
                print(f"  {column}")

            # Check if there are any invoices
            cursor.execute("SELECT COUNT(*) FROM invoices")
            count = cursor.fetchone()[0]
            print(f"\nNumber of invoices: {count}")

            if count > 0:
                # Show the first invoice
                print("\nFirst invoice:")
                cursor.execute("SELECT * FROM invoices LIMIT 1")
                invoice = cursor.fetchone()
                print(f"  {invoice}")

        # Check invoice_items table structure
        if 'invoice_items' in tables:
            print("\nInvoice_items table structure:")
            cursor.execute("PRAGMA table_info(invoice_items)")
            columns = cursor.fetchall()
            for column in columns:
                print(f"  {column}")

            # Check if there are any invoice items
            cursor.execute("SELECT COUNT(*) FROM invoice_items")
            count = cursor.fetchone()[0]
            print(f"\nNumber of invoice items: {count}")

        # Check invoice_attachments table structure
        if 'invoice_attachments' in tables:
            print("\nInvoice_attachments table structure:")
            cursor.execute("PRAGMA table_info(invoice_attachments)")
            columns = cursor.fetchall()
            for column in columns:
                print(f"  {column}")

    except Exception as e:
        print(f"Error: {e}")

    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
