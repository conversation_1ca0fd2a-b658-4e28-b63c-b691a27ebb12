# مخطط قاعدة البيانات للنظام المالي المتكامل

## 1. وحدة المحاسبة العامة (Core Accounting Engine)

### جدول ChartOfAccounts (شجرة الحسابات)
```sql
CREATE TABLE chart_of_accounts (
    id INTEGER PRIMARY KEY,
    account_code VARCHAR(20) UNIQUE NOT NULL,  -- e.g., 1010, 4020
    account_name VARCHAR(255) NOT NULL,        -- e.g., "النقد في البنك"
    account_type ENUM('Asset', 'Liability', 'Equity', 'Revenue', 'Expense') NOT NULL,
    parent_account_id INTEGER,                 -- للحسابات الفرعية
    balance DECIMAL(15,2) DEFAULT 0,           -- الرصيد الحالي
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
);
```

### جدول JournalEntries (قيود اليومية)
```sql
CREATE TABLE journal_entries (
    id INTEGER PRIMARY KEY,
    entry_number VARCHAR(50) UNIQUE NOT NULL,  -- رقم القيد
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    source_document_type VARCHAR(50),          -- 'invoice', 'bill', 'payment'
    source_document_id INTEGER,                -- ID المستند الأصلي
    total_debit DECIMAL(15,2) NOT NULL,
    total_credit DECIMAL(15,2) NOT NULL,
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
    created_by_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

### جدول JournalItems (بنود قيد اليومية)
```sql
CREATE TABLE journal_items (
    id INTEGER PRIMARY KEY,
    journal_entry_id INTEGER NOT NULL,
    account_id INTEGER NOT NULL,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);
```

## 2. وحدة العملاء والفواتير (Enhanced Accounts Receivable)

### تحسين جدول Customers (العملاء)
```sql
-- تحسين الجدول الحالي clients
ALTER TABLE clients ADD COLUMN credit_limit DECIMAL(15,2) DEFAULT 0;
ALTER TABLE clients ADD COLUMN payment_terms INTEGER DEFAULT 30; -- أيام الدفع
ALTER TABLE clients ADD COLUMN tax_id VARCHAR(50);
ALTER TABLE clients ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
```

### تحسين جدول Invoices (الفواتير)
```sql
-- إضافة حقول جديدة للجدول الحالي
ALTER TABLE invoices ADD COLUMN invoice_type ENUM('sales', 'service') DEFAULT 'service';
ALTER TABLE invoices ADD COLUMN payment_terms INTEGER DEFAULT 30;
ALTER TABLE invoices ADD COLUMN amount_due DECIMAL(15,2);
ALTER TABLE invoices ADD COLUMN journal_entry_id INTEGER;
ALTER TABLE invoices ADD COLUMN tax_amount DECIMAL(15,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN discount_amount DECIMAL(15,2) DEFAULT 0;
```

### جدول PaymentsReceived (الدفعات المستلمة)
```sql
CREATE TABLE payments_received (
    id INTEGER PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    invoice_id INTEGER,                        -- يمكن أن يكون NULL للدفعات المقدمة
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') NOT NULL,
    reference_number VARCHAR(100),             -- رقم الحوالة أو الشيك
    notes TEXT,
    journal_entry_id INTEGER,
    created_by_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES clients(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

## 3. وحدة الموردين والمصروفات (Accounts Payable)

### جدول Vendors (الموردون)
```sql
CREATE TABLE vendors (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company VARCHAR(255),
    email VARCHAR(120),
    phone VARCHAR(20),
    address TEXT,
    tax_id VARCHAR(50),
    payment_terms INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### جدول VendorBills (فواتير الموردين)
```sql
CREATE TABLE vendor_bills (
    id INTEGER PRIMARY KEY,
    vendor_id INTEGER NOT NULL,
    bill_number VARCHAR(100) NOT NULL,
    vendor_reference VARCHAR(100),             -- رقم فاتورة المورد
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    amount_due DECIMAL(15,2) NOT NULL,
    status ENUM('draft', 'to_pay', 'paid', 'cancelled') DEFAULT 'draft',
    journal_entry_id INTEGER,
    created_by_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

### جدول VendorBillItems (بنود فواتير الموردين)
```sql
CREATE TABLE vendor_bill_items (
    id INTEGER PRIMARY KEY,
    bill_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) DEFAULT 1,
    unit_price DECIMAL(15,2) NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL,
    account_id INTEGER NOT NULL,               -- حساب المصروف
    FOREIGN KEY (bill_id) REFERENCES vendor_bills(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);
```

### جدول PaymentsMade (الدفعات المسددة)
```sql
CREATE TABLE payments_made (
    id INTEGER PRIMARY KEY,
    vendor_id INTEGER NOT NULL,
    bill_id INTEGER,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'check') NOT NULL,
    reference_number VARCHAR(100),
    notes TEXT,
    journal_entry_id INTEGER,
    created_by_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id),
    FOREIGN KEY (bill_id) REFERENCES vendor_bills(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

## 4. جداول مساعدة

### جدول FiscalYears (السنوات المالية)
```sql
CREATE TABLE fiscal_years (
    id INTEGER PRIMARY KEY,
    name VARCHAR(50) NOT NULL,              -- e.g., "2024"
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    status ENUM('open', 'closed') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول TaxRates (معدلات الضرائب)
```sql
CREATE TABLE tax_rates (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,             -- e.g., "ضريبة القيمة المضافة"
    rate DECIMAL(5,2) NOT NULL,             -- e.g., 15.00 for 15%
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## الحسابات الافتراضية المطلوبة

```sql
-- الأصول (Assets)
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
('1010', 'النقد في البنك', 'Asset'),
('1020', 'حسابات العملاء', 'Asset'),
('1030', 'المخزون', 'Asset'),
('1040', 'المعدات والأجهزة', 'Asset');

-- الخصوم (Liabilities)
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
('2010', 'حسابات الموردين', 'Liability'),
('2020', 'ضرائب مستحقة', 'Liability'),
('2030', 'قروض قصيرة الأجل', 'Liability');

-- حقوق الملكية (Equity)
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
('3010', 'رأس المال', 'Equity'),
('3020', 'الأرباح المحتجزة', 'Equity');

-- الإيرادات (Revenue)
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
('4010', 'إيرادات خدمات التصميم', 'Revenue'),
('4020', 'إيرادات خدمات البرمجة', 'Revenue'),
('4030', 'إيرادات خدمات المونتاج', 'Revenue'),
('4040', 'إيرادات إدارة الحسابات', 'Revenue');

-- المصروفات (Expenses)
INSERT INTO chart_of_accounts (account_code, account_name, account_type) VALUES
('5010', 'رواتب الموظفين', 'Expense'),
('5020', 'اشتراكات البرامج', 'Expense'),
('5030', 'إيجار المكتب', 'Expense'),
('5040', 'مصروفات التسويق', 'Expense'),
('5050', 'مصروفات الاتصالات', 'Expense');
```
