{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل طلب الإجازة</h1>
        <a href="{{ url_for('leave.view', id=leave_request.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التفاصيل
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الإجازة</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('leave.edit', id=leave_request.id) }}" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">الموظف</label>
                        <input type="text" class="form-control" value="{{ leave_request.user.get_full_name() }}" readonly>
                        <div class="form-text">لا يمكن تغيير الموظف بعد إنشاء طلب الإجازة</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">حالة الطلب</label>
                        <select class="form-select" id="status" name="status" {% if not current_user.has_role('admin') %}disabled{% endif %}>
                            <option value="pending" {% if leave_request.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="approved" {% if leave_request.status == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                            <option value="rejected" {% if leave_request.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                        </select>
                        {% if not current_user.has_role('admin') %}
                        <div class="form-text">فقط المسؤولون يمكنهم تغيير حالة الطلب مباشرة</div>
                        <input type="hidden" name="status" value="{{ leave_request.status }}">
                        {% endif %}
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">تاريخ بداية الإجازة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ leave_request.start_date.strftime('%Y-%m-%d') }}" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">تاريخ نهاية الإجازة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ leave_request.end_date.strftime('%Y-%m-%d') }}" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="reason" class="form-label">سبب الإجازة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required>{{ leave_request.reason }}</textarea>
                    </div>

                    {% if leave_request.status == 'rejected' %}
                    <div class="col-md-12 mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" {% if not current_user.has_role('admin') %}readonly{% endif %}>{{ leave_request.rejection_reason }}</textarea>
                        {% if not current_user.has_role('admin') %}
                        <div class="form-text">فقط المسؤولون يمكنهم تعديل سبب الرفض</div>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if leave_request.attachments.count() > 0 %}
                    <div class="col-md-12 mb-3">
                        <label class="form-label">المرفقات الحالية:</label>
                        <div class="list-group">
                            {% for attachment in leave_request.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                </div>
                                <form action="{{ url_for('leave.delete_attachment', attachment_id=attachment.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا المرفق؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="col-md-12 mb-3">
                        <label for="attachments" class="form-label">إضافة مرفقات جديدة</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">يمكنك إرفاق ملفات متعددة (مثل التقارير الطبية أو أي مستندات داعمة)</div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('leave.view', id=leave_request.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Set minimum date for start_date to today
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
            
            // If end_date is before start_date, set it to start_date
            if ($('#end_date').val() < $(this).val()) {
                $('#end_date').val($(this).val());
            }
        });
        
        // Calculate duration when dates change
        function updateDuration() {
            const startDate = new Date($('#start_date').val());
            const endDate = new Date($('#end_date').val());
            
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // Include both start and end dates
                
                $('#duration').text(diffDays + ' يوم');
            } else {
                $('#duration').text('');
            }
        }
        
        $('#start_date, #end_date').change(updateDuration);
        
        // Set initial min value for end_date
        $('#end_date').attr('min', $('#start_date').val());
    });
</script>
{% endblock %}
