{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير العقوبات</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='penalties') }}" method="POST">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="employee_id" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id" name="employee_id">
                                    <option value="all" selected>جميع الموظفين</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="penalty_type" class="form-label">نوع العقوبة</label>
                                <select class="form-select" id="penalty_type" name="penalty_type">
                                    <option value="all" selected>الكل</option>
                                    <option value="verbal_warning">لفت نظر شفوي</option>
                                    <option value="written_warning">لفت نظر كتابي (تحذير أول)</option>
                                    <option value="written_notice">إنذار كتابي (تحذير ثاني)</option>
                                    <option value="suspension">إيقاف مؤقت عن العمل / خصم من الراتب</option>
                                    <option value="final_warning">الإنذار النهائي</option>
                                    <option value="termination">الفصل من العمل / إنهاء التعاقد</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع العقوبة</th>
                                    <th>السبب</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الخصم من الراتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for penalty in penalties %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ penalty.user.get_full_name() }}</td>
                                    <td>{{ penalty.get_penalty_type_display() }}</td>
                                    <td>{{ penalty.reason }}</td>
                                    <td>{{ penalty.start_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else 'غير محدد' }}</td>
                                    <td>{{ '$' + penalty.salary_deduction|string if penalty.salary_deduction else 'لا يوجد' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#employee_id, #penalty_type, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
        
        // Set min/max dates
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
        });
        
        $('#end_date').change(function() {
            $('#start_date').attr('max', $(this).val());
        });
    });
</script>
{% endblock %}
