from datetime import datetime
from app import db

class Transaction(db.Model):
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # income, expense
    category = db.Column(db.String(50))  # salary, rent, service, etc.
    description = db.Column(db.Text)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # New fields
    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # Supervisor of the transaction
    recipient = db.Column(db.String(255))  # Recipient of the transaction

    # Alternative currency fields
    alt_amount = db.Column(db.Float)  # Amount in alternative currency
    currency_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('currencies.id'))  # Alternative currency

    # Relationships
    invoice_id = db.Column(db.In<PERSON><PERSON>, db.ForeignKey('invoices.id'))
    recorded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    recorded_by = db.relationship('User', foreign_keys=[recorded_by_id], backref='transactions')
    supervisor = db.relationship('User', foreign_keys=[supervisor_id], backref='supervised_transactions')
    currency = db.relationship('Currency', backref='transactions')

    # Transaction attachments and verification links
    attachments = db.relationship('TransactionAttachment', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')
    verification_links = db.relationship('TransactionVerificationLink', backref='transaction', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Transaction {self.id}: {self.amount}>'

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    approval_date = db.Column(db.DateTime)

    # Nuevos campos para comisiones, impuestos y descuentos
    transfer_fee_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    transfer_fee_value = db.Column(db.Float, default=0)
    tax_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    tax_value = db.Column(db.Float, default=0)
    discount_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    discount_value = db.Column(db.Float, default=0)

    subtotal = db.Column(db.Float, default=0)  # Suma de los elementos de línea
    total_amount = db.Column(db.Float, default=0)  # Total final después de comisiones, impuestos y descuentos

    status = db.Column(db.String(20), default='unpaid')  # unpaid, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_invoices')

    # Invoice items
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Invoice attachments and verification links
    attachments = db.relationship('InvoiceAttachment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    verification_links = db.relationship('InvoiceVerificationLink', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Transactions related to this invoice
    transactions = db.relationship('Transaction', backref='invoice', lazy='dynamic')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

    def is_overdue(self):
        if self.due_date and self.status == 'unpaid':
            return datetime.utcnow() > self.due_date
        return False

    def calculate_subtotal(self):
        """Calcula el subtotal sumando todos los elementos de línea"""
        return sum(item.total_price for item in self.items)

    def calculate_transfer_fee(self):
        """Calcula la comisión de transferencia"""
        if self.transfer_fee_type == 'percentage':
            return (self.transfer_fee_value / 100) * self.subtotal
        else:
            return self.transfer_fee_value

    def calculate_tax(self):
        """Calcula el impuesto"""
        if self.tax_type == 'percentage':
            return (self.tax_value / 100) * self.subtotal
        else:
            return self.tax_value

    def calculate_discount(self):
        """Calcula el descuento"""
        if self.discount_type == 'percentage':
            return (self.discount_value / 100) * self.subtotal
        else:
            return self.discount_value

    def calculate_total(self):
        """Calcula el total final"""
        self.subtotal = self.calculate_subtotal()
        transfer_fee = self.calculate_transfer_fee()
        tax = self.calculate_tax()
        discount = self.calculate_discount()

        total = self.subtotal + transfer_fee + tax - discount
        return max(0, total)  # Asegurarse de que el total no sea negativo

    def update_total(self):
        """Actualiza los campos subtotal y total_amount"""
        self.subtotal = self.calculate_subtotal()
        self.total_amount = self.calculate_total()

    def mark_as_paid(self):
        self.status = 'paid'
        # Create a transaction for this payment
        # El beneficio de la empresa ya está incluido en el total_amount
        transaction = Transaction(
            amount=self.total_amount,
            transaction_type='income',
            category='invoice_payment',
            description=f'Payment for invoice {self.invoice_number}',
            invoice_id=self.id
        )
        db.session.add(transaction)

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Float, default=1)
    unit_price = db.Column(db.Float, nullable=False)

    # Nuevos campos
    company_profit_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    company_profit_value = db.Column(db.Float, default=0)

    # Campos adicionales solicitados
    status = db.Column(db.String(20), default='غير مستلم')  # مستلم, غير مستلم
    receipt_date = db.Column(db.DateTime)  # Fecha de recepción (opcional)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    supervisor = db.relationship('User', backref='supervised_invoice_items')

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    def calculate_company_profit(self):
        """Calcula el beneficio de la empresa para este elemento"""
        if self.company_profit_type == 'percentage':
            return (self.company_profit_value / 100) * self.total_price
        else:
            return self.company_profit_value

    @property
    def employee_profit(self):
        """Calcula el beneficio del empleado para este elemento (total - beneficio de la empresa)"""
        return self.total_price - self.calculate_company_profit()

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'


class InvoiceAttachment(db.Model):
    __tablename__ = 'invoice_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='uploaded_attachments')

    def __repr__(self):
        return f'<InvoiceAttachment {self.filename}>'


class InvoiceVerificationLink(db.Model):
    __tablename__ = 'invoice_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='invoice_verification_links')

    def __repr__(self):
        return f'<InvoiceVerificationLink {self.url}>'


class TransactionAttachment(db.Model):
    __tablename__ = 'transaction_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='transaction_attachments')

    def __repr__(self):
        return f'<TransactionAttachment {self.filename}>'


class TransactionVerificationLink(db.Model):
    __tablename__ = 'transaction_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='transaction_verification_links')

    def __repr__(self):
        return f'<TransactionVerificationLink {self.url}>'
