"""
Migration script to add new fields to transactions table and create currencies and transaction_attachments tables
"""
import sqlite3
import os

def run_migration():
    """
    Add new fields to transactions table and create currencies and transaction_attachments tables
    """
    # Get the database path
    db_path = os.path.join('app', 'sparkle.db')

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Create currencies table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS currencies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            symbol TEXT NOT NULL,
            exchange_rate REAL NOT NULL DEFAULT 1.0
        )
        ''')

        # Insert some default currencies
        currencies = [
            ('USD', 'US Dollar', '$', 1.0),
            ('EUR', 'Euro', '€', 1.1),
            ('SAR', 'Saudi Riyal', '﷼', 0.27),
            ('AED', 'UAE Dirham', 'د.إ', 0.27),
            ('EGP', 'Egyptian Pound', 'ج.م', 0.032),
            ('JOD', 'Jordanian Dinar', 'د.ا', 1.41),
            ('ILS', 'Israeli Shekel', '₪', 0.27)
        ]

        for currency in currencies:
            try:
                cursor.execute('INSERT INTO currencies (code, name, symbol, exchange_rate) VALUES (?, ?, ?, ?)', currency)
            except sqlite3.IntegrityError:
                # Currency already exists, skip
                pass

        # Check if the new columns already exist in transactions table
        cursor.execute("PRAGMA table_info(transactions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        # Add new columns to transactions table if they don't exist
        if 'supervisor_id' not in column_names:
            cursor.execute("ALTER TABLE transactions ADD COLUMN supervisor_id INTEGER REFERENCES users(id)")
            print("Added supervisor_id column to transactions table")

        if 'recipient' not in column_names:
            cursor.execute("ALTER TABLE transactions ADD COLUMN recipient TEXT")
            print("Added recipient column to transactions table")

        if 'alt_amount' not in column_names:
            cursor.execute("ALTER TABLE transactions ADD COLUMN alt_amount REAL")
            print("Added alt_amount column to transactions table")

        if 'currency_id' not in column_names:
            cursor.execute("ALTER TABLE transactions ADD COLUMN currency_id INTEGER REFERENCES currencies(id)")
            print("Added currency_id column to transactions table")

        # Create transaction_attachments table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS transaction_attachments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            transaction_id INTEGER NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
            uploaded_by_id INTEGER NOT NULL REFERENCES users(id)
        )
        ''')
        print("Created transaction_attachments table if it didn't exist")

        # Commit the changes
        conn.commit()
        print("Migration completed successfully")

    except Exception as e:
        print(f"Error during migration: {e}")
        conn.rollback()
    finally:
        # Close the connection
        conn.close()

if __name__ == "__main__":
    run_migration()
