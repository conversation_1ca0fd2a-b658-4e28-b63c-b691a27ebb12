{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تعديل الاجتماع</h1>
        <div>
            <a href="{{ url_for('meeting.view', id=meeting.id) }}" class="btn btn-info">
                <i class="fas fa-eye me-1"></i>عرض
            </a>
            <a href="{{ url_for('meeting.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الاجتماع</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('meeting.edit', id=meeting.id) }}" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="title" class="form-label">عنوان الاجتماع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" value="{{ meeting.title }}" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">وصف الاجتماع</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ meeting.description }}</textarea>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="date" class="form-label">تاريخ الاجتماع <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ meeting.date.strftime('%Y-%m-%d') }}" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="start_time" class="form-label">وقت البدء <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="start_time" name="start_time" value="{{ meeting.start_time.strftime('%H:%M') }}" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="end_time" class="form-label">وقت الانتهاء <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="end_time" name="end_time" value="{{ meeting.end_time.strftime('%H:%M') }}" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="location" class="form-label">مكان الاجتماع</label>
                        <input type="text" class="form-control" id="location" name="location" value="{{ meeting.location }}">
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="external_link" class="form-label">رابط خارجي (اختياري)</label>
                        <input type="url" class="form-control" id="external_link" name="external_link" value="{{ meeting.external_link }}" placeholder="مثال: https://zoom.us/j/123456789">
                        <div class="form-text">يمكنك إضافة رابط لاجتماع افتراضي (Zoom، Microsoft Teams، إلخ)</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="attachments_link" class="form-label">رابط مرفقات (اختياري)</label>
                        <input type="url" class="form-control" id="attachments_link" name="attachments_link" value="{{ meeting.attachments_link }}" placeholder="مثال: https://drive.google.com/drive/folders/abc123">
                        <div class="form-text">يمكنك إضافة رابط لمرفقات الاجتماع (مستندات، عروض تقديمية، إلخ)</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="user_ids" class="form-label">الموظفون المدعوون</label>
                        <select class="form-select" id="user_ids" name="user_ids" multiple data-placeholder="اختر الموظفين...">
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user in meeting.attendees %}selected{% endif %}>{{ user.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر الموظفين الذين سيحضرون الاجتماع</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="client_ids" class="form-label">العملاء المدعوون</label>
                        <select class="form-select" id="client_ids" name="client_ids" multiple data-placeholder="اختر العملاء...">
                            {% for client in clients %}
                            <option value="{{ client.id }}" {% if client in meeting.clients %}selected{% endif %}>{{ client.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر العملاء الذين سيحضرون الاجتماع</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="external_attendees" class="form-label">حاضرون خارجيون</label>
                        <textarea class="form-control" id="external_attendees" name="external_attendees" rows="2">{{ meeting.external_attendees }}</textarea>
                        <div class="form-text">أدخل أسماء الحاضرين الخارجيين (غير الموظفين أو العملاء) مفصولة بفواصل</div>
                    </div>

                    {% if meeting.attachments.count() > 0 %}
                    <div class="col-md-12 mb-3">
                        <label class="form-label">المرفقات الحالية:</label>
                        <div class="list-group">
                            {% for attachment in meeting.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                </div>
                                <form action="{{ url_for('meeting.delete_attachment', attachment_id=attachment.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا المرفق؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="col-md-12 mb-3">
                        <label for="attachments" class="form-label">إضافة مرفقات جديدة</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">يمكنك إرفاق ملفات متعددة (مثل جدول الأعمال أو العروض التقديمية)</div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('meeting.view', id=meeting.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2 for multiple select
        $('#user_ids, #client_ids').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });

        // Validate end time is after start time
        $('#start_time, #end_time').change(function() {
            const startTime = $('#start_time').val();
            const endTime = $('#end_time').val();

            if (startTime && endTime && startTime >= endTime) {
                alert('وقت الانتهاء يجب أن يكون بعد وقت البدء');
                $('#end_time').val('');
            }
        });
    });
</script>
{% endblock %}
