{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الاجتماعات</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='meetings') }}" method="POST">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="employee_id" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id" name="employee_id">
                                    <option value="all" selected>جميع الموظفين</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="all" selected>جميع العملاء</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>العنوان</th>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>المكان</th>
                                    <th>الحاضرون</th>
                                    <th>رابط خارجي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meeting in meetings %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ meeting.title }}</td>
                                    <td>{{ meeting.date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if meeting.start_time is defined and meeting.start_time and meeting.end_time is defined and meeting.end_time %}
                                            {{ meeting.start_time.strftime('%I:%M %p') }} - {{ meeting.end_time.strftime('%I:%M %p') }}
                                        {% elif meeting.meeting_time is defined and meeting.meeting_time %}
                                            {{ meeting.meeting_time.strftime('%I:%M %p') }}
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </td>
                                    <td>{{ meeting.location or 'غير محدد' }}</td>
                                    <td>
                                        {% for attendee in meeting.attendees %}
                                        <span class="badge bg-primary">{{ attendee.get_full_name() }}</span>
                                        {% endfor %}

                                        {% for client in meeting.clients %}
                                        <span class="badge bg-secondary">{{ client.name }}</span>
                                        {% endfor %}

                                        {% if meeting.external_attendees %}
                                            <div class="mt-1">
                                                <strong>حاضرون خارجيون:</strong>
                                                {{ meeting.external_attendees.replace(',', '، ') }}
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if meeting.external_link %}
                                            <a href="{{ meeting.external_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> فتح الرابط
                                            </a>
                                        {% else %}
                                            لا يوجد
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#employee_id, #client_id, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });

        // Set min/max dates
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
        });

        $('#end_date').change(function() {
            $('#start_date').attr('max', $(this).val());
        });
    });
</script>
{% endblock %}
