from datetime import datetime
from app import db

class Report(db.Model):
    __tablename__ = 'reports'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    report_type = db.Column(db.String(50), nullable=False)  # employees, departments, leave, meetings, penalties, clients, financial
    parameters = db.Column(db.Text)  # JSON string with the parameters used to generate the report
    file_path = db.Column(db.String(255))  # Path to the saved report file (PDF/Excel)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='reports')

    def __repr__(self):
        return f'<Report {self.id}: {self.title}>'
