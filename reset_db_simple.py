import os
import sqlite3

# Delete the existing database
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Deleted existing database: {db_path}")

# Create a new empty database
conn = sqlite3.connect(db_path)
conn.close()
print(f"Created new empty database: {db_path}")

print("Database reset completed successfully!")
