{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">الاجتماعات</h1>
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <a href="{{ url_for('meeting.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إنشاء اجتماع جديد
        </a>
        {% endif %}
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الاجتماعات</h6>
        </div>
        <div class="card-body">
            <!-- Search and Filter Form -->
            <form method="GET" action="{{ url_for('meeting.index') }}" class="mb-4">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بالعنوان أو الوصف أو المكان" value="{{ search_query }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            {% if search_query %}
                            <a href="{{ url_for('meeting.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="location" name="location" onchange="this.form.submit()">
                            <option value="" {% if not location_filter %}selected{% endif %}>جميع الأماكن</option>
                            {% for location in locations %}
                            <option value="{{ location }}" {% if location_filter == location %}selected{% endif %}>{{ location }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                            <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 اجتماع</option>
                            <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 اجتماع</option>
                            <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 اجتماع</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">من تاريخ</span>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">إلى تاريخ</span>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('meeting.index') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
            {% if meetings.items %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>المكان</th>
                            <th>عدد الحاضرين</th>
                            <th>منشئ الاجتماع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for meeting in meetings.items %}
                        <tr>
                            <td>{{ meeting.title }}</td>
                            <td>{{ meeting.date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ meeting.start_time.strftime('%I:%M %p') }} - {{ meeting.end_time.strftime('%I:%M %p') }}</td>
                            <td>{{ meeting.location }}</td>
                            <td>{{ meeting.attendees|length + meeting.clients|length }}</td>
                            <td>{{ meeting.created_by.get_full_name() }}</td>
                            <td>
                                <a href="{{ url_for('meeting.view', id=meeting.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                <a href="{{ url_for('meeting.edit', id=meeting.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('meeting.delete', id=meeting.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الاجتماع؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        عرض {{ meetings.items|length }} من {{ meetings.total }} اجتماع
                        {% if search_query %}
                        <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                        {% endif %}
                        {% if location_filter %}
                        <span class="text-muted">(المكان: {{ location_filter }})</span>
                        {% endif %}
                        {% if date_from or date_to %}
                        <span class="text-muted">
                            (التاريخ:
                            {% if date_from %}من {{ date_from }}{% endif %}
                            {% if date_to %}إلى {{ date_to }}{% endif %}
                            )
                        </span>
                        {% endif %}
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            {% if meetings.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('meeting.index', page=meetings.prev_num, per_page=current_per_page, search=search_query, location=location_filter, date_from=date_from, date_to=date_to) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% set start_page = meetings.page - 2 if meetings.page > 2 else 1 %}
                            {% set end_page = start_page + 4 if start_page + 4 <= meetings.pages else meetings.pages %}
                            {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            <li class="page-item {% if page_num == meetings.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('meeting.index', page=page_num, per_page=current_per_page, search=search_query, location=location_filter, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}

                            {% if meetings.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('meeting.index', page=meetings.next_num, per_page=current_per_page, search=search_query, location=location_filter, date_from=date_from, date_to=date_to) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                {% if search_query or location_filter or date_from or date_to %}
                <p class="text-muted mb-0">لا توجد نتائج مطابقة للبحث</p>
                <a href="{{ url_for('meeting.index') }}" class="btn btn-secondary mt-3">
                    <i class="fas fa-redo me-1"></i>عرض جميع الاجتماعات
                </a>
                {% else %}
                <p class="text-muted mb-0">لا توجد اجتماعات حالياً</p>
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <a href="{{ url_for('meeting.create') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus me-1"></i>إنشاء اجتماع جديد
                </a>
                {% endif %}
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
