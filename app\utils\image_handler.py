import os
import base64
from io import BytesIO
from PIL import Image
from flask import current_app
from werkzeug.utils import secure_filename
from app import db

def handle_image_upload(file, model_instance, folder_name, attribute_name='profile_image'):
    """
    Handle image upload for a model instance, saving both to database and file system
    
    Args:
        file: The uploaded file object
        model_instance: The model instance to update
        folder_name: The folder name to save the file in
        attribute_name: The attribute name to update in the model instance
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Save image to database
        from app.utils.image_storage import save_image_to_db, get_mime_type
        
        # Save image to database
        img_data = save_image_to_db(file)
        if img_data:
            setattr(model_instance, f"{attribute_name}_data", img_data)
            setattr(model_instance, f"{attribute_name}_mime", get_mime_type(img_data))
        
        # Also save to file system for backward compatibility
        filename = secure_filename(file.filename)
        base, ext = os.path.splitext(filename)
        import datetime as dt
        timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
        unique_filename = f"{base}_{timestamp}{ext}"
        
        # Ensure directory exists
        from app.config import UPLOAD_FOLDER
        images_dir = os.path.join(UPLOAD_FOLDER, folder_name)
        os.makedirs(images_dir, exist_ok=True)
        
        # Save the file
        file_path = os.path.join(images_dir, unique_filename)
        file.save(file_path)
        
        # Update the database with the new image path
        setattr(model_instance, attribute_name, f'uploads/{folder_name}/{unique_filename}')
        
        return True
    except Exception as e:
        current_app.logger.error(f"Error handling image upload: {e}")
        return False

def get_image_url(model_instance, attribute_name='profile_image'):
    """
    Get the image URL for a model instance
    
    Args:
        model_instance: The model instance
        attribute_name: The attribute name
        
    Returns:
        The image URL
    """
    # First try to get the image from the database
    img_data = getattr(model_instance, f"{attribute_name}_data", None)
    img_mime = getattr(model_instance, f"{attribute_name}_mime", 'image/jpeg')
    
    if img_data:
        # Convert binary data to data URL
        try:
            base64_data = base64.b64encode(img_data).decode('utf-8')
            return f"data:{img_mime};base64,{base64_data}"
        except Exception as e:
            current_app.logger.error(f"Error converting image to data URL: {e}")
    
    # Fall back to file system
    img_path = getattr(model_instance, attribute_name, 'default.jpg')
    if img_path and img_path != 'default.jpg':
        from flask import url_for
        return url_for('static', filename=img_path)
    
    # Default image
    from flask import url_for
    return url_for('static', filename='uploads/default.jpg')
