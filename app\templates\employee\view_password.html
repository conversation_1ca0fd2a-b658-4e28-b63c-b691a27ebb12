{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">إعادة تعيين كلمة المرور</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير أمني:</strong> هذه الصفحة مخصصة للمسؤولين فقط. استخدام هذه الميزة يجب أن يكون محدودًا للحالات الضرورية فقط.
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> لا يمكن عرض كلمة المرور الحالية لأنها مخزنة بشكل مشفر في قاعدة البيانات. يمكنك فقط إعادة تعيين كلمة المرور إلى قيمة جديدة.
                    </div>

                    <div class="mb-4">
                        <h5>معلومات الموظف:</h5>
                        <p><strong>اسم المستخدم:</strong> {{ employee.username }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ employee.get_full_name() }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ employee.email }}</p>
                    </div>

                    {% if generated_password %}
                    <div class="alert alert-success">
                        <h5 class="alert-heading">تم إنشاء كلمة مرور جديدة!</h5>
                        <p>تم إعادة تعيين كلمة المرور للمستخدم <strong>{{ employee.username }}</strong> بنجاح.</p>
                        <hr>
                        <div class="d-flex align-items-center">
                            <div class="input-group">
                                <input type="text" class="form-control" id="password-display" value="{{ generated_password }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" id="copy-btn" onclick="copyPassword()">
                                    <i class="fas fa-copy"></i> نسخ
                                </button>
                            </div>
                        </div>
                        <p class="mt-2 mb-0">
                            <small class="text-muted">يرجى تسجيل كلمة المرور هذه أو نسخها الآن. لن تتمكن من رؤيتها مرة أخرى بعد مغادرة هذه الصفحة.</small>
                        </p>
                    </div>
                    {% endif %}

                    <form method="POST" action="{{ url_for('employee.view_password', id=employee.id) }}">
                        <div class="mb-3">
                            <label for="password_type" class="form-label">نوع كلمة المرور</label>
                            <select class="form-select" id="password_type" name="password_type">
                                <option value="random">إنشاء كلمة مرور عشوائية</option>
                                <option value="custom">تحديد كلمة مرور مخصصة</option>
                            </select>
                        </div>

                        <div class="mb-3" id="custom-password-div" style="display: none;">
                            <label for="custom_password" class="form-label">كلمة المرور المخصصة</label>
                            <input type="text" class="form-control" id="custom_password" name="custom_password">
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="require_change" name="require_change" checked>
                            <label class="form-check-label" for="require_change">
                                مطالبة المستخدم بتغيير كلمة المرور عند تسجيل الدخول التالي
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-key me-1"></i>إعادة تعيين كلمة المرور
                            </button>
                            <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Toggle custom password field based on selection
        $('#password_type').change(function() {
            if ($(this).val() === 'custom') {
                $('#custom-password-div').show();
            } else {
                $('#custom-password-div').hide();
            }
        });
    });

    function copyPassword() {
        var passwordField = document.getElementById("password-display");
        passwordField.select();
        document.execCommand("copy");

        // Change button text temporarily
        var copyBtn = document.getElementById("copy-btn");
        var originalHtml = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';

        setTimeout(function() {
            copyBtn.innerHTML = originalHtml;
        }, 2000);
    }
</script>
{% endblock %}
