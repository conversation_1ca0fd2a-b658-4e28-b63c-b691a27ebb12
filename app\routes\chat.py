from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app import db
from app.models import Project, ProjectMessage, User
from datetime import datetime

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/project/<int:project_id>/chat')
@login_required
def project_chat(project_id):
    project = Project.query.get_or_404(project_id)

    # Check if user has permission to view project chat
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or project in current_user.projects):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('project.index'))

    messages = project.messages.order_by(ProjectMessage.timestamp.asc()).all()

    return render_template('chat/project_chat.html', title=f'Chat - {project.name}',
                          project=project, messages=messages, now=datetime.utcnow())

@chat_bp.route('/project/<int:project_id>/send_message', methods=['POST'])
@login_required
def send_message(project_id):
    project = Project.query.get_or_404(project_id)

    # Check if user has permission to send messages
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or project in current_user.projects):
        return jsonify({'status': 'error', 'message': 'Permission denied'}), 403

    content = request.form.get('content')

    if not content or content.strip() == '':
        return jsonify({'status': 'error', 'message': 'Message cannot be empty'}), 400

    message = ProjectMessage(
        content=content,
        project_id=project_id,
        user_id=current_user.id
    )

    db.session.add(message)
    db.session.commit()

    # Return the message in JSON format for AJAX updates
    return jsonify({
        'status': 'success',
        'message': {
            'id': message.id,
            'content': message.content,
            'timestamp': message.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'user_id': message.user_id,
            'user_name': message.user.get_full_name(),
            'is_current_user': message.user_id == current_user.id
        }
    })

@chat_bp.route('/project/<int:project_id>/get_messages')
@login_required
def get_messages(project_id):
    project = Project.query.get_or_404(project_id)

    # Check if user has permission to view messages
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or project in current_user.projects):
        return jsonify({'status': 'error', 'message': 'Permission denied'}), 403

    # Get timestamp of last message the client has
    last_timestamp = request.args.get('last_timestamp')
    last_message_id = request.args.get('last_message_id', 0, type=int)

    # Query for new messages
    query = project.messages

    # Filter by timestamp if provided
    if last_timestamp:
        last_timestamp = datetime.strptime(last_timestamp, '%Y-%m-%d %H:%M:%S')
        query = query.filter(ProjectMessage.timestamp > last_timestamp)

    # Also filter by ID to ensure we don't get duplicates
    if last_message_id > 0:
        query = query.filter(ProjectMessage.id > last_message_id)

    # Get messages sorted by timestamp
    messages = query.order_by(ProjectMessage.timestamp.asc()).all()

    return jsonify({
        'status': 'success',
        'messages': [{
            'id': message.id,
            'content': message.content,
            'timestamp': message.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'user_id': message.user_id,
            'user_name': message.user.get_full_name(),
            'is_current_user': message.user_id == current_user.id
        } for message in messages]
    })
