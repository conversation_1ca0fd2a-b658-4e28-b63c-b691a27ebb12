{% extends 'base.html' %}

{% block styles %}
<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
<style>
    /* Mejoras generales */
    .fc {
        font-family: 'Cairo', sans-serif;
    }

    .fc-event {
        cursor: pointer;
        border-radius: 6px;
        border: none;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: transform 0.2s, box-shadow 0.2s;
        overflow: hidden;
    }

    .fc-event:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .fc-event-title {
        font-weight: 600;
        padding: 2px 4px;
    }

    /* Mejoras en la barra de herramientas */
    .fc-toolbar-title {
        font-size: 1.8rem !important;
        font-weight: 700;
        color: #1765A0;
    }

    .fc-button-primary {
        background-color: #1765A0 !important;
        border-color: #1765A0 !important;
        box-shadow: none !important;
        transition: all 0.3s;
    }

    .fc-button-primary:hover {
        background-color: #0f4c75 !important;
        border-color: #0f4c75 !important;
    }

    .fc-button-active {
        background-color: #EABF54 !important;
        border-color: #EABF54 !important;
        color: #333 !important;
    }

    /* Mejoras en las celdas del calendario */
    .fc-daygrid-day {
        transition: background-color 0.2s;
    }

    .fc-daygrid-day:hover {
        background-color: #f8f9fa;
    }

    .fc-day-today {
        background-color: rgba(23, 101, 160, 0.1) !important;
    }

    .fc-day-today .fc-daygrid-day-number {
        background-color: #1765A0;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 5px;
    }

    /* Tooltip mejorado */
    .event-tooltip {
        position: absolute;
        z-index: 10001;
        background: white;
        border: none;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        max-width: 300px;
        font-family: 'Cairo', sans-serif;
        animation: fadeIn 0.2s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Filtros de tipo de evento */
    .event-type-filter {
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-badge {
        padding: 8px 15px;
        border-radius: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .filter-badge.active {
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .filter-badge.project {
        background-color: #1765A0;
        color: white;
    }

    .filter-badge.task {
        background-color: #EABF54;
        color: #333;
    }

    .filter-badge.leave {
        background-color: #28a745;
        color: white;
    }

    .filter-badge.meeting {
        background-color: #dc3545;
        color: white;
    }

    .filter-badge.project.inactive {
        background-color: #e9ecef;
        color: #6c757d;
    }

    .filter-badge.task.inactive {
        background-color: #e9ecef;
        color: #6c757d;
    }

    .filter-badge.leave.inactive {
        background-color: #e9ecef;
        color: #6c757d;
    }

    .filter-badge.meeting.inactive {
        background-color: #e9ecef;
        color: #6c757d;
    }

    /* Contenedor del calendario */
    .calendar-container {
        height: 800px;
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 0 20px rgba(0,0,0,0.05);
    }

    /* Modal de eventos */
    #eventModal .modal-content {
        border-radius: 10px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    #eventModal .modal-header {
        border-bottom: none;
        padding: 20px 25px;
    }

    #eventModal .modal-body {
        padding: 10px 25px 20px;
    }

    #eventModal .modal-footer {
        border-top: none;
        padding: 15px 25px 20px;
    }

    /* Custom button styles for FullCalendar */
    .fc-prev-button .fc-icon,
    .fc-next-button .fc-icon {
        font-family: 'Font Awesome 6 Free' !important;
        font-weight: 900;
    }

    .fc-icon-chevron-right:before {
        content: "\f053" !important; /* fa-chevron-left */
    }

    .fc-icon-chevron-left:before {
        content: "\f054" !important; /* fa-chevron-right */
    }

    /* Today button */
    .fc-today-button {
        font-family: 'Cairo', sans-serif !important;
    }

    /* View buttons */
    .fc-dayGridMonth-button,
    .fc-timeGridWeek-button,
    .fc-timeGridDay-button {
        font-family: 'Cairo', sans-serif !important;
    }

    /* Fix button icons */
    .fc-button .fc-icon {
        vertical-align: middle;
        font-size: 1.2em;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .calendar-container {
            height: 600px;
            padding: 10px;
        }

        .fc-toolbar-title {
            font-size: 1.4rem !important;
        }

        .filter-badge {
            padding: 6px 12px;
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">التقويم الشهري</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">أحداث {{ current_user.get_full_name() }}</h5>
                </div>
                <div class="col-md-6">
                    <div class="event-type-filter d-flex flex-wrap justify-content-md-end">
                        <div class="filter-badge project active" id="filter-project" data-type="project">
                            <i class="fas fa-project-diagram"></i>
                            <span>المشاريع</span>
                        </div>
                        <div class="filter-badge task active" id="filter-task" data-type="task">
                            <i class="fas fa-tasks"></i>
                            <span>المهام</span>
                        </div>
                        <div class="filter-badge leave active" id="filter-leave" data-type="leave">
                            <i class="fas fa-umbrella-beach"></i>
                            <span>الإجازات</span>
                        </div>
                        <div class="filter-badge meeting active" id="filter-meeting" data-type="meeting">
                            <i class="fas fa-users"></i>
                            <span>الاجتماعات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="calendar-container" id="calendar"></div>
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="event-header">
                <h5 class="modal-title" id="eventModalLabel">تفاصيل الحدث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center mb-4">
                    <div class="event-icon-container me-3" id="event-icon-container">
                        <i class="fas fa-calendar-day fa-2x" id="event-icon"></i>
                    </div>
                    <div>
                        <h4 class="mb-1" id="event-title"></h4>
                        <div class="text-muted" id="event-date"></div>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">
                            <i class="fas fa-align-left me-2"></i>الوصف
                        </h6>
                        <p class="card-text" id="event-description"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إغلاق
                </button>
                <a href="#" class="btn btn-primary" id="event-link">
                    <i class="fas fa-external-link-alt me-1"></i>عرض التفاصيل
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/locales/ar.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltip element
        let tooltip = document.createElement('div');
        tooltip.classList.add('event-tooltip');
        tooltip.style.display = 'none';
        document.body.appendChild(tooltip);

        // Fix for FullCalendar icons
        document.addEventListener('DOMNodeInserted', function(e) {
            if (e.target.classList && e.target.classList.contains('fc-icon')) {
                // Fix icon alignment
                e.target.style.lineHeight = '1';
                e.target.style.verticalAlign = 'middle';
            }
        });

        // Initialize calendar
        const calendarEl = document.getElementById('calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                day: 'يوم'
            },
            events: "{{ url_for('calendar.get_events') }}",
            eventTimeFormat: {
                hour: '2-digit',
                minute: '2-digit',
                meridiem: false,
                hour12: false
            },
            eventClick: function(info) {
                // Show event details in modal
                const event = info.event;
                const modal = new bootstrap.Modal(document.getElementById('eventModal'));
                const eventType = event.extendedProps.type;

                // Set event title and description
                document.getElementById('event-title').textContent = event.title;
                document.getElementById('event-description').textContent = event.extendedProps.description || 'لا يوجد وصف';

                // Set event icon based on type
                const iconElement = document.getElementById('event-icon');
                const iconContainer = document.getElementById('event-icon-container');

                // Reset classes
                iconElement.className = 'fas fa-2x';
                iconContainer.style.backgroundColor = event.backgroundColor;
                iconContainer.style.color = '#fff';
                iconContainer.style.width = '50px';
                iconContainer.style.height = '50px';
                iconContainer.style.borderRadius = '50%';
                iconContainer.style.display = 'flex';
                iconContainer.style.alignItems = 'center';
                iconContainer.style.justifyContent = 'center';

                // Set header color based on event type
                const headerElement = document.getElementById('event-header');
                headerElement.style.backgroundColor = event.backgroundColor;
                headerElement.style.color = '#fff';

                // Set icon based on event type
                switch(eventType) {
                    case 'project':
                        iconElement.classList.add('fa-project-diagram');
                        break;
                    case 'task':
                        iconElement.classList.add('fa-tasks');
                        iconContainer.style.color = '#333'; // Dark text for gold background
                        break;
                    case 'leave':
                        iconElement.classList.add('fa-umbrella-beach');
                        break;
                    case 'meeting':
                        iconElement.classList.add('fa-users');
                        break;
                    default:
                        iconElement.classList.add('fa-calendar-day');
                }

                // Format date
                let dateText = '';
                if (event.allDay) {
                    if (event.end) {
                        // Multi-day event
                        const startDate = new Date(event.start);
                        const endDate = new Date(event.end);
                        dateText = `${startDate.toLocaleDateString('ar-SA')} إلى ${endDate.toLocaleDateString('ar-SA')}`;
                    } else {
                        // Single day event
                        dateText = new Date(event.start).toLocaleDateString('ar-SA');
                    }
                } else {
                    // Event with time
                    const startDate = new Date(event.start);
                    const endDate = event.end ? new Date(event.end) : null;

                    const formatTime = (date) => {
                        return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
                    };

                    if (endDate) {
                        dateText = `${startDate.toLocaleDateString('ar-SA')} من ${formatTime(startDate)} إلى ${formatTime(endDate)}`;
                    } else {
                        dateText = `${startDate.toLocaleDateString('ar-SA')} الساعة ${formatTime(startDate)}`;
                    }
                }

                document.getElementById('event-date').textContent = dateText;

                // Set link
                const linkElement = document.getElementById('event-link');
                if (event.url) {
                    linkElement.href = event.url;
                    linkElement.style.display = 'block';
                } else {
                    linkElement.style.display = 'none';
                }

                modal.show();

                // Prevent navigation
                info.jsEvent.preventDefault();
            },
            eventMouseEnter: function(info) {
                // Show tooltip on hover
                const event = info.event;
                tooltip.innerHTML = `
                    <strong>${event.title}</strong>
                    ${event.extendedProps.description ? `<br>${event.extendedProps.description.substring(0, 100)}${event.extendedProps.description.length > 100 ? '...' : ''}` : ''}
                `;
                tooltip.style.display = 'block';
                positionTooltip(info.jsEvent);
            },
            eventMouseLeave: function() {
                // Hide tooltip
                tooltip.style.display = 'none';
            }
        });

        calendar.render();

        // Position tooltip near mouse
        function positionTooltip(event) {
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY + 10) + 'px';
        }

        // Track mouse movement for tooltip
        document.addEventListener('mousemove', function(e) {
            if (tooltip.style.display === 'block') {
                positionTooltip(e);
            }
        });

        // Event type filtering with new badge style
        document.querySelectorAll('.filter-badge').forEach(badge => {
            badge.addEventListener('click', function() {
                const eventType = this.dataset.type;
                const isActive = this.classList.contains('active');

                // Toggle active state
                if (isActive) {
                    this.classList.remove('active');
                    this.classList.add('inactive');
                } else {
                    this.classList.add('active');
                    this.classList.remove('inactive');
                }

                // Get all events
                const events = calendar.getEvents();

                // Filter events
                events.forEach(event => {
                    if (event.extendedProps.type === eventType) {
                        if (!isActive) { // We're activating it (it was inactive)
                            event.setProp('display', 'auto');
                        } else { // We're deactivating it (it was active)
                            event.setProp('display', 'none');
                        }
                    }
                });
            });
        });

        // Add calendar to dashboard quick links
        if (window.parent.document.querySelector('.dashboard-quick-links')) {
            const quickLinks = window.parent.document.querySelector('.dashboard-quick-links');
            if (!quickLinks.querySelector('a[href="{{ url_for("calendar.index") }}"]')) {
                const calendarLink = document.createElement('a');
                calendarLink.href = "{{ url_for('calendar.index') }}";
                calendarLink.className = 'btn btn-outline-primary';
                calendarLink.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>التقويم';
                quickLinks.appendChild(calendarLink);
            }
        }
    });
</script>
{% endblock %}
