{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>الإشعارات</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الإشعارات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">جميع الإشعارات</h5>
                    <div>
                        <a href="{{ url_for('notification.unread') }}" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-filter me-1"></i>الإشعارات غير المقروءة
                        </a>
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <a href="{{ url_for('notification.send') }}" class="btn btn-sm btn-outline-info me-2">
                            <i class="fas fa-paper-plane me-1"></i>إرسال إشعار
                        </a>
                        {% endif %}
                        <form action="{{ url_for('notification.mark_all_read') }}" method="POST" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-outline-success me-2">
                                <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
                            </button>
                        </form>
                        <form action="{{ url_for('notification.delete_all') }}" method="POST" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف جميع الإشعارات؟')">
                                <i class="fas fa-trash me-1"></i>حذف الكل
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('notification.index') }}" class="mb-4">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بالعنوان أو المحتوى" value="{{ search_query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    {% if search_query %}
                                    <a href="{{ url_for('notification.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="notification_type" name="notification_type" onchange="this.form.submit()">
                                    <option value="" {% if not notification_type %}selected{% endif %}>جميع الأنواع</option>
                                    {% for type in notification_types %}
                                    <option value="{{ type }}" {% if notification_type == type %}selected{% endif %}>{{ type }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="is_read" name="is_read" onchange="this.form.submit()">
                                    <option value="" {% if not is_read %}selected{% endif %}>جميع الحالات</option>
                                    <option value="read" {% if is_read == 'read' %}selected{% endif %}>مقروءة</option>
                                    <option value="unread" {% if is_read == 'unread' %}selected{% endif %}>غير مقروءة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 إشعار</option>
                                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 إشعار</option>
                                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 إشعار</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">من تاريخ</span>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">إلى تاريخ</span>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ url_for('notification.index') }}" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if notifications.items %}
                    <div class="list-group">
                        {% for notification in notifications.items %}
                        <div class="list-group-item list-group-item-action {% if not notification.is_read %}bg-light{% endif %}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ notification.title }}</h5>
                                <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            {% if notification.link_url %}
                            <div class="mt-2">
                                <a href="{{ notification.link_url }}" class="btn btn-sm btn-outline-secondary" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>{{ notification.link_text or 'فتح الرابط' }}
                                </a>
                            </div>
                            {% endif %}
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <small class="text-muted">
                                    {% if notification.notification_type %}
                                    <span class="badge bg-info">{{ notification.notification_type }}</span>
                                    {% endif %}

                                    {% if notification.related_project %}
                                    <a href="{{ url_for('project.view', id=notification.related_project.id) }}" class="text-decoration-none">
                                        <span class="badge bg-primary">{{ notification.related_project.name }}</span>
                                    </a>
                                    {% endif %}

                                    {% if notification.related_task %}
                                    <a href="{{ url_for('project.tasks', id=notification.related_task.project_id) }}" class="text-decoration-none">
                                        <span class="badge bg-secondary">{{ notification.related_task.title }}</span>
                                    </a>
                                    {% endif %}
                                </small>
                                <div>
                                    {% if not notification.is_read %}
                                    <form action="{{ url_for('notification.mark_read', id=notification.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-check me-1"></i>تحديد كمقروء
                                        </button>
                                    </form>
                                    {% endif %}
                                    <form action="{{ url_for('notification.delete', id=notification.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا الإشعار؟')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            عرض {{ notifications.items|length }} من {{ notifications.total }} إشعار
                            {% if search_query %}
                            <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                            {% endif %}
                            {% if notification_type %}
                            <span class="text-muted">(النوع: {{ notification_type }})</span>
                            {% endif %}
                            {% if is_read %}
                            <span class="text-muted">
                                (الحالة:
                                {% if is_read == 'read' %}مقروءة{% endif %}
                                {% if is_read == 'unread' %}غير مقروءة{% endif %}
                                )
                            </span>
                            {% endif %}
                        </div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                {% if notifications.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('notification.index', page=notifications.prev_num, per_page=current_per_page, search=search_query, notification_type=notification_type, is_read=is_read, date_from=date_from, date_to=date_to) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% set start_page = notifications.page - 2 if notifications.page > 2 else 1 %}
                                {% set end_page = start_page + 4 if start_page + 4 <= notifications.pages else notifications.pages %}
                                {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                                {% for page_num in range(start_page, end_page + 1) %}
                                <li class="page-item {% if page_num == notifications.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('notification.index', page=page_num, per_page=current_per_page, search=search_query, notification_type=notification_type, is_read=is_read, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                                </li>
                                {% endfor %}

                                {% if notifications.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('notification.index', page=notifications.next_num, per_page=current_per_page, search=search_query, notification_type=notification_type, is_read=is_read, date_from=date_from, date_to=date_to) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        {% if search_query or notification_type or is_read or date_from or date_to %}
                        لا توجد نتائج مطابقة للبحث.
                        <a href="{{ url_for('notification.index') }}" class="alert-link">عرض جميع الإشعارات</a>
                        {% else %}
                        لا توجد إشعارات حاليًا.
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
