from datetime import datetime
from app import db

class Penalty(db.Model):
    __tablename__ = 'penalties'

    id = db.<PERSON>umn(db.<PERSON>teger, primary_key=True)
    penalty_type = db.Column(db.String(50), nullable=False)  # verbal_warning, written_warning, written_notice, suspension, final_warning, termination
    reason = db.Column(db.Text, nullable=False)
    details = db.Column(db.Text)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)  # Optional, for temporary penalties
    salary_deduction = db.Column(db.Float)  # Optional, for financial penalties
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=False)
    user = db.relationship('User', foreign_keys=[user_id], backref='penalties')

    issued_by_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    issued_by = db.relationship('User', foreign_keys=[issued_by_id], backref='issued_penalties')

    # Attachments for this penalty
    attachments = db.relationship('PenaltyAttachment', backref='penalty', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Penalty {self.id}: {self.penalty_type} for {self.user.get_full_name()}>'

    def is_active(self):
        """Check if the penalty is currently active"""
        today = datetime.now().date()
        if self.end_date:
            return self.start_date <= today <= self.end_date
        return self.start_date <= today  # Permanent penalty

    def get_penalty_type_display(self):
        """Return the human-readable name of the penalty type"""
        penalty_types = {
            'verbal_warning': 'لفت نظر شفوي',
            'written_warning': 'لفت نظر كتابي (تحذير أول)',
            'written_notice': 'إنذار كتابي (تحذير ثاني)',
            'suspension': 'إيقاف مؤقت عن العمل / خصم من الراتب',
            'final_warning': 'الإنذار النهائي',
            'termination': 'الفصل من العمل / إنهاء التعاقد'
        }
        return penalty_types.get(self.penalty_type, self.penalty_type)

class PenaltyAttachment(db.Model):
    __tablename__ = 'penalty_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    penalty_id = db.Column(db.Integer, db.ForeignKey('penalties.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='penalty_attachments')

    def __repr__(self):
        return f'<PenaltyAttachment {self.filename}>'
