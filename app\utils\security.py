import re
import secrets
import string
from functools import wraps
from flask import request, abort, current_app, session
from flask_login import current_user
import bleach
from bleach.sanitizer import ALLOWED_TAGS, ALLOWED_ATTRIBUTES
import hashlib
import hmac
import base64

# Extended allowed HTML tags for rich text fields
EXTENDED_ALLOWED_TAGS = list(ALLOWED_TAGS) + ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'br', 'hr', 'pre', 'code', 'blockquote', 'div', 'span', 'ul', 'ol', 'li', 'table', 'thead', 'tbody', 'tr', 'th', 'td']
EXTENDED_ALLOWED_ATTRIBUTES = dict(ALLOWED_ATTRIBUTES)
EXTENDED_ALLOWED_ATTRIBUTES.update({
    'a': ['href', 'title', 'target', 'rel'],
    'img': ['src', 'alt', 'title', 'width', 'height'],
    'div': ['class', 'id', 'style'],
    'span': ['class', 'id', 'style'],
    'table': ['class', 'id', 'border', 'cellpadding', 'cellspacing'],
    'th': ['scope', 'colspan', 'rowspan'],
    'td': ['colspan', 'rowspan'],
    '*': ['class', 'id']
})

def sanitize_html(html_content, extended=False):
    """
    Sanitize HTML content to prevent XSS attacks
    
    Args:
        html_content: The HTML content to sanitize
        extended: Whether to use extended allowed tags and attributes
        
    Returns:
        Sanitized HTML content
    """
    if not html_content:
        return ''
    
    if extended:
        return bleach.clean(
            html_content,
            tags=EXTENDED_ALLOWED_TAGS,
            attributes=EXTENDED_ALLOWED_ATTRIBUTES,
            strip=True
        )
    else:
        return bleach.clean(html_content)

def sanitize_filename(filename):
    """
    Sanitize a filename to prevent path traversal attacks
    
    Args:
        filename: The filename to sanitize
        
    Returns:
        Sanitized filename
    """
    # Remove any directory components
    filename = re.sub(r'[/\\]', '', filename)
    
    # Remove any null bytes
    filename = filename.replace('\0', '')
    
    # Limit to alphanumeric characters, dots, dashes, and underscores
    filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
    
    # Ensure the filename is not empty
    if not filename:
        filename = 'unnamed_file'
    
    return filename

def generate_csrf_token():
    """
    Generate a CSRF token for form protection
    
    Returns:
        CSRF token
    """
    if '_csrf_token' not in session:
        session['_csrf_token'] = secrets.token_hex(32)
    return session['_csrf_token']

def validate_csrf_token(token):
    """
    Validate a CSRF token
    
    Args:
        token: The token to validate
        
    Returns:
        True if valid, False otherwise
    """
    session_token = session.get('_csrf_token', None)
    if not session_token:
        return False
    
    # Use constant-time comparison to prevent timing attacks
    return hmac.compare_digest(session_token, token)

def require_csrf_token(f):
    """
    Decorator to require a valid CSRF token for POST requests
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'POST':
            token = request.form.get('_csrf_token')
            if not token or not validate_csrf_token(token):
                abort(403)
        return f(*args, **kwargs)
    return decorated_function

def generate_secure_password(length=12):
    """
    Generate a secure random password
    
    Args:
        length: Length of the password
        
    Returns:
        Secure random password
    """
    alphabet = string.ascii_letters + string.digits + string.punctuation
    while True:
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        # Ensure password has at least one lowercase, one uppercase, one digit, and one special character
        if (any(c.islower() for c in password) and
            any(c.isupper() for c in password) and
            any(c.isdigit() for c in password) and
            any(c in string.punctuation for c in password)):
            break
    return password

def hash_password(password, salt=None):
    """
    Hash a password using a secure algorithm
    
    Args:
        password: The password to hash
        salt: Optional salt to use
        
    Returns:
        Tuple of (hash, salt)
    """
    if salt is None:
        salt = secrets.token_hex(16)
    
    # Use a secure hashing algorithm (PBKDF2)
    key = hashlib.pbkdf2_hmac(
        'sha256',
        password.encode('utf-8'),
        salt.encode('utf-8'),
        100000,  # Number of iterations
        dklen=32  # Length of the derived key
    )
    
    return base64.b64encode(key).decode('utf-8'), salt

def verify_password(password, stored_hash, salt):
    """
    Verify a password against a stored hash
    
    Args:
        password: The password to verify
        stored_hash: The stored hash
        salt: The salt used for hashing
        
    Returns:
        True if the password matches, False otherwise
    """
    calculated_hash, _ = hash_password(password, salt)
    return hmac.compare_digest(calculated_hash, stored_hash)

def rate_limit_ip(ip_address, limit=100, period=60):
    """
    Check if an IP address has exceeded the rate limit
    
    Args:
        ip_address: The IP address to check
        limit: Maximum number of requests allowed in the period
        period: Time period in seconds
        
    Returns:
        True if the IP is allowed, False if it has exceeded the limit
    """
    # This is a placeholder. In a real implementation, you would use Redis or a similar
    # service to track request counts per IP address.
    return True

def is_safe_redirect_url(url):
    """
    Check if a URL is safe to redirect to (prevents open redirect vulnerabilities)
    
    Args:
        url: The URL to check
        
    Returns:
        True if the URL is safe, False otherwise
    """
    if not url:
        return False
    
    # Check if the URL is relative
    if url.startswith('/'):
        return True
    
    # Check if the URL is for the same host
    allowed_hosts = [current_app.config.get('SERVER_NAME', 'localhost')]
    from urllib.parse import urlparse
    parsed_url = urlparse(url)
    
    return parsed_url.netloc in allowed_hosts

def validate_input(value, pattern=None, min_length=None, max_length=None):
    """
    Validate user input against various criteria
    
    Args:
        value: The input value to validate
        pattern: Optional regex pattern to match
        min_length: Optional minimum length
        max_length: Optional maximum length
        
    Returns:
        True if valid, False otherwise
    """
    if value is None:
        return False
    
    if min_length is not None and len(value) < min_length:
        return False
    
    if max_length is not None and len(value) > max_length:
        return False
    
    if pattern is not None and not re.match(pattern, value):
        return False
    
    return True

def secure_headers():
    """
    Generate secure HTTP headers to prevent common web vulnerabilities
    
    Returns:
        Dictionary of headers to add to responses
    """
    return {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-XSS-Protection': '1; mode=block',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net https://code.jquery.com 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self'",
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    }

def add_security_headers(response):
    """
    Add security headers to a Flask response
    
    Args:
        response: The Flask response object
        
    Returns:
        Modified response with security headers
    """
    headers = secure_headers()
    for header, value in headers.items():
        response.headers[header] = value
    return response
