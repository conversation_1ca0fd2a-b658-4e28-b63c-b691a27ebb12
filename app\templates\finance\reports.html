{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>التقارير المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">التقارير</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تقارير مالية</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الإيرادات والمصروفات</h5>
                                    <p class="card-text">عرض تقرير مفصل للإيرادات والمصروفات خلال فترة زمنية محددة.</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#incomeExpenseReportModal">
                                        عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير الفواتير</h5>
                                    <p class="card-text">عرض تقرير مفصل للفواتير حسب الحالة والفترة الزمنية.</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#invoicesReportModal">
                                        عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير المشاريع المالي</h5>
                                    <p class="card-text">عرض تقرير مالي مفصل للمشاريع وتكاليفها وإيراداتها.</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#projectsReportModal">
                                        عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">تقرير العملاء المالي</h5>
                                    <p class="card-text">عرض تقرير مالي مفصل للعملاء والمبالغ المدفوعة والمستحقة.</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#clientsReportModal">
                                        عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Results Section -->
    <div class="row mb-4" id="reportResultsSection" style="display: none;">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" id="reportTitle">نتائج التقرير</h5>
                    <button class="btn btn-sm btn-primary" id="exportReportBtn">
                        <i class="fas fa-download me-1"></i>تصدير التقرير
                    </button>
                </div>
                <div class="card-body">
                    <div id="reportResults"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Income/Expense Report Modal -->
<div class="modal fade" id="incomeExpenseReportModal" tabindex="-1" aria-labelledby="incomeExpenseReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="incomeExpenseReportModalLabel">تقرير الإيرادات والمصروفات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="incomeExpenseReportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reportPeriod" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="reportPeriod" name="reportPeriod">
                            <option value="this_month">الشهر الحالي</option>
                            <option value="last_month">الشهر الماضي</option>
                            <option value="this_quarter">الربع الحالي</option>
                            <option value="last_quarter">الربع الماضي</option>
                            <option value="this_year">السنة الحالية</option>
                            <option value="last_year">السنة الماضية</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="row custom-date-range" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="startDate" name="startDate">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="endDate" name="endDate">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="groupBy" class="form-label">تجميع حسب</label>
                        <select class="form-select" id="groupBy" name="groupBy">
                            <option value="day">اليوم</option>
                            <option value="week">الأسبوع</option>
                            <option value="month" selected>الشهر</option>
                            <option value="quarter">الربع</option>
                            <option value="year">السنة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="categoryFilter" class="form-label">تصفية حسب الفئة (اختياري)</label>
                        <select class="form-select" id="categoryFilter" name="categoryFilter">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                            <option value="{{ category }}">{{ category }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Invoices Report Modal -->
<div class="modal fade" id="invoicesReportModal" tabindex="-1" aria-labelledby="invoicesReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoicesReportModalLabel">تقرير الفواتير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="invoicesReportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="invoiceStatus" class="form-label">حالة الفاتورة</label>
                        <select class="form-select" id="invoiceStatus" name="invoiceStatus">
                            <option value="all">جميع الحالات</option>
                            <option value="paid">مدفوعة</option>
                            <option value="pending">معلقة</option>
                            <option value="overdue">متأخرة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="invoicePeriod" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="invoicePeriod" name="invoicePeriod">
                            <option value="this_month">الشهر الحالي</option>
                            <option value="last_month">الشهر الماضي</option>
                            <option value="this_quarter">الربع الحالي</option>
                            <option value="last_quarter">الربع الماضي</option>
                            <option value="this_year">السنة الحالية</option>
                            <option value="last_year">السنة الماضية</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="row invoice-custom-date-range" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="invoiceStartDate" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="invoiceStartDate" name="invoiceStartDate">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="invoiceEndDate" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="invoiceEndDate" name="invoiceEndDate">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="clientFilter" class="form-label">تصفية حسب العميل (اختياري)</label>
                        <select class="form-select" id="clientFilter" name="clientFilter">
                            <option value="">جميع العملاء</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Projects Report Modal -->
<div class="modal fade" id="projectsReportModal" tabindex="-1" aria-labelledby="projectsReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectsReportModalLabel">تقرير المشاريع المالي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="projectsReportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="projectStatus" class="form-label">حالة المشروع</label>
                        <select class="form-select" id="projectStatus" name="projectStatus">
                            <option value="all">جميع الحالات</option>
                            <option value="pending">معلق</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="departmentFilter" class="form-label">تصفية حسب القسم (اختياري)</label>
                        <select class="form-select" id="departmentFilter" name="departmentFilter">
                            <option value="">جميع الأقسام</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="projectClientFilter" class="form-label">تصفية حسب العميل (اختياري)</label>
                        <select class="form-select" id="projectClientFilter" name="projectClientFilter">
                            <option value="">جميع العملاء</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Clients Report Modal -->
<div class="modal fade" id="clientsReportModal" tabindex="-1" aria-labelledby="clientsReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clientsReportModalLabel">تقرير العملاء المالي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="clientsReportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="clientReportType" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="clientReportType" name="clientReportType">
                            <option value="all">جميع المعلومات المالية</option>
                            <option value="invoices">الفواتير فقط</option>
                            <option value="payments">المدفوعات فقط</option>
                            <option value="outstanding">المبالغ المستحقة فقط</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="clientReportFilter" class="form-label">تصفية حسب العميل (اختياري)</label>
                        <select class="form-select" id="clientReportFilter" name="clientReportFilter">
                            <option value="">جميع العملاء</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="clientPeriod" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="clientPeriod" name="clientPeriod">
                            <option value="this_year">السنة الحالية</option>
                            <option value="last_year">السنة الماضية</option>
                            <option value="all_time">كل الوقت</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="row client-custom-date-range" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="clientStartDate" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="clientStartDate" name="clientStartDate">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="clientEndDate" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="clientEndDate" name="clientEndDate">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">عرض التقرير</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle custom date range for income/expense report
        const reportPeriod = document.getElementById('reportPeriod');
        const customDateRange = document.querySelector('.custom-date-range');

        if (reportPeriod) {
            reportPeriod.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateRange.style.display = 'flex';
                } else {
                    customDateRange.style.display = 'none';
                }
            });
        }

        // Handle custom date range for invoices report
        const invoicePeriod = document.getElementById('invoicePeriod');
        const invoiceCustomDateRange = document.querySelector('.invoice-custom-date-range');

        if (invoicePeriod) {
            invoicePeriod.addEventListener('change', function() {
                if (this.value === 'custom') {
                    invoiceCustomDateRange.style.display = 'flex';
                } else {
                    invoiceCustomDateRange.style.display = 'none';
                }
            });
        }

        // Handle custom date range for clients report
        const clientPeriod = document.getElementById('clientPeriod');
        const clientCustomDateRange = document.querySelector('.client-custom-date-range');

        if (clientPeriod) {
            clientPeriod.addEventListener('change', function() {
                if (this.value === 'custom') {
                    clientCustomDateRange.style.display = 'flex';
                } else {
                    clientCustomDateRange.style.display = 'none';
                }
            });
        }

        // Handle form submissions
        const incomeExpenseReportForm = document.getElementById('incomeExpenseReportForm');
        const invoicesReportForm = document.getElementById('invoicesReportForm');
        const projectsReportForm = document.getElementById('projectsReportForm');
        const clientsReportForm = document.getElementById('clientsReportForm');

        if (incomeExpenseReportForm) {
            incomeExpenseReportForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showReportResults('تقرير الإيرادات والمصروفات', 'هذا تقرير تجريبي للإيرادات والمصروفات.');

                // Close the modal
                const modalElement = document.getElementById('incomeExpenseReportModal');
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                } else {
                    $(modalElement).modal('hide');
                }
            });
        }

        if (invoicesReportForm) {
            invoicesReportForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showReportResults('تقرير الفواتير', 'هذا تقرير تجريبي للفواتير.');

                // Close the modal
                const modalElement = document.getElementById('invoicesReportModal');
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                } else {
                    $(modalElement).modal('hide');
                }
            });
        }

        if (projectsReportForm) {
            projectsReportForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showReportResults('تقرير المشاريع المالي', 'هذا تقرير تجريبي للمشاريع المالية.');

                // Close the modal
                const modalElement = document.getElementById('projectsReportModal');
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                } else {
                    $(modalElement).modal('hide');
                }
            });
        }

        if (clientsReportForm) {
            clientsReportForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showReportResults('تقرير العملاء المالي', 'هذا تقرير تجريبي للعملاء المالي.');

                // Close the modal
                const modalElement = document.getElementById('clientsReportModal');
                const bsModal = bootstrap.Modal.getInstance(modalElement);
                if (bsModal) {
                    bsModal.hide();
                } else {
                    $(modalElement).modal('hide');
                }
            });
        }

        // Function to show report results
        function showReportResults(title, content) {
            const reportResultsSection = document.getElementById('reportResultsSection');
            const reportTitle = document.getElementById('reportTitle');
            const reportResults = document.getElementById('reportResults');

            reportTitle.textContent = title;
            reportResults.innerHTML = `
                <div class="alert alert-info">
                    ${content}
                </div>
                <p>لا توجد بيانات كافية لإنشاء تقرير. يرجى إضافة المزيد من البيانات المالية أولاً.</p>
            `;

            reportResultsSection.style.display = 'block';
        }

        // Handle export report button
        const exportReportBtn = document.getElementById('exportReportBtn');
        if (exportReportBtn) {
            exportReportBtn.addEventListener('click', function() {
                alert('سيتم تنفيذ وظيفة تصدير التقارير في الإصدار القادم.');
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
