from app import db
from app.models.project import ProjectFile
import sqlite3
import os
from app.config import SQLALCHEMY_DATABASE_URI

def run_migration():
    """
    Add notes column to project_files table
    """
    print("Running migration: add_notes_to_project_files.py")
    
    # Extract database path from URI
    db_path = SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')
    
    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if the notes column already exists
    cursor.execute("PRAGMA table_info(project_files)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]
    
    if 'notes' not in column_names:
        # Add the notes column
        cursor.execute("ALTER TABLE project_files ADD COLUMN notes TEXT")
        conn.commit()
        print("Added notes column to project_files table")
    else:
        print("notes column already exists in project_files table")
    
    # Close the connection
    conn.close()
    
    print("Migration completed: add_notes_to_project_files.py")

if __name__ == "__main__":
    run_migration()
