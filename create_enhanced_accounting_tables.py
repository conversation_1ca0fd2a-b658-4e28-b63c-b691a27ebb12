# إنشاء جداول النظام المالي المتكامل
import sqlite3
from datetime import datetime, date

def create_enhanced_accounting_tables():
    """إنشاء جداول النظام المالي المتكامل"""
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        print("بدء إنشاء جداول النظام المالي المتكامل...")
        
        # 1. إنشاء جدول شجرة الحسابات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chart_of_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code VARCHAR(20) UNIQUE NOT NULL,
                account_name VARCHAR(255) NOT NULL,
                account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('Asset', 'Liability', 'Equity', 'Revenue', 'Expense')),
                parent_account_id INTEGER,
                balance DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
            )
        ''')
        print("✓ تم إنشاء جدول شجرة الحسابات")
        
        # 2. إنشاء جدول قيود اليومية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number VARCHAR(50) UNIQUE NOT NULL,
                entry_date DATE NOT NULL,
                description TEXT NOT NULL,
                source_document_type VARCHAR(50),
                source_document_id INTEGER,
                total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
                total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'posted', 'cancelled')),
                created_by_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        ''')
        print("✓ تم إنشاء جدول قيود اليومية")
        
        # 3. إنشاء جدول بنود قيود اليومية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                debit DECIMAL(15,2) DEFAULT 0,
                credit DECIMAL(15,2) DEFAULT 0,
                description TEXT,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
            )
        ''')
        print("✓ تم إنشاء جدول بنود قيود اليومية")
        
        # 4. إنشاء جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                company VARCHAR(255),
                email VARCHAR(120),
                phone VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                payment_terms INTEGER DEFAULT 30,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✓ تم إنشاء جدول الموردين")
        
        # 5. إنشاء جدول فواتير الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendor_bills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vendor_id INTEGER NOT NULL,
                bill_number VARCHAR(100) NOT NULL,
                vendor_reference VARCHAR(100),
                issue_date DATE NOT NULL,
                due_date DATE NOT NULL,
                subtotal DECIMAL(15,2) NOT NULL,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                amount_due DECIMAL(15,2) NOT NULL,
                status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'to_pay', 'paid', 'cancelled')),
                journal_entry_id INTEGER,
                created_by_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (vendor_id) REFERENCES vendors(id),
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        ''')
        print("✓ تم إنشاء جدول فواتير الموردين")
        
        # 6. إنشاء جدول بنود فواتير الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendor_bill_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bill_id INTEGER NOT NULL,
                description TEXT NOT NULL,
                quantity DECIMAL(10,2) DEFAULT 1,
                unit_price DECIMAL(15,2) NOT NULL,
                subtotal DECIMAL(15,2) NOT NULL,
                account_id INTEGER NOT NULL,
                FOREIGN KEY (bill_id) REFERENCES vendor_bills(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
            )
        ''')
        print("✓ تم إنشاء جدول بنود فواتير الموردين")
        
        # 7. إنشاء جدول الدفعات المستلمة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments_received (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                invoice_id INTEGER,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_date DATE NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('cash', 'bank_transfer', 'check', 'credit_card')),
                reference_number VARCHAR(100),
                notes TEXT,
                journal_entry_id INTEGER,
                created_by_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES clients(id),
                FOREIGN KEY (invoice_id) REFERENCES invoices(id),
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        ''')
        print("✓ تم إنشاء جدول الدفعات المستلمة")
        
        # 8. إنشاء جدول الدفعات المسددة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments_made (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vendor_id INTEGER NOT NULL,
                bill_id INTEGER,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_date DATE NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('cash', 'bank_transfer', 'check')),
                reference_number VARCHAR(100),
                notes TEXT,
                journal_entry_id INTEGER,
                created_by_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (vendor_id) REFERENCES vendors(id),
                FOREIGN KEY (bill_id) REFERENCES vendor_bills(id),
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        ''')
        print("✓ تم إنشاء جدول الدفعات المسددة")
        
        # 9. إنشاء جدول السنوات المالية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fiscal_years (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_current BOOLEAN DEFAULT 0,
                status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'closed')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✓ تم إنشاء جدول السنوات المالية")
        
        # 10. إنشاء جدول معدلات الضرائب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tax_rates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                rate DECIMAL(5,2) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✓ تم إنشاء جدول معدلات الضرائب")
        
        # 11. تحديث جدول الفواتير الحالي (إضافة حقول جديدة)
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN invoice_type VARCHAR(20) DEFAULT "service"')
            print("✓ تم إضافة حقل invoice_type لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل invoice_type موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN payment_terms INTEGER DEFAULT 30')
            print("✓ تم إضافة حقل payment_terms لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل payment_terms موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN amount_due DECIMAL(15,2)')
            print("✓ تم إضافة حقل amount_due لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل amount_due موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN journal_entry_id INTEGER')
            print("✓ تم إضافة حقل journal_entry_id لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل journal_entry_id موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN tax_amount DECIMAL(15,2) DEFAULT 0')
            print("✓ تم إضافة حقل tax_amount لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل tax_amount موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE invoices ADD COLUMN discount_amount DECIMAL(15,2) DEFAULT 0')
            print("✓ تم إضافة حقل discount_amount لجدول الفواتير")
        except sqlite3.OperationalError:
            print("- حقل discount_amount موجود بالفعل")
        
        # 12. تحديث جدول العملاء الحالي (إضافة حقول جديدة)
        try:
            cursor.execute('ALTER TABLE clients ADD COLUMN credit_limit DECIMAL(15,2) DEFAULT 0')
            print("✓ تم إضافة حقل credit_limit لجدول العملاء")
        except sqlite3.OperationalError:
            print("- حقل credit_limit موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE clients ADD COLUMN payment_terms INTEGER DEFAULT 30')
            print("✓ تم إضافة حقل payment_terms لجدول العملاء")
        except sqlite3.OperationalError:
            print("- حقل payment_terms موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE clients ADD COLUMN tax_id VARCHAR(50)')
            print("✓ تم إضافة حقل tax_id لجدول العملاء")
        except sqlite3.OperationalError:
            print("- حقل tax_id موجود بالفعل")
        
        try:
            cursor.execute('ALTER TABLE clients ADD COLUMN is_active BOOLEAN DEFAULT 1')
            print("✓ تم إضافة حقل is_active لجدول العملاء")
        except sqlite3.OperationalError:
            print("- حقل is_active موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        print("\n🎉 تم إنشاء جميع جداول النظام المالي المتكامل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        conn.rollback()
        raise
    
    finally:
        conn.close()

def seed_chart_of_accounts():
    """إدراج شجرة الحسابات الأساسية"""
    
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        print("\nبدء إدراج شجرة الحسابات الأساسية...")
        
        # البيانات الأساسية لشجرة الحسابات
        accounts = [
            # الأصول (Assets)
            ('1000', 'الأصول', 'Asset', None),
            ('1010', 'النقد في البنك', 'Asset', None),
            ('1020', 'حسابات العملاء', 'Asset', None),
            ('1030', 'المخزون', 'Asset', None),
            ('1040', 'المعدات والأجهزة', 'Asset', None),
            
            # الخصوم (Liabilities)
            ('2000', 'الخصوم', 'Liability', None),
            ('2010', 'حسابات الموردين', 'Liability', None),
            ('2020', 'ضرائب مستحقة', 'Liability', None),
            ('2030', 'قروض قصيرة الأجل', 'Liability', None),
            
            # حقوق الملكية (Equity)
            ('3000', 'حقوق الملكية', 'Equity', None),
            ('3010', 'رأس المال', 'Equity', None),
            ('3020', 'الأرباح المحتجزة', 'Equity', None),
            
            # الإيرادات (Revenue)
            ('4000', 'الإيرادات', 'Revenue', None),
            ('4010', 'إيرادات خدمات التصميم', 'Revenue', None),
            ('4020', 'إيرادات خدمات البرمجة', 'Revenue', None),
            ('4030', 'إيرادات خدمات المونتاج', 'Revenue', None),
            ('4040', 'إيرادات إدارة الحسابات', 'Revenue', None),
            
            # المصروفات (Expenses)
            ('5000', 'المصروفات', 'Expense', None),
            ('5010', 'رواتب الموظفين', 'Expense', None),
            ('5020', 'اشتراكات البرامج', 'Expense', None),
            ('5030', 'إيجار المكتب', 'Expense', None),
            ('5040', 'مصروفات التسويق', 'Expense', None),
            ('5050', 'مصروفات الاتصالات', 'Expense', None),
            ('5060', 'مصروفات الاستضافة', 'Expense', None),
        ]
        
        # إدراج الحسابات
        for account_code, account_name, account_type, parent_id in accounts:
            cursor.execute('''
                INSERT OR IGNORE INTO chart_of_accounts 
                (account_code, account_name, account_type, parent_account_id, balance, is_active)
                VALUES (?, ?, ?, ?, 0, 1)
            ''', (account_code, account_name, account_type, parent_id))
        
        # إدراج السنة المالية الحالية
        current_year = datetime.now().year
        cursor.execute('''
            INSERT OR IGNORE INTO fiscal_years 
            (name, start_date, end_date, is_current, status)
            VALUES (?, ?, ?, 1, 'open')
        ''', (str(current_year), f'{current_year}-01-01', f'{current_year}-12-31'))
        
        # إدراج معدلات الضرائب الافتراضية
        tax_rates = [
            ('ضريبة القيمة المضافة', 15.00),
            ('ضريبة الخدمات', 5.00),
            ('معفى من الضريبة', 0.00),
        ]
        
        for name, rate in tax_rates:
            cursor.execute('''
                INSERT OR IGNORE INTO tax_rates (name, rate, is_active)
                VALUES (?, ?, 1)
            ''', (name, rate))
        
        conn.commit()
        print("✓ تم إدراج شجرة الحسابات والبيانات الأساسية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات: {e}")
        conn.rollback()
        raise
    
    finally:
        conn.close()

if __name__ == '__main__':
    # تشغيل إنشاء الجداول وإدراج البيانات
    create_enhanced_accounting_tables()
    seed_chart_of_accounts()
    print("\n🚀 النظام المالي المتكامل جاهز للاستخدام!")
