{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إضافة عنصر للفاتورة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.invoices') }}">الفواتير</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.view_invoice', id=invoice.id) }}">{{ invoice.invoice_number }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إضافة عنصر</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إضافة عنصر للفاتورة #{{ invoice.invoice_number }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('finance.add_invoice_item', invoice_id=invoice.id) }}" method="POST">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <input type="text" class="form-control" id="description" name="description" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">الكمية</label>
                                <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" value="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="unit_price" class="form-label">سعر الوحدة</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" class="form-control" id="unit_price" name="unit_price" value="0" required>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="supervisor_id" class="form-label">الموظف المشرف</label>
                                <select class="form-select" id="supervisor_id" name="supervisor_id">
                                    <option value="">-- بدون مشرف --</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.username }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_profit_type" class="form-label">نوع ربح الشركة</label>
                                <select class="form-select" id="company_profit_type" name="company_profit_type">
                                    <option value="percentage" selected>نسبة مئوية</option>
                                    <option value="fixed">مبلغ ثابت</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_profit_value" class="form-label">قيمة ربح الشركة</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" id="company_profit_value" name="company_profit_value" value="0">
                                    <span class="input-group-text" id="company_profit_symbol">%</span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="غير مستلم" selected>غير مستلم</option>
                                    <option value="مستلم">مستلم</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="receipt_date" class="form-label">تاريخ الاستلام (اختياري)</label>
                                <input type="date" class="form-control" id="receipt_date" name="receipt_date">
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.edit_invoice', id=invoice.id) }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">إضافة العنصر</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const companyProfitType = document.getElementById('company_profit_type');
        const companyProfitSymbol = document.getElementById('company_profit_symbol');

        companyProfitType.addEventListener('change', function() {
            companyProfitSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });
    });
</script>
{% endblock %}
