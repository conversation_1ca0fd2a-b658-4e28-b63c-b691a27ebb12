{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>الملف الشخصي</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الملف الشخصي</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card shadow">
                <div class="card-body text-center">
                    <img src="{{ url_for('static', filename='img/' + current_user.profile_image) }}" alt="{{ current_user.get_full_name() }}" class="profile-image mb-3">
                    <h4>{{ current_user.get_full_name() }}</h4>
                    <p class="text-muted">{{ current_user.username }}</p>

                    <div class="d-flex justify-content-center mb-3">
                        {% for role in current_user.roles %}
                        <span class="badge bg-primary me-1">{{ role.name }}</span>
                        {% endfor %}
                    </div>

                    <p class="mb-1">
                        <i class="fas fa-envelope me-2"></i>{{ current_user.email }}
                    </p>
                    {% if current_user.department %}
                    <p class="mb-1">
                        <i class="fas fa-building me-2"></i>{{ current_user.department.name }}
                    </p>
                    {% endif %}
                    {% if current_user.phone %}
                    <p class="mb-1">
                        <i class="fas fa-phone me-2"></i>{{ current_user.phone }}
                    </p>
                    {% endif %}
                    <p class="mb-1">
                        <i class="fas fa-calendar me-2"></i>تاريخ الانضمام: {{ current_user.date_joined.strftime('%Y-%m-%d') }}
                    </p>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">معلومات الحساب</h5>
                    <a href="{{ url_for('auth.profile') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-1"></i>تعديل الملف الشخصي
                    </a>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">الاسم الكامل:</div>
                        <div class="col-md-9">{{ current_user.get_full_name() }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">اسم المستخدم:</div>
                        <div class="col-md-9">{{ current_user.username }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">البريد الإلكتروني:</div>
                        <div class="col-md-9">{{ current_user.email }}</div>
                    </div>
                    {% if current_user.phone %}
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">رقم الهاتف:</div>
                        <div class="col-md-9">{{ current_user.phone }}</div>
                    </div>
                    {% endif %}
                    {% if current_user.department %}
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">القسم:</div>
                        <div class="col-md-9">{{ current_user.department.name }}</div>
                    </div>
                    {% endif %}
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">الصلاحيات:</div>
                        <div class="col-md-9">
                            {% for role in current_user.roles %}
                            <span class="badge bg-primary me-1">{{ role.name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% if current_user.bio %}
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">نبذة شخصية:</div>
                        <div class="col-md-9">{{ current_user.bio }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تغيير كلمة المرور</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.profile') }}">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">النشاط الأخير</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-0">سيتم عرض سجل نشاطك هنا.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
