# خدمة المحاسبة المتكاملة - محرك إنشاء القيود التلقائية
from datetime import date, datetime
from decimal import Decimal
from app import db
from app.models.accounting import ChartOfAccounts, JournalEntry, JournalItem, PaymentReceived
from flask_login import current_user

class AccountingService:
    """خدمة المحاسبة العامة - محرك إنشاء القيود التلقائية"""
    
    @staticmethod
    def create_invoice_journal_entry(invoice):
        """إنشاء قيد يومية عند إصدار فاتورة"""
        try:
            # البحث عن الحسابات المطلوبة
            ar_account = ChartOfAccounts.query.filter_by(account_code='1020').first()  # حسابات العملاء
            
            if not ar_account:
                raise ValueError("حساب العملاء (1020) غير موجود")
            
            # تحديد حساب الإيرادات المناسب حسب نوع الخدمة
            revenue_account = AccountingService.get_revenue_account_for_service(invoice)
            
            if not revenue_account:
                raise ValueError("حساب الإيرادات غير موجود")
            
            # إنشاء رقم قيد فريد
            entry_number = f"INV-{invoice.invoice_number}-{datetime.now().strftime('%Y%m%d')}"
            
            # إنشاء القيد
            entry = JournalEntry(
                entry_number=entry_number,
                entry_date=invoice.issue_date or date.today(),
                description=f"فاتورة رقم {invoice.invoice_number} للعميل {invoice.client.name}",
                source_document_type='invoice',
                source_document_id=invoice.id,
                created_by_id=current_user.id if current_user.is_authenticated else 1
            )
            
            # بند المدين: حسابات العملاء
            debit_item = JournalItem(
                account_id=ar_account.id,
                debit=invoice.total_amount,
                credit=0,
                description=f"فاتورة للعميل {invoice.client.name}"
            )
            
            # بند الدائن: الإيرادات
            credit_item = JournalItem(
                account_id=revenue_account.id,
                debit=0,
                credit=invoice.total_amount,
                description=f"إيرادات من {invoice.client.name}"
            )
            
            entry.items.append(debit_item)
            entry.items.append(credit_item)
            
            db.session.add(entry)
            db.session.flush()  # للحصول على ID
            
            # ربط الفاتورة بالقيد
            invoice.journal_entry_id = entry.id
            
            # ترحيل القيد
            entry.post()
            
            return entry
            
        except Exception as e:
            db.session.rollback()
            raise Exception(f"خطأ في إنشاء قيد الفاتورة: {str(e)}")
    
    @staticmethod
    def get_revenue_account_for_service(invoice):
        """تحديد حساب الإيرادات المناسب حسب نوع الخدمة"""
        
        # إذا كان هناك مشروع مرتبط، نحدد نوع الخدمة من اسم المشروع
        if invoice.project:
            project_name = invoice.project.name.lower()
            
            if 'تصميم' in project_name or 'design' in project_name:
                return ChartOfAccounts.query.filter_by(account_code='4010').first()
            elif 'برمجة' in project_name or 'programming' in project_name or 'تطوير' in project_name:
                return ChartOfAccounts.query.filter_by(account_code='4020').first()
            elif 'مونتاج' in project_name or 'editing' in project_name or 'فيديو' in project_name:
                return ChartOfAccounts.query.filter_by(account_code='4030').first()
            elif 'إدارة' in project_name or 'management' in project_name or 'حسابات' in project_name:
                return ChartOfAccounts.query.filter_by(account_code='4040').first()
        
        # إذا كان هناك بنود في الفاتورة، نحدد من وصف البنود
        if hasattr(invoice, 'items') and invoice.items:
            for item in invoice.items:
                if item.description:
                    desc = item.description.lower()
                    if 'تصميم' in desc or 'design' in desc:
                        return ChartOfAccounts.query.filter_by(account_code='4010').first()
                    elif 'برمجة' in desc or 'programming' in desc:
                        return ChartOfAccounts.query.filter_by(account_code='4020').first()
                    elif 'مونتاج' in desc or 'editing' in desc:
                        return ChartOfAccounts.query.filter_by(account_code='4030').first()
                    elif 'إدارة' in desc or 'management' in desc:
                        return ChartOfAccounts.query.filter_by(account_code='4040').first()
        
        # الافتراضي: إيرادات خدمات التصميم
        return ChartOfAccounts.query.filter_by(account_code='4010').first()
    
    @staticmethod
    def create_payment_received_entry(payment_data):
        """إنشاء قيد يومية للدفعة المستلمة"""
        try:
            # البحث عن الحسابات المطلوبة
            cash_account = ChartOfAccounts.query.filter_by(account_code='1010').first()  # النقد في البنك
            ar_account = ChartOfAccounts.query.filter_by(account_code='1020').first()    # حسابات العملاء
            
            if not cash_account or not ar_account:
                raise ValueError("الحسابات المطلوبة غير موجودة")
            
            # إنشاء سجل الدفعة
            payment = PaymentReceived(
                customer_id=payment_data['customer_id'],
                invoice_id=payment_data.get('invoice_id'),
                payment_number=payment_data['payment_number'],
                payment_date=payment_data.get('payment_date', date.today()),
                amount=Decimal(str(payment_data['amount'])),
                payment_method=payment_data['payment_method'],
                reference_number=payment_data.get('reference_number'),
                notes=payment_data.get('notes'),
                created_by_id=current_user.id if current_user.is_authenticated else 1
            )
            
            db.session.add(payment)
            db.session.flush()
            
            # إنشاء القيد المحاسبي
            payment.create_journal_entry()
            
            return payment
            
        except Exception as e:
            db.session.rollback()
            raise Exception(f"خطأ في إنشاء قيد الدفعة: {str(e)}")
    
    @staticmethod
    def get_account_balance(account_code, as_of_date=None):
        """حساب رصيد حساب محدد"""
        account = ChartOfAccounts.query.filter_by(account_code=account_code).first()
        if not account:
            return 0
        
        if as_of_date:
            return account.get_balance_for_period(date.min, as_of_date)
        else:
            return account.balance
    
    @staticmethod
    def get_trial_balance(as_of_date=None):
        """إنشاء ميزان المراجعة"""
        if not as_of_date:
            as_of_date = date.today()
        
        accounts = ChartOfAccounts.query.filter_by(is_active=True).order_by(ChartOfAccounts.account_code).all()
        
        trial_balance = []
        total_debit = 0
        total_credit = 0
        
        for account in accounts:
            balance = account.get_balance_for_period(date.min, as_of_date)
            
            if balance != 0:
                if account.account_type in ['Asset', 'Expense']:
                    debit = balance if balance > 0 else 0
                    credit = abs(balance) if balance < 0 else 0
                else:  # Liability, Equity, Revenue
                    debit = abs(balance) if balance < 0 else 0
                    credit = balance if balance > 0 else 0
                
                trial_balance.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'account_type': account.account_type,
                    'debit': float(debit),
                    'credit': float(credit)
                })
                
                total_debit += debit
                total_credit += credit
        
        return {
            'as_of_date': as_of_date.strftime('%Y-%m-%d'),
            'accounts': trial_balance,
            'total_debit': float(total_debit),
            'total_credit': float(total_credit),
            'is_balanced': abs(total_debit - total_credit) < 0.01
        }
    
    @staticmethod
    def update_invoice_amount_due(invoice):
        """تحديث المبلغ المستحق للفاتورة"""
        if not invoice.amount_due:
            invoice.amount_due = invoice.total_amount
        
        # حساب إجمالي الدفعات المستلمة لهذه الفاتورة
        total_payments = sum(
            payment.amount for payment in invoice.payments_received
        )
        
        # تحديث المبلغ المستحق
        invoice.amount_due = max(0, invoice.total_amount - total_payments)
        
        # تحديث حالة الفاتورة
        if invoice.amount_due == 0:
            invoice.status = 'paid'
        elif invoice.amount_due < invoice.total_amount:
            invoice.status = 'partially_paid'
        else:
            invoice.status = 'sent'
        
        db.session.commit()

class ReportingService:
    """خدمة التقارير المالية"""
    
    @staticmethod
    def get_profit_loss_report(start_date, end_date):
        """تقرير قائمة الدخل (الأرباح والخسائر)"""
        
        # الإيرادات
        revenue_accounts = ChartOfAccounts.query.filter_by(account_type='Revenue', is_active=True).all()
        revenue_data = []
        total_revenue = 0
        
        for account in revenue_accounts:
            balance = account.get_balance_for_period(start_date, end_date)
            if balance != 0:
                revenue_data.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'balance': float(balance)
                })
                total_revenue += balance
        
        # المصروفات
        expense_accounts = ChartOfAccounts.query.filter_by(account_type='Expense', is_active=True).all()
        expense_data = []
        total_expenses = 0
        
        for account in expense_accounts:
            balance = account.get_balance_for_period(start_date, end_date)
            if balance != 0:
                expense_data.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'balance': float(balance)
                })
                total_expenses += balance
        
        # صافي الربح
        net_profit = total_revenue - total_expenses
        
        return {
            'period': f"{start_date} إلى {end_date}",
            'revenue_accounts': revenue_data,
            'total_revenue': float(total_revenue),
            'expense_accounts': expense_data,
            'total_expenses': float(total_expenses),
            'net_profit': float(net_profit)
        }
    
    @staticmethod
    def get_balance_sheet_report(as_of_date):
        """تقرير الميزانية العمومية"""
        
        # الأصول
        asset_accounts = ChartOfAccounts.query.filter_by(account_type='Asset', is_active=True).all()
        assets_data = []
        total_assets = 0
        
        for account in asset_accounts:
            balance = account.get_balance_for_period(date.min, as_of_date)
            if balance != 0:
                assets_data.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'balance': float(balance)
                })
                total_assets += balance
        
        # الخصوم
        liability_accounts = ChartOfAccounts.query.filter_by(account_type='Liability', is_active=True).all()
        liabilities_data = []
        total_liabilities = 0
        
        for account in liability_accounts:
            balance = account.get_balance_for_period(date.min, as_of_date)
            if balance != 0:
                liabilities_data.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'balance': float(balance)
                })
                total_liabilities += balance
        
        # حقوق الملكية
        equity_accounts = ChartOfAccounts.query.filter_by(account_type='Equity', is_active=True).all()
        equity_data = []
        total_equity = 0
        
        for account in equity_accounts:
            balance = account.get_balance_for_period(date.min, as_of_date)
            if balance != 0:
                equity_data.append({
                    'account_code': account.account_code,
                    'account_name': account.account_name,
                    'balance': float(balance)
                })
                total_equity += balance
        
        return {
            'as_of_date': str(as_of_date),
            'assets': assets_data,
            'total_assets': float(total_assets),
            'liabilities': liabilities_data,
            'total_liabilities': float(total_liabilities),
            'equity': equity_data,
            'total_equity': float(total_equity),
            'balance_check': abs(total_assets - (total_liabilities + total_equity)) < 0.01
        }
