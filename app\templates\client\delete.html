{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف العميل</h1>
    <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لصفحة العميل
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
                
                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف العميل التالي؟</h4>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ client.name }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>الشركة:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ client.company or 'غير محدد' }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>البريد الإلكتروني:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ client.email or 'غير محدد' }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-3">
                                <strong>رقم الهاتف:</strong>
                            </div>
                            <div class="col-md-9">
                                {{ client.phone or 'غير محدد' }}
                            </div>
                        </div>
                        
                        {% if projects_count > 0 or invoices_count > 0 %}
                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-ban me-2"></i>
                            <strong>لا يمكن حذف هذا العميل</strong> لأنه مرتبط بـ:
                            <ul class="mb-0 mt-2">
                                {% if projects_count > 0 %}
                                <li>{{ projects_count }} مشروع</li>
                                {% endif %}
                                {% if invoices_count > 0 %}
                                <li>{{ invoices_count }} فاتورة</li>
                                {% endif %}
                            </ul>
                            <p class="mt-2 mb-0">يجب حذف جميع المشاريع والفواتير المرتبطة بهذا العميل أولاً.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <form action="{{ url_for('client.delete', id=client.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        
                        {% if projects_count == 0 and invoices_count == 0 %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف العميل
                        </button>
                        {% else %}
                        <button type="button" class="btn btn-danger" disabled>
                            <i class="fas fa-trash me-1"></i>حذف العميل
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
