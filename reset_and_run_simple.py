import os
import sqlite3
from app import create_app, db

# Delete the existing database
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Deleted existing database: {db_path}")

# Create a new empty database file
conn = sqlite3.connect(db_path)
conn.close()
print(f"Created new empty database file: {db_path}")

# Create the app
app = create_app()

# Create all tables
with app.app_context():
    db.create_all()
    print("Created all database tables")

    # Create default roles
    from app.models import Role

    # Check if roles already exist
    if Role.query.count() == 0:
        roles = [
            Role(name='admin', description='المسؤول الرئيسي للنظام مع كامل الصلاحيات'),
            Role(name='manager', description='مدير مع صلاحيات إدارية واسعة'),
            Role(name='department_head', description='رئيس قسم مع صلاحيات إدارة القسم'),
            Role(name='employee', description='موظف عادي'),
            Role(name='finance', description='موظف مالية مع صلاحيات إدارة المالية'),
            Role(name='sales', description='موظف مبيعات مع صلاحيات إدارة العملاء')
        ]

        db.session.add_all(roles)
        db.session.commit()
        print("Created default roles")

    # Create default admin user
    from app.models import User

    # Check if users already exist
    if User.query.count() == 0:
        admin_role = Role.query.filter_by(name='admin').first()

        if admin_role:
            admin = User(
                username='GolDeN',
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                is_active=True,
                cv=''
            )
            admin.set_password('GolDeN2252005')
            admin.roles.append(admin_role)

            db.session.add(admin)
            db.session.commit()
            print("Created default admin user (username: GolDeN, password: GolDeN2252005)")

    print("Database setup completed successfully!")

# Run the app
if __name__ == '__main__':
    app.run(debug=True)
