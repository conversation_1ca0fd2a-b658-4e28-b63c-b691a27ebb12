import os
from PIL import Image

# Path to the logo file
logo_path = os.path.join('app', 'static', 'img', 'logo.png')

# Path to save the favicon
favicon_path = os.path.join('app', 'static', 'img', 'favicon.ico')

# Check if the logo file exists
if not os.path.exists(logo_path):
    print(f"Logo file not found: {logo_path}")
    exit(1)

try:
    # Open the logo image
    img = Image.open(logo_path)
    
    # Resize the image to standard favicon sizes
    sizes = [16, 32, 48, 64, 128, 256]
    favicon_images = []
    
    for size in sizes:
        resized_img = img.copy()
        resized_img.thumbnail((size, size), Image.LANCZOS)
        favicon_images.append(resized_img)
    
    # Save as ICO file with multiple sizes
    favicon_images[0].save(
        favicon_path,
        format='ICO',
        sizes=[(img.width, img.height) for img in favicon_images],
        append_images=favicon_images[1:]
    )
    
    print(f"Favicon created successfully: {favicon_path}")
    
except Exception as e:
    print(f"Error creating favicon: {e}")
