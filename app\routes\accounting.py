# مسارات النظام المحاسبي المتكامل
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from decimal import Decimal
import json

from app import db
from app.models.accounting import (
    ChartOfAccounts, JournalEntry, JournalItem, PaymentReceived,
    Vendor, VendorBill, VendorBillItem, PaymentMade, FiscalYear, TaxRate
)
from app.models.finance import Invoice
from app.models.client import Client
from app.services.accounting_service import AccountingService, ReportingService

# إنشاء Blueprint للنظام المحاسبي
accounting_bp = Blueprint('accounting', __name__, url_prefix='/accounting')

# ===== لوحة التحكم المحاسبية =====

@accounting_bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم المحاسبية المتكاملة"""
    
    # حساب المؤشرات الرئيسية
    current_month_start = date.today().replace(day=1)
    current_month_end = date.today()
    
    # الإيرادات والمصروفات الشهرية
    pnl_report = ReportingService.get_profit_loss_report(current_month_start, current_month_end)
    
    # الأرصدة الرئيسية
    cash_balance = AccountingService.get_account_balance('1010')
    ar_balance = AccountingService.get_account_balance('1020')
    ap_balance = AccountingService.get_account_balance('2010')
    
    # الفواتير المعلقة
    pending_invoices = Invoice.query.filter(
        Invoice.status.in_(['sent', 'unpaid'])
    ).count()
    
    # إجمالي المبالغ المستحقة
    outstanding_receivables = sum(
        invoice.amount_due or invoice.total_amount 
        for invoice in Invoice.query.filter(Invoice.status.in_(['sent', 'unpaid'])).all()
    )
    
    # الفواتير المتأخرة
    overdue_invoices = Invoice.query.filter(
        Invoice.status.in_(['sent', 'unpaid']),
        Invoice.due_date < date.today()
    ).count()
    
    return render_template('accounting/dashboard.html',
                         monthly_revenue=pnl_report['total_revenue'],
                         monthly_expenses=pnl_report['total_expenses'],
                         net_profit=pnl_report['net_profit'],
                         cash_balance=cash_balance,
                         ar_balance=ar_balance,
                         ap_balance=ap_balance,
                         pending_invoices=pending_invoices,
                         outstanding_receivables=outstanding_receivables,
                         overdue_invoices=overdue_invoices)

# ===== شجرة الحسابات =====

@accounting_bp.route('/chart-of-accounts')
@login_required
def chart_of_accounts():
    """عرض شجرة الحسابات"""
    accounts = ChartOfAccounts.query.filter_by(is_active=True).order_by(ChartOfAccounts.account_code).all()
    return render_template('accounting/chart_of_accounts.html', accounts=accounts)

@accounting_bp.route('/api/accounts')
@login_required
def api_accounts():
    """API لجلب قائمة الحسابات"""
    accounts = ChartOfAccounts.query.filter_by(is_active=True).order_by(ChartOfAccounts.account_code).all()
    
    accounts_data = []
    for account in accounts:
        accounts_data.append({
            'id': account.id,
            'account_code': account.account_code,
            'account_name': account.account_name,
            'account_type': account.account_type,
            'balance': float(account.balance),
            'parent_account_id': account.parent_account_id
        })
    
    return jsonify(accounts_data)

@accounting_bp.route('/api/accounts/<int:account_id>/balance')
@login_required
def api_account_balance(account_id):
    """API لجلب رصيد حساب محدد"""
    account = ChartOfAccounts.query.get_or_404(account_id)
    
    return jsonify({
        'account_code': account.account_code,
        'account_name': account.account_name,
        'balance': float(account.balance),
        'account_type': account.account_type
    })

# ===== قيود اليومية =====

@accounting_bp.route('/journal-entries')
@login_required
def journal_entries():
    """عرض قيود اليومية"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    entries = JournalEntry.query.order_by(JournalEntry.entry_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('accounting/journal_entries.html', entries=entries)

@accounting_bp.route('/journal-entries/<int:entry_id>')
@login_required
def journal_entry_details(entry_id):
    """تفاصيل قيد يومية"""
    entry = JournalEntry.query.get_or_404(entry_id)
    return render_template('accounting/journal_entry_details.html', entry=entry)

# ===== الدفعات المستلمة =====

@accounting_bp.route('/payments/receive', methods=['GET', 'POST'])
@login_required
def receive_payment():
    """تسجيل دفعة مستلمة"""
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            customer_id = request.form.get('customer_id', type=int)
            invoice_id = request.form.get('invoice_id', type=int) if request.form.get('invoice_id') else None
            amount = Decimal(request.form.get('amount'))
            payment_method = request.form.get('payment_method')
            reference_number = request.form.get('reference_number')
            notes = request.form.get('notes')
            
            # إنشاء رقم دفعة فريد
            payment_number = f"PAY-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # بيانات الدفعة
            payment_data = {
                'customer_id': customer_id,
                'invoice_id': invoice_id,
                'payment_number': payment_number,
                'payment_date': date.today(),
                'amount': amount,
                'payment_method': payment_method,
                'reference_number': reference_number,
                'notes': notes
            }
            
            # إنشاء الدفعة والقيد المحاسبي
            payment = AccountingService.create_payment_received_entry(payment_data)
            
            # تحديث رصيد الفاتورة إذا كانت مرتبطة
            if invoice_id:
                invoice = Invoice.query.get(invoice_id)
                if invoice:
                    AccountingService.update_invoice_amount_due(invoice)
            
            db.session.commit()
            flash('تم تسجيل الدفعة والقيد المحاسبي بنجاح', 'success')
            return redirect(url_for('accounting.payments_received'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تسجيل الدفعة: {str(e)}', 'danger')
    
    # جلب العملاء والفواتير المعلقة
    customers = Client.query.filter_by(is_active=True).all()
    pending_invoices = Invoice.query.filter(Invoice.status.in_(['sent', 'unpaid'])).all()
    
    return render_template('accounting/receive_payment.html', 
                         customers=customers, 
                         pending_invoices=pending_invoices)

@accounting_bp.route('/payments/received')
@login_required
def payments_received():
    """عرض الدفعات المستلمة"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    payments = PaymentReceived.query.order_by(PaymentReceived.payment_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('accounting/payments_received.html', payments=payments)

# ===== API للدفعات =====

@accounting_bp.route('/api/payments/receive', methods=['POST'])
@login_required
def api_receive_payment():
    """API لتسجيل دفعة مستلمة"""
    try:
        data = request.get_json()
        
        # إنشاء رقم دفعة فريد
        payment_number = f"PAY-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        payment_data = {
            'customer_id': data['customer_id'],
            'invoice_id': data.get('invoice_id'),
            'payment_number': payment_number,
            'payment_date': datetime.strptime(data.get('payment_date', date.today().isoformat()), '%Y-%m-%d').date(),
            'amount': Decimal(str(data['amount'])),
            'payment_method': data['payment_method'],
            'reference_number': data.get('reference_number'),
            'notes': data.get('notes')
        }
        
        # إنشاء الدفعة والقيد المحاسبي
        payment = AccountingService.create_payment_received_entry(payment_data)
        
        # تحديث رصيد الفاتورة إذا كانت مرتبطة
        if payment_data['invoice_id']:
            invoice = Invoice.query.get(payment_data['invoice_id'])
            if invoice:
                AccountingService.update_invoice_amount_due(invoice)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تسجيل الدفعة والقيد المحاسبي بنجاح',
            'payment_id': payment.id,
            'journal_entry_id': payment.journal_entry_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في تسجيل الدفعة: {str(e)}'
        }), 400

# ===== التقارير المالية =====

@accounting_bp.route('/reports')
@login_required
def reports():
    """صفحة التقارير المالية"""
    return render_template('accounting/reports.html')

@accounting_bp.route('/reports/profit-loss')
@login_required
def profit_loss_report():
    """تقرير قائمة الدخل"""
    # الحصول على نطاق التواريخ من المعاملات
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # الافتراضي: الشهر الحالي
    if not start_date:
        start_date = date.today().replace(day=1)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    
    if not end_date:
        end_date = date.today()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # إنشاء التقرير
    report = ReportingService.get_profit_loss_report(start_date, end_date)
    
    return render_template('accounting/profit_loss_report.html', 
                         report=report, 
                         start_date=start_date, 
                         end_date=end_date)

@accounting_bp.route('/reports/balance-sheet')
@login_required
def balance_sheet_report():
    """تقرير الميزانية العمومية"""
    # الحصول على التاريخ من المعاملات
    as_of_date = request.args.get('as_of_date')
    
    if not as_of_date:
        as_of_date = date.today()
    else:
        as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
    
    # إنشاء التقرير
    report = ReportingService.get_balance_sheet_report(as_of_date)
    
    return render_template('accounting/balance_sheet_report.html', 
                         report=report, 
                         as_of_date=as_of_date)

@accounting_bp.route('/reports/trial-balance')
@login_required
def trial_balance_report():
    """تقرير ميزان المراجعة"""
    as_of_date = request.args.get('as_of_date')
    
    if not as_of_date:
        as_of_date = date.today()
    else:
        as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
    
    # إنشاء ميزان المراجعة
    trial_balance = AccountingService.get_trial_balance(as_of_date)
    
    return render_template('accounting/trial_balance_report.html', 
                         trial_balance=trial_balance, 
                         as_of_date=as_of_date)

# ===== APIs للتقارير =====

@accounting_bp.route('/api/reports/profit-loss')
@login_required
def api_profit_loss_report():
    """API لتقرير قائمة الدخل"""
    start_date = request.args.get('start_date', date.today().replace(day=1).isoformat())
    end_date = request.args.get('end_date', date.today().isoformat())
    
    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    report = ReportingService.get_profit_loss_report(start_date, end_date)
    return jsonify(report)

@accounting_bp.route('/api/reports/balance-sheet')
@login_required
def api_balance_sheet_report():
    """API لتقرير الميزانية العمومية"""
    as_of_date = request.args.get('as_of_date', date.today().isoformat())
    as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
    
    report = ReportingService.get_balance_sheet_report(as_of_date)
    return jsonify(report)

@accounting_bp.route('/api/dashboard/kpis')
@login_required
def api_dashboard_kpis():
    """API لمؤشرات الأداء الرئيسية"""
    current_month_start = date.today().replace(day=1)
    current_month_end = date.today()
    
    # الإيرادات والمصروفات الشهرية
    pnl_report = ReportingService.get_profit_loss_report(current_month_start, current_month_end)
    
    # الأرصدة الرئيسية
    cash_balance = AccountingService.get_account_balance('1010')
    ar_balance = AccountingService.get_account_balance('1020')
    ap_balance = AccountingService.get_account_balance('2010')
    
    # إجمالي المبالغ المستحقة
    outstanding_receivables = sum(
        invoice.amount_due or invoice.total_amount 
        for invoice in Invoice.query.filter(Invoice.status.in_(['sent', 'unpaid'])).all()
    )
    
    return jsonify({
        'monthly_revenue': pnl_report['total_revenue'],
        'monthly_expenses': pnl_report['total_expenses'],
        'net_profit': pnl_report['net_profit'],
        'cash_balance': float(cash_balance),
        'accounts_receivable': float(ar_balance),
        'accounts_payable': float(ap_balance),
        'outstanding_receivables': float(outstanding_receivables)
    })
