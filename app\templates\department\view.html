{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ department.name }}</h1>
    <div>
        <a href="{{ url_for('department.index') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للأقسام
        </a>
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <a href="{{ url_for('department.edit', id=department.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">معلومات القسم</h5>
            </div>
            <div class="card-body">
                <p class="mb-3">{{ department.description or 'لا يوجد وصف متاح.' }}</p>

                <p class="mb-2">
                    <strong>رئيس القسم:</strong>
                    {% if department.head %}
                    <a href="{{ url_for('employee.view', id=department.head.id) }}">{{ department.head.get_full_name() }}</a>
                    {% else %}
                    <span class="text-muted">غير معين</span>
                    {% endif %}
                </p>

                <p class="mb-2">
                    <strong>عدد الموظفين:</strong> {{ department.get_member_count() }}
                </p>

                <p class="mb-2">
                    <strong>المشاريع النشطة:</strong> {{ department.get_active_projects_count() }}
                </p>

                <p class="mb-2">
                    <strong>تاريخ الإنشاء:</strong> {{ department.created_at.strftime('%Y-%m-%d') }}
                </p>

                <div class="d-grid gap-2 mt-4">
                    <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-primary">
                        <i class="fas fa-users me-1"></i>إدارة الأعضاء
                    </a>

                    {% if current_user.has_role('admin') %}
                    <a href="{{ url_for('department.confirm_delete', id=department.id) }}" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف القسم
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Department Members -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أعضاء القسم</h5>
                <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if department.members %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الصلاحيات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in department.members %}
                            <tr>
                                <td>{{ member.get_full_name() }}</td>
                                <td>{{ member.email }}</td>
                                <td>
                                    {% for role in member.roles %}
                                    <span class="badge bg-secondary me-1">{{ role.name }}</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    <a href="{{ url_for('employee.view', id=member.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد أعضاء في هذا القسم حاليًا.</p>
                {% endif %}
            </div>
        </div>

        <!-- Department Projects -->
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">مشاريع القسم</h5>
            </div>
            <div class="card-body">
                {% set projects = department.get_projects() %}
                {% if projects|length > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم المشروع</th>
                                <th>الحالة</th>
                                <th>تاريخ البدء</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for project in projects %}
                            <tr>
                                <td>{{ project.name }}</td>
                                <td>
                                    {% if project.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif project.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif project.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif project.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ project.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                <td>
                                    <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد مشاريع لهذا القسم حاليًا.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
