{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>تعديل بيانات العميل</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('client.index') }}">العملاء</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل بيانات العميل</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-warning">
                    <h5 class="mb-0">تعديل معلومات العميل</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('client.edit', id=client.id) }}" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العميل / الشركة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ client.name }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="contact_person" class="form-label">الشخص المسؤول</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person" value="{{ client.contact_person }}">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ client.email }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" value="{{ client.phone }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3">{{ client.address }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="website" name="website" value="{{ client.website }}" placeholder="https://">
                        </div>

                        <div class="mb-3">
                            <label for="industry" class="form-label">المجال / الصناعة</label>
                            <input type="text" class="form-control" id="industry" name="industry" value="{{ client.industry }}">
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ client.notes }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="profile_image" class="form-label">الصورة الشخصية / شعار الشركة</label>
                            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                            <small class="text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الصورة الحالية</small>
                            <div class="mt-2">
                                <p>الصورة الحالية:</p>
                                {% if client.profile_image and client.profile_image != 'default.jpg' %}
                                <img src="{{ url_for('static', filename=client.profile_image) }}" alt="{{ client.name }}" class="img-thumbnail" style="max-width: 150px;">
                                {% else %}
                                <img src="{{ url_for('static', filename='uploads/default.jpg') }}" alt="{{ client.name }}" class="img-thumbnail" style="max-width: 150px;">
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                            <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
