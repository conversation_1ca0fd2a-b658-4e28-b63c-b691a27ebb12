{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>لوحة المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">لوحة المالية</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الإيرادات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_revenue }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي المصروفات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_expenses }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-info h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                صافي الربح</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ net_profit }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الفواتير المعلقة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_invoices_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي ربح الشركة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_company_profit }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card shadow border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الربح العام</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_general_profit }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-check-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث المعاملات</h5>
                    <div>
                        <a href="{{ url_for('finance.transactions') }}" class="btn btn-sm btn-primary me-2">عرض الكل</a>
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#transactionsFilterCollapse" aria-expanded="false" aria-controls="transactionsFilterCollapse">
                            <i class="fas fa-filter me-1"></i>تصفية
                        </button>
                    </div>
                </div>

                <!-- Transactions Filter -->
                <div class="collapse" id="transactionsFilterCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="{{ url_for('finance.index') }}" class="row g-3">
                            <!-- Keep existing invoice filters -->
                            <input type="hidden" name="invoices_page" value="{{ request.args.get('invoices_page', 1) }}">
                            <input type="hidden" name="invoices_per_page" value="{{ invoices_per_page }}">
                            <input type="hidden" name="invoices_status" value="{{ invoices_status_filter }}">

                            <div class="col-md-6">
                                <label for="transactions_type" class="form-label">النوع</label>
                                <select class="form-select" id="transactions_type" name="transactions_type">
                                    <option value="all" {% if transactions_type_filter == 'all' %}selected{% endif %}>الكل</option>
                                    <option value="income" {% if transactions_type_filter == 'income' %}selected{% endif %}>إيراد</option>
                                    <option value="expense" {% if transactions_type_filter == 'expense' %}selected{% endif %}>مصروف</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="transactions_per_page" class="form-label">عدد النتائج</label>
                                <select class="form-select" id="transactions_per_page" name="transactions_per_page">
                                    <option value="5" {% if transactions_per_page == 5 %}selected{% endif %}>5</option>
                                    <option value="10" {% if transactions_per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="15" {% if transactions_per_page == 15 %}selected{% endif %}>15</option>
                                    <option value="20" {% if transactions_per_page == 20 %}selected{% endif %}>20</option>
                                </select>
                            </div>
                            <div class="col-12 d-flex">
                                <button type="submit" class="btn btn-primary">تطبيق</button>
                                <a href="{{ url_for('finance.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    {% if recent_transactions.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions.items %}
                                <tr>
                                    <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>
                                        {% if transaction.transaction_type == 'income' %}
                                        <span class="badge bg-success">إيراد</span>
                                        {% else %}
                                        <span class="badge bg-danger">مصروف</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ transaction.amount }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Transactions Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_transactions.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.index', transactions_page=recent_transactions.prev_num, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter, invoices_page=recent_invoices.page, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_transactions.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_transactions.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('finance.index', transactions_page=page_num, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter, invoices_page=recent_invoices.page, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_transactions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.index', transactions_page=recent_transactions.next_num, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter, invoices_page=recent_invoices.page, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد معاملات حديثة.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الفواتير</h5>
                    <div>
                        <a href="{{ url_for('finance.invoices') }}" class="btn btn-sm btn-primary me-2">عرض الكل</a>
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#invoicesFilterCollapse" aria-expanded="false" aria-controls="invoicesFilterCollapse">
                            <i class="fas fa-filter me-1"></i>تصفية
                        </button>
                    </div>
                </div>

                <!-- Invoices Filter -->
                <div class="collapse" id="invoicesFilterCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="{{ url_for('finance.index') }}" class="row g-3">
                            <!-- Keep existing transaction filters -->
                            <input type="hidden" name="transactions_page" value="{{ request.args.get('transactions_page', 1) }}">
                            <input type="hidden" name="transactions_per_page" value="{{ transactions_per_page }}">
                            <input type="hidden" name="transactions_type" value="{{ transactions_type_filter }}">

                            <div class="col-md-6">
                                <label for="invoices_status" class="form-label">الحالة</label>
                                <select class="form-select" id="invoices_status" name="invoices_status">
                                    <option value="all" {% if invoices_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                    <option value="paid" {% if invoices_status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                    <option value="pending" {% if invoices_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="overdue" {% if invoices_status_filter == 'overdue' %}selected{% endif %}>متأخر</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="invoices_per_page" class="form-label">عدد النتائج</label>
                                <select class="form-select" id="invoices_per_page" name="invoices_per_page">
                                    <option value="5" {% if invoices_per_page == 5 %}selected{% endif %}>5</option>
                                    <option value="10" {% if invoices_per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="15" {% if invoices_per_page == 15 %}selected{% endif %}>15</option>
                                    <option value="20" {% if invoices_per_page == 20 %}selected{% endif %}>20</option>
                                </select>
                            </div>
                            <div class="col-12 d-flex">
                                <button type="submit" class="btn btn-primary">تطبيق</button>
                                <a href="{{ url_for('finance.index') }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    {% if recent_invoices.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices.items %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>{{ invoice.client.name if invoice.client else 'غير محدد' }}</td>
                                    <td>${{ invoice.total_amount }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif invoice.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخر</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Invoices Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if recent_invoices.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.index', invoices_page=recent_invoices.prev_num, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter, transactions_page=recent_transactions.page, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in recent_invoices.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=1) %}
                                    {% if page_num %}
                                        {% if page_num == recent_invoices.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('finance.index', invoices_page=page_num, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter, transactions_page=recent_transactions.page, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if recent_invoices.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('finance.index', invoices_page=recent_invoices.next_num, invoices_per_page=invoices_per_page, invoices_status=invoices_status_filter, transactions_page=recent_transactions.page, transactions_per_page=transactions_per_page, transactions_type=transactions_type_filter) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد فواتير حديثة.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Charts -->
    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إجمالي الربح العام والمصروفات الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyFinanceChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">توزيع المصروفات</h5>
                </div>
                <div class="card-body">
                    <canvas id="expensesChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Finance Chart
        var monthlyFinanceCtx = document.getElementById('monthlyFinanceChart').getContext('2d');
        var monthlyFinanceChart = new Chart(monthlyFinanceCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'إجمالي الربح العام',
                    data: {{ monthly_income|tojson }},
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }, {
                    label: 'المصروفات',
                    data: {{ monthly_expenses|tojson }},
                    backgroundColor: 'rgba(231, 74, 59, 0.5)',
                    borderColor: 'rgba(231, 74, 59, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Expenses Chart
        var expensesCtx = document.getElementById('expensesChart').getContext('2d');
        var expensesChart = new Chart(expensesCtx, {
            type: 'pie',
            data: {
                labels: {{ expense_categories|tojson }},
                datasets: [{
                    data: {{ expense_amounts|tojson }},
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.8)',
                        'rgba(28, 200, 138, 0.8)',
                        'rgba(54, 185, 204, 0.8)',
                        'rgba(246, 194, 62, 0.8)',
                        'rgba(231, 74, 59, 0.8)',
                        'rgba(133, 135, 150, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
