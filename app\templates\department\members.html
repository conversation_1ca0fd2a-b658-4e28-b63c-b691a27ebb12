{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>أعضاء قسم {{ department.name }}</h1>
    <div>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == department.head_id %}
        <a href="{{ url_for('department.add_member', id=department.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-user-plus me-1"></i>إضافة أعضاء
        </a>
        {% endif %}
        <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقسم
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة الأعضاء</h5>
    </div>
    <div class="card-body">
        {% if members %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الصلاحيات</th>
                        <th>تاريخ الانضمام</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member in members %}
                    <tr>
                        <td>
                            {% if member.id == department.head_id %}
                            <i class="fas fa-crown text-warning me-1" data-bs-toggle="tooltip" title="رئيس القسم"></i>
                            {% endif %}
                            {{ member.get_full_name() }}
                        </td>
                        <td>{{ member.email }}</td>
                        <td>{{ member.phone or 'غير متوفر' }}</td>
                        <td>
                            {% for role in member.roles %}
                            <span class="badge bg-secondary me-1">{{ role.name }}</span>
                            {% endfor %}
                        </td>
                        <td>{{ member.date_joined.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="{{ url_for('employee.view', id=member.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>

                            {% if (current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == department.head_id) and member.id != department.head_id %}
                            <a href="{{ url_for('department.confirm_remove_member', dept_id=department.id, user_id=member.id) }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="إزالة من القسم">
                                <i class="fas fa-user-minus"></i>
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا يوجد أعضاء في هذا القسم حاليًا.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
