import os
from app import create_app, db

# Determine environment
env = os.environ.get('FLASK_ENV', 'development')  # Set to development for debugging
debug_mode = True  # Force debug mode on

# Create app with appropriate configuration
app = create_app()

# Create tables if they don't exist
with app.app_context():
    # Create all tables
    db.create_all()
    print("Database setup completed!")

if __name__ == '__main__':
    # Get port from environment or use default
    port = int(os.environ.get('PORT', 5000))

    # Always use 0.0.0.0 for production deployments
    # This allows the app to be accessible from outside the container/VM
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
