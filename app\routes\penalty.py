from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import datetime
import os
from werkzeug.utils import secure_filename

from app import db
from app.models.penalty import Penalty, PenaltyAttachment
from app.models.user import User
from app.utils.notifications import send_notification

penalty_bp = Blueprint('penalty', __name__, url_prefix='/penalties')

@penalty_bp.route('/')
@login_required
def index():
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    penalty_type = request.args.get('penalty_type', '')
    status = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    employee_id = request.args.get('employee_id', '')

    # Start building the query based on user role
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers see all penalties
        query = Penalty.query
    else:
        # Regular employees see only their own penalties
        query = Penalty.query.filter_by(user_id=current_user.id)

    # Apply search filter if provided
    if search_query:
        # Join with User model to search by employee name
        query = query.join(User, Penalty.user_id == User.id).filter(
            (User.first_name.like(f"%{search_query}%")) |
            (User.last_name.like(f"%{search_query}%")) |
            (User.username.like(f"%{search_query}%")) |
            (Penalty.reason.like(f"%{search_query}%"))
        )

    # Apply penalty type filter if provided
    if penalty_type and penalty_type != 'all':
        query = query.filter(Penalty.penalty_type == penalty_type)

    # Apply status filter if provided
    if status and status != 'all':
        today = datetime.now().date()
        if status == 'active':
            # Active penalties: end_date is null or end_date >= today
            query = query.filter((Penalty.end_date == None) | (Penalty.end_date >= today))
        elif status == 'expired':
            # Expired penalties: end_date < today
            query = query.filter(Penalty.end_date < today)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Penalty.start_date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Penalty.start_date <= to_date)
        except ValueError:
            pass

    # Apply employee filter if provided (only for admins and managers)
    if employee_id and (current_user.has_role('admin') or current_user.has_role('manager')):
        query = query.filter(Penalty.user_id == employee_id)

    # Order by created_at (newest first)
    query = query.order_by(Penalty.created_at.desc())

    # Paginate the results
    penalties = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get all employees for filter (only for admins and managers)
    employees = []
    if current_user.has_role('admin') or current_user.has_role('manager'):
        employees = User.query.order_by(User.first_name).all()

    # Get penalty types for filter
    penalty_types = [
        {'value': 'verbal_warning', 'label': 'لفت نظر شفوي'},
        {'value': 'written_warning', 'label': 'لفت نظر كتابي (تحذير أول)'},
        {'value': 'written_notice', 'label': 'إنذار كتابي (تحذير ثاني)'},
        {'value': 'suspension', 'label': 'إيقاف مؤقت / خصم من الراتب'},
        {'value': 'final_warning', 'label': 'الإنذار النهائي'},
        {'value': 'termination', 'label': 'الفصل من العمل / إنهاء التعاقد'}
    ]

    return render_template('penalty/index.html',
                          title='العقوبات',
                          penalties=penalties,
                          employees=employees,
                          penalty_types=penalty_types,
                          search_query=search_query,
                          penalty_type=penalty_type,
                          status=status,
                          date_from=date_from,
                          date_to=date_to,
                          employee_id=employee_id,
                          current_per_page=per_page)

@penalty_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Only admin and manager can create penalties
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لإنشاء عقوبات', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all users for the form
    users = User.query.all()

    if request.method == 'POST':
        user_id = request.form.get('user_id')
        penalty_type = request.form.get('penalty_type')
        reason = request.form.get('reason')
        details = request.form.get('details')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        salary_deduction = request.form.get('salary_deduction')

        # Validate data
        if not user_id or not penalty_type or not reason or not start_date:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
            return redirect(url_for('penalty.create'))

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                if start_date > end_date:
                    flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                    return redirect(url_for('penalty.create'))
            else:
                end_date = None

            if salary_deduction:
                try:
                    salary_deduction = float(salary_deduction)
                    if salary_deduction < 0:
                        flash('قيمة الخصم يجب أن تكون رقماً موجباً', 'danger')
                        return redirect(url_for('penalty.create'))
                except ValueError:
                    flash('قيمة الخصم يجب أن تكون رقماً', 'danger')
                    return redirect(url_for('penalty.create'))
            else:
                salary_deduction = None
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')
            return redirect(url_for('penalty.create'))

        # Create penalty
        penalty = Penalty(
            user_id=user_id,
            penalty_type=penalty_type,
            reason=reason,
            details=details,
            start_date=start_date,
            end_date=end_date,
            salary_deduction=salary_deduction,
            issued_by_id=current_user.id
        )

        db.session.add(penalty)
        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'penalties', str(penalty.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = PenaltyAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'penalties', str(penalty.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        penalty_id=penalty.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

            db.session.commit()

        # Send notification to the user
        send_notification(
            user_id=penalty.user_id,
            title='تم إصدار عقوبة جديدة',
            message=f'تم إصدار عقوبة جديدة بحقك: {penalty.get_penalty_type_display()}. السبب: {penalty.reason}',
            notification_type='penalty'
        )

        flash('تم إنشاء العقوبة بنجاح', 'success')
        return redirect(url_for('penalty.index'))

    return render_template('penalty/create.html', title='إنشاء عقوبة', users=users)

@penalty_bp.route('/view/<int:id>')
@login_required
def view(id):
    penalty = Penalty.query.get_or_404(id)

    # Check if user has permission to view this penalty
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == penalty.user_id):
        flash('ليس لديك صلاحية لعرض هذه العقوبة', 'danger')
        return redirect(url_for('dashboard.index'))

    return render_template('penalty/view.html', title='عرض العقوبة', penalty=penalty)

@penalty_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Only admin and manager can edit penalties
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتعديل العقوبات', 'danger')
        return redirect(url_for('dashboard.index'))

    penalty = Penalty.query.get_or_404(id)

    if request.method == 'POST':
        penalty_type = request.form.get('penalty_type')
        reason = request.form.get('reason')
        details = request.form.get('details')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        salary_deduction = request.form.get('salary_deduction')

        # Validate data
        if not penalty_type or not reason or not start_date:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
            return redirect(url_for('penalty.edit', id=id))

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                if start_date > end_date:
                    flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                    return redirect(url_for('penalty.edit', id=id))
            else:
                end_date = None

            if salary_deduction:
                try:
                    salary_deduction = float(salary_deduction)
                    if salary_deduction < 0:
                        flash('قيمة الخصم يجب أن تكون رقماً موجباً', 'danger')
                        return redirect(url_for('penalty.edit', id=id))
                except ValueError:
                    flash('قيمة الخصم يجب أن تكون رقماً', 'danger')
                    return redirect(url_for('penalty.edit', id=id))
            else:
                salary_deduction = None
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'danger')
            return redirect(url_for('penalty.edit', id=id))

        # Update penalty
        penalty.penalty_type = penalty_type
        penalty.reason = reason
        penalty.details = details
        penalty.start_date = start_date
        penalty.end_date = end_date
        penalty.salary_deduction = salary_deduction
        penalty.updated_at = datetime.utcnow()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'penalties', str(penalty.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = PenaltyAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'penalties', str(penalty.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        penalty_id=penalty.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        db.session.commit()

        # Send notification to the user
        send_notification(
            user_id=penalty.user_id,
            title='تم تحديث العقوبة',
            message=f'تم تحديث العقوبة: {penalty.get_penalty_type_display()}. السبب: {penalty.reason}',
            notification_type='penalty'
        )

        flash('تم تحديث العقوبة بنجاح', 'success')
        return redirect(url_for('penalty.view', id=id))

    return render_template('penalty/edit.html', title='تعديل العقوبة', penalty=penalty)

@penalty_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    # Only admin can delete penalties
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف العقوبات', 'danger')
        return redirect(url_for('dashboard.index'))

    penalty = Penalty.query.get_or_404(id)

    # Delete attachments from filesystem
    for attachment in penalty.attachments:
        file_path = os.path.join('app', 'static', attachment.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)

    # Delete penalty
    db.session.delete(penalty)
    db.session.commit()

    flash('تم حذف العقوبة بنجاح', 'success')
    return redirect(url_for('penalty.index'))

@penalty_bp.route('/delete_attachment/<int:attachment_id>', methods=['POST'])
@login_required
def delete_attachment(attachment_id):
    # Only admin and manager can delete attachments
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لحذف المرفقات', 'danger')
        return redirect(url_for('dashboard.index'))

    attachment = PenaltyAttachment.query.get_or_404(attachment_id)
    penalty_id = attachment.penalty_id

    # Delete file from filesystem
    file_path = os.path.join('app', 'static', attachment.file_path)
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete from database
    db.session.delete(attachment)
    db.session.commit()

    flash('تم حذف المرفق بنجاح', 'success')
    return redirect(url_for('penalty.edit', id=penalty_id))
