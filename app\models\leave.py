from datetime import datetime
from app import db

class LeaveRequest(db.Model):
    __tablename__ = 'leave_requests'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    rejection_reason = db.Column(db.Text)
    approval_reason = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user = db.relationship('User', foreign_keys=[user_id], backref='leave_requests')

    # If request was created by someone else (manager/admin) on behalf of the user
    created_by_id = db.Column(db.Integer, db.<PERSON>ey('users.id'))
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_leave_requests')

    # If request was approved/rejected by someone
    reviewed_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    reviewed_by = db.relationship('User', foreign_keys=[reviewed_by_id], backref='reviewed_leave_requests')

    # Attachments for this leave request
    attachments = db.relationship('LeaveAttachment', backref='leave_request', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<LeaveRequest {self.id}: {self.user.get_full_name()} ({self.start_date} to {self.end_date})>'

    def get_duration_days(self):
        """Calculate the duration of the leave in days"""
        if not self.start_date or not self.end_date:
            return 0
        delta = self.end_date - self.start_date
        return delta.days + 1  # Include both start and end dates

    def is_active(self):
        """Check if the leave is currently active (approved and current date is within leave period)"""
        if self.status != 'approved':
            return False
        today = datetime.now().date()
        return self.start_date <= today <= self.end_date

class LeaveAttachment(db.Model):
    __tablename__ = 'leave_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    leave_request_id = db.Column(db.Integer, db.ForeignKey('leave_requests.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='leave_attachments')

    def __repr__(self):
        return f'<LeaveAttachment {self.filename}>'
