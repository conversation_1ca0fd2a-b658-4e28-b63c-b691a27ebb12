{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5>إحصائيات الملفات</h5>
                        <p>إجمالي عدد الملفات: <strong>{{ total_files }}</strong></p>
                        <p>إجمالي حجم الملفات: <strong>{{ total_size_str }}</strong></p>
                    </div>

                    <form action="{{ url_for('system.confirm_delete_files') }}" method="POST" id="delete-files-form">
                        <div class="mb-3">
                            <button type="submit" class="btn btn-danger" id="delete-selected-btn" disabled>
                                <i class="fas fa-trash-alt"></i> حذف الملفات المحددة
                            </button>
                        </div>

                        <ul class="nav nav-tabs" id="filesTab" role="tablist">
                            {% for category_key, category in file_categories.items() %}
                            <li class="nav-item" role="presentation">
                                <a class="nav-link {% if loop.first %}active{% endif %}"
                                   id="{{ category_key }}-tab"
                                   data-bs-toggle="tab"
                                   href="#{{ category_key }}"
                                   role="tab"
                                   aria-controls="{{ category_key }}"
                                   aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                    {{ category.title }} ({{ category.files|length }})
                                </a>
                            </li>
                            {% endfor %}
                        </ul>

                        <div class="tab-content" id="filesTabContent">
                            {% for category_key, category in file_categories.items() %}
                            <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                                 id="{{ category_key }}"
                                 role="tabpanel"
                                 aria-labelledby="{{ category_key }}-tab">

                                <div class="mt-3 mb-3">
                                    <div class="alert alert-secondary">
                                        <p>عدد الملفات: <strong><span class="visible-files-count">{{ category.files|length }}</span>/<span class="total-files-count">{{ category.files|length }}</span></strong></p>
                                        <p>إجمالي الحجم: <strong>{{ category.total_size_str }}</strong></p>
                                    </div>

                                    <!-- Search and Filter Controls -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <input type="text" class="form-control file-search" placeholder="بحث بالاسم..." data-category="{{ category_key }}">
                                                <button class="btn btn-outline-secondary clear-search" type="button" data-category="{{ category_key }}">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="btn-group w-100">
                                                <button class="btn btn-outline-primary sort-files" data-category="{{ category_key }}" data-sort="name">
                                                    <i class="fas fa-sort-alpha-down"></i> الاسم
                                                </button>
                                                <button class="btn btn-outline-primary sort-files" data-category="{{ category_key }}" data-sort="size">
                                                    <i class="fas fa-sort-amount-down"></i> الحجم
                                                </button>
                                                <button class="btn btn-outline-primary sort-files" data-category="{{ category_key }}" data-sort="date">
                                                    <i class="fas fa-sort-numeric-down"></i> التاريخ
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if category.files or (category.db_items is defined and category.db_items) %}
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th width="50">
                                                    <div class="form-check">
                                                        <input class="form-check-input select-all" type="checkbox" data-category="{{ category_key }}">
                                                    </div>
                                                </th>
                                                <th>اسم الملف</th>
                                                <th>المسار</th>
                                                <th>الحجم</th>
                                                <th>تاريخ التعديل</th>
                                                <th>معاينة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for file in category.files %}
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input file-checkbox" type="checkbox" name="files" value="{{ file.path }}" data-category="{{ category_key }}">
                                                    </div>
                                                </td>
                                                <td>{{ file.name }}</td>
                                                <td>{{ file.path }}</td>
                                                <td data-size="{{ file.size }}">{{ file.size_str }}</td>
                                                <td>{{ file.modified }}</td>
                                                <td>
                                                    <div class="btn-group">
                                                        {% if file.path.endswith('.jpg') or file.path.endswith('.jpeg') or file.path.endswith('.png') or file.path.endswith('.gif') %}
                                                        <a href="{{ url_for('static', filename='uploads/' + file.path.replace('\\', '/')) }}" target="_blank" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i> معاينة
                                                        </a>
                                                        {% endif %}
                                                        <a href="{{ url_for('static', filename='uploads/' + file.path.replace('\\', '/')) }}" target="_blank" download class="btn btn-sm btn-secondary">
                                                            <i class="fas fa-download"></i> تنزيل
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}

                                            {% if category.db_items is defined %}
                                                {% for item in category.db_items %}
                                                <tr class="table-info">
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input file-checkbox" type="checkbox" name="files" value="{{ item.file_path }}" data-category="{{ category_key }}">
                                                        </div>
                                                    </td>
                                                    <td>{{ item.filename }}</td>
                                                    <td>{{ item.file_path }}</td>
                                                    <td data-size="{{ item.file_size or 0 }}">{{ (item.file_size / 1024)|round(2) if item.file_size else 0 }} كيلوبايت</td>
                                                    <td>{{ item.uploaded_at.strftime('%Y-%m-%d %H:%M:%S') if item.uploaded_at else 'غير معروف' }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            {% if item.file_path.endswith('.jpg') or item.file_path.endswith('.jpeg') or item.file_path.endswith('.png') or item.file_path.endswith('.gif') %}
                                                            <a href="{{ url_for('static', filename=item.file_path) }}" target="_blank" class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i> معاينة
                                                            </a>
                                                            {% endif %}
                                                            <a href="{{ url_for('static', filename=item.file_path) }}" target="_blank" download class="btn btn-sm btn-secondary">
                                                                <i class="fas fa-download"></i> تنزيل
                                                            </a>

                                                            {% if category_key == 'invoice_attachments' %}
                                                            <a href="{{ url_for('finance.view_invoice', id=item.invoice_id) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-file-invoice-dollar"></i> عرض الفاتورة
                                                            </a>
                                                            {% elif category_key == 'transaction_attachments' %}
                                                            <a href="{{ url_for('finance.view_transaction', transaction_id=item.transaction_id) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-exchange-alt"></i> عرض المعاملة
                                                            </a>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-warning mt-3">
                                    لا توجد ملفات في هذه الفئة
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </form>


                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#filesTab a'));
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function (event) {
                event.preventDefault();
                tabTrigger.show();
            });
        });

        // Handle select all checkboxes
        $('.select-all').change(function() {
            var category = $(this).data('category');
            var isChecked = $(this).prop('checked');
            $('.file-checkbox[data-category="' + category + '"]:visible').prop('checked', isChecked);
            updateDeleteButton();
        });

        // Handle individual checkboxes
        $('.file-checkbox').change(function() {
            updateDeleteButton();

            // Update select all checkbox
            var category = $(this).data('category');
            var allChecked = $('.file-checkbox[data-category="' + category + '"]:visible').length ===
                             $('.file-checkbox[data-category="' + category + '"]:visible:checked').length;
            $('.select-all[data-category="' + category + '"]').prop('checked', allChecked);
        });

        // Update delete button state
        function updateDeleteButton() {
            var checkedCount = $('.file-checkbox:checked').length;
            $('#delete-selected-btn').prop('disabled', checkedCount === 0);
        }



        // File search functionality
        $('.file-search').on('keyup', function() {
            var category = $(this).data('category');
            var searchText = $(this).val().toLowerCase();

            $('#' + category + ' tbody tr').each(function() {
                var fileName = $(this).find('td:nth-child(3)').text().toLowerCase(); // File name column
                var filePath = $(this).find('td:nth-child(4)').text().toLowerCase(); // File path column

                if (fileName.includes(searchText) || filePath.includes(searchText)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });

            // Update visible files count
            updateVisibleFilesCount(category);
        });

        // Clear search
        $('.clear-search').click(function() {
            var category = $(this).data('category');
            $('#' + category + ' .file-search').val('');
            $('#' + category + ' tbody tr').show();

            // Update visible files count
            updateVisibleFilesCount(category);
        });

        // Sort files
        $('.sort-files').click(function() {
            var category = $(this).data('category');
            var sortBy = $(this).data('sort');
            var $table = $('#' + category + ' table');
            var $rows = $table.find('tbody tr').toArray();

            // Toggle sort direction
            var sortDir = $(this).hasClass('sort-asc') ? -1 : 1;
            $(this).closest('.btn-group').find('.sort-files').removeClass('sort-asc sort-desc active');
            $(this).addClass(sortDir === 1 ? 'sort-asc active' : 'sort-desc active');

            // Sort rows
            $rows.sort(function(a, b) {
                var aValue, bValue;

                if (sortBy === 'name') {
                    aValue = $(a).find('td:nth-child(3)').text().toLowerCase(); // File name column
                    bValue = $(b).find('td:nth-child(3)').text().toLowerCase();
                    return sortDir * (aValue > bValue ? 1 : -1);
                } else if (sortBy === 'size') {
                    // Extract size in bytes from the data attribute
                    aValue = parseInt($(a).find('td:nth-child(5)').data('size') || 0);
                    bValue = parseInt($(b).find('td:nth-child(5)').data('size') || 0);
                    return sortDir * (aValue - bValue);
                } else if (sortBy === 'date') {
                    aValue = new Date($(a).find('td:nth-child(6)').text());
                    bValue = new Date($(b).find('td:nth-child(6)').text());
                    return sortDir * (aValue - bValue);
                }

                return 0;
            });

            // Reattach sorted rows to table
            $.each($rows, function(index, row) {
                $table.find('tbody').append(row);
            });
        });

        // Function to update visible files count
        function updateVisibleFilesCount(category) {
            var visibleCount = $('#' + category + ' tbody tr:visible').length;
            var totalCount = $('#' + category + ' tbody tr').length;
            $('#' + category + ' .visible-files-count').text(visibleCount);
            $('#' + category + ' .total-files-count').text(totalCount);
        }
    });
</script>
{% endblock %}
