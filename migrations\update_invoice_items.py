import os
import sqlite3

def upgrade():
    # Conectar a la base de datos
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app', 'sparkle.db')
    print(f'Database path: {db_path}')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Verificar si las columnas ya existen en la tabla invoice_items
        cursor.execute("PRAGMA table_info(invoice_items)")
        columns = [row[1] for row in cursor.fetchall()]
        
        # Agregar columnas si no existen
        if 'company_profit_type' not in columns:
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN company_profit_type VARCHAR(10) DEFAULT 'percentage'")
        
        if 'company_profit_value' not in columns:
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN company_profit_value FLOAT DEFAULT 0")
        
        if 'supervisor_id' not in columns:
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN supervisor_id INTEGER")
        
        # Confirmar cambios
        conn.commit()
        print("Migración completada con éxito.")
        
    except Exception as e:
        conn.rollback()
        print(f"Error durante la migración: {e}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    upgrade()
