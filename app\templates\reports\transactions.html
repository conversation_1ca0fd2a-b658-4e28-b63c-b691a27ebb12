{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير المعاملات المالية</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-file-export me-1"></i>تصدير التقرير
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">فلترة المعاملات المالية</h6>
            <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
            </button>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form id="filterForm" action="{{ url_for('report.transactions') }}" method="GET">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="transaction_type" class="form-label">نوع المعاملة</label>
                            <select class="form-select" id="transaction_type" name="transaction_type">
                                <option value="all" {% if transaction_type == 'all' %}selected{% endif %}>الكل</option>
                                <option value="income" {% if transaction_type == 'income' %}selected{% endif %}>إيراد</option>
                                <option value="expense" {% if transaction_type == 'expense' %}selected{% endif %}>مصروف</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="all" {% if category == 'all' %}selected{% endif %}>الكل</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>{{ cat }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                        </div>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>تطبيق الفلتر
                        </button>
                        <a href="{{ url_for('report.transactions') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">المعاملات المالية</h6>
        </div>
        <div class="card-body">
            {% if transactions %}
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>الفئة</th>
                            <th>المبلغ</th>
                            <th>المبلغ بعملة أخرى</th>
                            <th>الوصف</th>
                            <th>الفاتورة المرتبطة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if transaction.transaction_type == 'income' %}
                                <span class="badge bg-success">إيراد</span>
                                {% else %}
                                <span class="badge bg-danger">مصروف</span>
                                {% endif %}
                            </td>
                            <td>{{ transaction.category or 'غير مصنف' }}</td>
                            <td>${{ "%.2f"|format(transaction.amount) }}</td>
                            <td>
                                {% if transaction.alt_amount %}
                                    {% if transaction.currency %}
                                        {{ transaction.currency.symbol }}{{ "%.2f"|format(transaction.alt_amount) }}
                                    {% else %}
                                        ${{ "%.2f"|format(transaction.alt_amount) }}
                                    {% endif %}
                                {% else %}
                                    لا يوجد
                                {% endif %}
                            </td>
                            <td>{{ transaction.description or 'لا يوجد وصف' }}</td>
                            <td>
                                {% if transaction.invoice_id %}
                                <a href="{{ url_for('finance.view_invoice', id=transaction.invoice_id) }}">
                                    {{ transaction.invoice.invoice_number }}
                                </a>
                                {% else %}
                                لا يوجد
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted mb-0">لا توجد معاملات مالية لعرضها</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('report.export', report_type='transactions') }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">تصدير تقرير المعاملات المالية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="export_format" class="form-label">صيغة التصدير</label>
                        <select class="form-select" id="export_format" name="export_format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>

                    <!-- Hidden fields for filters -->
                    <input type="hidden" id="hidden_transaction_type" name="transaction_type" value="{{ transaction_type }}">
                    <input type="hidden" id="hidden_category" name="category" value="{{ category }}">
                    <input type="hidden" id="hidden_start_date" name="start_date" value="{{ start_date }}">
                    <input type="hidden" id="hidden_end_date" name="end_date" value="{{ end_date }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تصدير</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[1, "desc"]]
        });

        // Update hidden fields when form is submitted
        $('#filterForm').on('submit', function() {
            $('#hidden_transaction_type').val($('#transaction_type').val());
            $('#hidden_category').val($('#category').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });

        // Update hidden fields when export button is clicked
        $('#exportModal').on('show.bs.modal', function() {
            $('#hidden_transaction_type').val($('#transaction_type').val());
            $('#hidden_category').val($('#category').val());
            $('#hidden_start_date').val($('#start_date').val());
            $('#hidden_end_date').val($('#end_date').val());
        });
    });
</script>
{% endblock %}
