# نظام إدارة شركة Sparkle Media Agency

نظام إدارة شامل لشركة Sparkle Media Agency يتيح إدارة الموظفين، الأقسام، المشاريع، العملاء، المالية، والمزيد.

## المميزات

- **نظام تسجيل دخول متعدد المستويات**: مع صلاحيات مختلفة (مدير النظام، مشرف، مدير قسم، موظف عادي)
- **لوحة تحكم مركزية**: تعرض الإحصائيات والأنشطة الأخيرة والتنبيهات
- **إدارة الموظفين**: إدارة بيانات الموظفين، الأدوار، التقييمات
- **إدارة الأقسام**: إنشاء وإدارة أقسام الشركة المختلفة
- **إدارة المشاريع والمهام**: تتبع المشاريع والمهام مع تعيين المسؤولين والمواعيد النهائية
- **الإدارة المالية**: تتبع الإيرادات والمصروفات وإنشاء الفواتير
- **قاعدة بيانات العملاء**: إدارة بيانات العملاء ومشاريعهم
- **نظام الإشعارات**: إشعارات داخلية للموظفين والإداريين
- **التقارير والإحصائيات**: تقارير مفصلة عن المشاريع والمالية وأداء الموظفين
- **إدارة المستندات والملفات**: تخزين وتنظيم ملفات المشاريع والعملاء

## المتطلبات

- Python 3.8+
- Flask 2.3.3+
- SQLAlchemy 3.1.1+
- Flask-Login 0.6.2+
- وغيرها من المكتبات المذكورة في ملف requirements.txt

## التثبيت

1. قم بنسخ المستودع:
```
git clone https://github.com/yourusername/sparkle-media-agency.git
cd sparkle-media-agency
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
# في نظام Windows
venv\Scripts\activate
# في نظام Linux/Mac
source venv/bin/activate
```

3. قم بتثبيت المتطلبات:
```
pip install -r requirements.txt
```

4. قم بتشغيل التطبيق:
```
python run.py
```

5. افتح المتصفح على العنوان:
```
http://localhost:5000
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: GolDeN
- **كلمة المرور**: GolDeN2252005

## الهيكل

```
Sparkle Media Agency/
├── app/                    # مجلد التطبيق الرئيسي
│   ├── models/             # نماذج قاعدة البيانات
│   ├── routes/             # مسارات التطبيق
│   ├── static/             # الملفات الثابتة (CSS, JS, الصور)
│   └── templates/          # قوالب HTML
├── migrations/             # ملفات ترحيل قاعدة البيانات
├── requirements.txt        # متطلبات التطبيق
├── run.py                  # ملف تشغيل التطبيق
└── README.md               # ملف التوثيق
```

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الاتصال

Sparkle Media Agency - [<EMAIL>](mailto:<EMAIL>)

رابط المشروع: [https://github.com/yourusername/sparkle-media-agency](https://github.com/yourusername/sparkle-media-agency)
