{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إنشاء عقوبة جديدة</h1>
        <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل العقوبة</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('penalty.create') }}" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="" selected disabled>اختر الموظف...</option>
                            {% for user in users %}
                            <option value="{{ user.id }}">{{ user.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="penalty_type" class="form-label">نوع العقوبة <span class="text-danger">*</span></label>
                        <select class="form-select" id="penalty_type" name="penalty_type" required>
                            <option value="" selected disabled>اختر نوع العقوبة...</option>
                            <option value="verbal_warning">لفت نظر شفوي</option>
                            <option value="written_warning">لفت نظر كتابي (تحذير أول)</option>
                            <option value="written_notice">إنذار كتابي (تحذير ثاني)</option>
                            <option value="suspension">إيقاف مؤقت عن العمل / خصم من الراتب</option>
                            <option value="final_warning">الإنذار النهائي</option>
                            <option value="termination">الفصل من العمل / إنهاء التعاقد</option>
                        </select>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="reason" class="form-label">سبب العقوبة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="details" class="form-label">تفاصيل إضافية</label>
                        <textarea class="form-control" id="details" name="details" rows="3"></textarea>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="start_date" class="form-label">تاريخ بدء العقوبة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="end_date" class="form-label">تاريخ انتهاء العقوبة</label>
                        <input type="date" class="form-control" id="end_date" name="end_date">
                        <div class="form-text">اتركه فارغاً إذا كانت العقوبة دائمة</div>
                    </div>

                    <div class="col-md-4 mb-3 suspension-field d-none">
                        <label for="salary_deduction" class="form-label">قيمة الخصم من الراتب</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="salary_deduction" name="salary_deduction" min="0" step="0.01">
                            <span class="input-group-text">$</span>
                        </div>
                        <div class="form-text">أدخل قيمة الخصم من الراتب (إذا كان ينطبق)</div>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="attachments" class="form-label">المرفقات</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">يمكنك إرفاق ملفات متعددة (مثل المستندات الداعمة أو الأدلة)</div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>إنشاء العقوبة
                        </button>
                        <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#user_id, #penalty_type').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
        
        // Set minimum date for start_date to today
        const today = new Date().toISOString().split('T')[0];
        $('#start_date').val(today);
        
        // Update end_date min value when start_date changes
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
            
            // If end_date is before start_date, clear it
            if ($('#end_date').val() && $('#end_date').val() < $(this).val()) {
                $('#end_date').val('');
            }
        });
        
        // Show/hide salary deduction field based on penalty type
        $('#penalty_type').change(function() {
            if ($(this).val() === 'suspension') {
                $('.suspension-field').removeClass('d-none');
            } else {
                $('.suspension-field').addClass('d-none');
                $('#salary_deduction').val('');
            }
        });
    });
</script>
{% endblock %}
