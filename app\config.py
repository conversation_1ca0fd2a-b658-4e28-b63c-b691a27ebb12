import os
from datetime import timedelta

# Base directory of the application
BASE_DIR = os.path.abspath(os.path.dirname(__file__))

# Secret key for session management
SECRET_KEY = os.environ.get('SECRET_KEY', 'sparkle-media-agency-secret-key')

# Database configuration
SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///' + os.path.join(BASE_DIR, 'sparkle.db'))
SQLALCHEMY_TRACK_MODIFICATIONS = False

# File upload configuration
UPLOAD_FOLDER = os.path.join(BASE_DIR, 'static/uploads')
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload

# Mail server configuration
MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')

# Session configuration
PERMANENT_SESSION_LIFETIME = timedelta(days=1)

# Application name
APP_NAME = "Sparkle Media Agency"
