from app import db
from app.models.notification import Notification
from flask_login import current_user

def send_notification(user_id, title, message, notification_type=None, related_task_id=None, related_project_id=None, link_url=None, link_text=None):
    """
    Send a notification to a user

    Args:
        user_id (int): The ID of the user to send the notification to
        title (str): The title of the notification
        message (str): The message of the notification
        notification_type (str, optional): The type of notification. Defaults to None.
        related_task_id (int, optional): The ID of the related task. Defaults to None.
        related_project_id (int, optional): The ID of the related project. Defaults to None.
        link_url (str, optional): URL to include in the notification. Defaults to None.
        link_text (str, optional): Text to display for the link. Defaults to None.

    Returns:
        Notification: The created notification
    """
    # Check if user has permission to send notifications
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        return None

    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        notification_type=notification_type,
        related_task_id=related_task_id,
        related_project_id=related_project_id,
        link_url=link_url,
        link_text=link_text
    )

    db.session.add(notification)
    db.session.commit()

    return notification

def send_notification_to_multiple_users(user_ids, title, message, notification_type=None, related_task_id=None, related_project_id=None, link_url=None, link_text=None):
    """
    Send a notification to multiple users

    Args:
        user_ids (list): A list of user IDs to send the notification to
        title (str): The title of the notification
        message (str): The message of the notification
        notification_type (str, optional): The type of notification. Defaults to None.
        related_task_id (int, optional): The ID of the related task. Defaults to None.
        related_project_id (int, optional): The ID of the related project. Defaults to None.
        link_url (str, optional): URL to include in the notification. Defaults to None.
        link_text (str, optional): Text to display for the link. Defaults to None.

    Returns:
        list: A list of created notifications
    """
    # Check if user has permission to send notifications
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        return []

    notifications = []

    for user_id in user_ids:
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            related_task_id=related_task_id,
            related_project_id=related_project_id,
            link_url=link_url,
            link_text=link_text
        )

        db.session.add(notification)
        notifications.append(notification)

    db.session.commit()

    return notifications
