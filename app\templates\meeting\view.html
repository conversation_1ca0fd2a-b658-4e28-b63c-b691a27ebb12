{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل الاجتماع</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <a href="{{ url_for('meeting.edit', id=meeting.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('meeting.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الاجتماع</h6>
                </div>
                <div class="card-body">
                    <h4 class="mb-3">{{ meeting.title }}</h4>

                    <div class="row mb-4">
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">التاريخ:</p>
                            <p class="mb-0 fw-bold">{{ meeting.date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">الوقت:</p>
                            <p class="mb-0 fw-bold">{{ meeting.start_time.strftime('%I:%M %p') }} - {{ meeting.end_time.strftime('%I:%M %p') }}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">المدة:</p>
                            <p class="mb-0 fw-bold">{{ meeting.get_duration_minutes() }} دقيقة</p>
                        </div>
                    </div>

                    {% if meeting.location %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">المكان:</p>
                        <p class="mb-0">{{ meeting.location }}</p>
                    </div>
                    {% endif %}

                    {% if meeting.external_link %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">رابط خارجي:</p>
                        <p class="mb-0">
                            <a href="{{ meeting.external_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i>الانضمام إلى الاجتماع
                            </a>
                        </p>
                    </div>
                    {% endif %}

                    {% if meeting.attachments_link %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">رابط المرفقات:</p>
                        <p class="mb-0">
                            <a href="{{ meeting.attachments_link }}" target="_blank" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-file-alt me-1"></i>عرض مرفقات الاجتماع
                            </a>
                        </p>
                    </div>
                    {% endif %}

                    {% if meeting.description %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">الوصف:</p>
                        <p class="mb-0">{{ meeting.description }}</p>
                    </div>
                    {% endif %}

                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">منشئ الاجتماع:</p>
                        <p class="mb-0">{{ meeting.created_by.get_full_name() }}</p>
                    </div>
                    {% endif %}

                    {% if meeting.attachments.count() > 0 %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">المرفقات:</p>
                        <div class="list-group">
                            {% for attachment in meeting.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                    <small class="text-muted d-block">
                                        تم الرفع بواسطة: {{ attachment.uploaded_by.get_full_name() }} -
                                        {{ attachment.uploaded_at.strftime('%Y-%m-%d %I:%M %p') }}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الحاضرون</h6>
                </div>
                <div class="card-body">
                    {% if meeting.attendees %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">الموظفون:</p>
                        <ul class="list-group">
                            {% for attendee in meeting.attendees %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ attendee.get_full_name() }}
                                <span class="badge bg-primary rounded-pill">{{ attendee.department.name if attendee.department else 'بدون قسم' }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    {% if meeting.clients %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">العملاء:</p>
                        <ul class="list-group">
                            {% for client in meeting.clients %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {{ client.name }}
                                <span class="badge bg-info rounded-pill">{{ client.company }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    {% if meeting.external_attendees %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">حاضرون خارجيون:</p>
                        <ul class="list-group">
                            {% for attendee in meeting.external_attendees.split(',') %}
                            <li class="list-group-item">{{ attendee.strip() }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    {% if not meeting.attendees and not meeting.clients and not meeting.external_attendees %}
                    <p class="text-muted text-center">لم يتم تحديد حاضرين</p>
                    {% endif %}
                </div>
            </div>

            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('meeting.edit', id=meeting.id) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i>تعديل الاجتماع
                    </a>
                    <form action="{{ url_for('meeting.delete', id=meeting.id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الاجتماع؟');">
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-1"></i>حذف الاجتماع
                        </button>
                    </form>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Meeting Summaries Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">ملخصات الاجتماع</h6>
                </div>
                <div class="card-body">
                    <!-- Add Summary Form -->
                    {% if current_user in meeting.attendees or current_user.has_role('admin') or current_user.has_role('manager') %}
                    <div class="mb-4">
                        <form action="{{ url_for('meeting.add_summary', meeting_id=meeting.id) }}" method="POST">
                            <div class="mb-3">
                                <label for="summary_content" class="form-label">إضافة ملخص جديد</label>
                                <textarea class="form-control" id="summary_content" name="summary_content" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>إضافة ملخص
                            </button>
                        </form>
                    </div>
                    <hr>
                    {% endif %}

                    <!-- Summaries List -->
                    {% if meeting.summaries.count() > 0 %}
                        <div class="summaries-container">
                            {% for summary in meeting.summaries.order_by(MeetingSummary.created_at.desc()) %}
                                <div class="card mb-3 summary-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="card-subtitle mb-1 text-primary">{{ summary.created_by.get_full_name() }}</h6>
                                                <small class="text-muted">{{ summary.created_at.strftime('%Y-%m-%d %I:%M %p') }}</small>
                                            </div>
                                            {% if current_user.id == summary.created_by_id or current_user.has_role('admin') or current_user.has_role('manager') %}
                                                <div>
                                                    <a href="{{ url_for('meeting.edit_summary', summary_id=summary.id) }}" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit me-1"></i>تعديل
                                                    </a>
                                                    <a href="{{ url_for('meeting.delete_summary', summary_id=summary.id) }}" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash me-1"></i>حذف
                                                    </a>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <p class="card-text">{{ summary.content|nl2br|safe }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">لا توجد ملخصات لهذا الاجتماع حتى الآن</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .summary-card {
        border-radius: 10px;
        border-left: 4px solid #4e73df;
        transition: all 0.3s ease;
    }
    .summary-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}
