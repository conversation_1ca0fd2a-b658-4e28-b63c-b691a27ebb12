{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل طلب الإجازة</h1>
        <a href="{{ url_for('leave.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الإجازة</h6>
                    <div>
                        {% if leave_request.status == 'pending' %}
                        <span class="badge bg-warning text-dark">قيد الانتظار</span>
                        {% elif leave_request.status == 'approved' %}
                        <span class="badge bg-success">تمت الموافقة</span>
                        {% elif leave_request.status == 'rejected' %}
                        <span class="badge bg-danger">مرفوض</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">الموظف:</p>
                            <p class="mb-3 fw-bold">{{ leave_request.user.get_full_name() }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">تاريخ تقديم الطلب:</p>
                            <p class="mb-3 fw-bold">{{ leave_request.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">تاريخ بداية الإجازة:</p>
                            <p class="mb-3 fw-bold">{{ leave_request.start_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">تاريخ نهاية الإجازة:</p>
                            <p class="mb-3 fw-bold">{{ leave_request.end_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 text-muted">مدة الإجازة:</p>
                            <p class="mb-3 fw-bold">{{ leave_request.get_duration_days() }} يوم</p>
                        </div>
                    </div>

                    <div class="mb-4">
                        <p class="mb-1 text-muted">سبب الإجازة:</p>
                        <p class="mb-0">{{ leave_request.reason }}</p>
                    </div>

                    {% if leave_request.created_by %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">تم إنشاء الطلب بواسطة:</p>
                        <p class="mb-0">{{ leave_request.created_by.get_full_name() }}</p>
                    </div>
                    {% endif %}

                    {% if leave_request.status == 'approved' and leave_request.reviewed_by %}
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <div class="mb-4">
                            <p class="mb-1 text-muted">تمت الموافقة بواسطة:</p>
                            <p class="mb-0">{{ leave_request.reviewed_by.get_full_name() }} ({{ leave_request.updated_at.strftime('%Y-%m-%d %H:%M') }})</p>
                        </div>
                        {% endif %}
                    {% endif %}

                    {% if leave_request.status == 'rejected' %}
                    <div class="mb-4">
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <p class="mb-1 text-muted">تم الرفض بواسطة:</p>
                        <p class="mb-0">{{ leave_request.reviewed_by.get_full_name() }} ({{ leave_request.updated_at.strftime('%Y-%m-%d %H:%M') }})</p>
                        {% endif %}

                        <p class="mb-1 text-muted mt-3">سبب الرفض:</p>
                        <p class="mb-0 text-danger">{{ leave_request.rejection_reason }}</p>
                    </div>
                    {% endif %}

                    {% if leave_request.status == 'approved' and leave_request.approval_reason %}
                    <div class="mb-4">
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <p class="mb-1 text-muted">تمت الموافقة بواسطة:</p>
                        <p class="mb-0">{{ leave_request.reviewed_by.get_full_name() }} ({{ leave_request.updated_at.strftime('%Y-%m-%d %H:%M') }})</p>
                        {% endif %}

                        <p class="mb-1 text-muted mt-3">سبب الموافقة:</p>
                        <p class="mb-0 text-success">{{ leave_request.approval_reason }}</p>
                    </div>
                    {% endif %}

                    {% if leave_request.attachments.count() > 0 %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">المرفقات:</p>
                        <div class="list-group">
                            {% for attachment in leave_request.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                    <small class="text-muted d-block">
                                        تم الرفع بواسطة: {{ attachment.uploaded_by.get_full_name() }} -
                                        {{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>

                                {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == attachment.uploaded_by_id %}
                                <form action="{{ url_for('leave.delete_attachment', attachment_id=attachment.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا المرفق؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                </div>
                <div class="card-body">
                    {% if leave_request.status == 'pending' %}
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <button type="button" class="btn btn-success w-100 mb-3" data-bs-toggle="modal" data-bs-target="#approveModal">
                            <i class="fas fa-check me-1"></i>الموافقة على الطلب
                        </button>

                        <button type="button" class="btn btn-danger w-100 mb-3" data-bs-toggle="modal" data-bs-target="#rejectModal">
                            <i class="fas fa-times me-1"></i>رفض الطلب
                        </button>
                        {% endif %}

                        <a href="{{ url_for('leave.edit', id=leave_request.id) }}" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-edit me-1"></i>تعديل الطلب
                        </a>

                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == leave_request.user_id %}
                        <form action="{{ url_for('leave.delete', id=leave_request.id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الطلب؟');">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-1"></i>حذف الطلب
                            </button>
                        </form>
                        {% endif %}
                    {% else %}
                        {% if current_user.has_role('admin') %}
                        <a href="{{ url_for('leave.edit', id=leave_request.id) }}" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-edit me-1"></i>تعديل الطلب
                        </a>
                        {% else %}
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-1"></i>
                            تم اتخاذ قرار بشأن هذا الطلب ولا يمكن تغييره.
                        </div>
                        {% endif %}

                        {% if current_user.has_role('admin') %}
                        <form action="{{ url_for('leave.delete', id=leave_request.id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الطلب؟');">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-1"></i>حذف الطلب
                            </button>
                        </form>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('leave.reject', id=leave_request.id) }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">رفض طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض <span class="text-muted">(اختياري)</span></label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3"></textarea>
                        <div class="form-text">يمكنك إضافة سبب لرفض طلب الإجازة (اختياري)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الرفض</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ url_for('leave.approve', id=leave_request.id) }}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveModalLabel">الموافقة على طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approval_reason" class="form-label">سبب الموافقة <span class="text-muted">(اختياري)</span></label>
                        <textarea class="form-control" id="approval_reason" name="approval_reason" rows="3"></textarea>
                        <div class="form-text">يمكنك إضافة سبب للموافقة على طلب الإجازة (اختياري)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تأكيد الموافقة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
