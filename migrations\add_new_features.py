import os
import sqlite3
import sys
from datetime import datetime

def find_db_files():
    """Find all SQLite database files in the current directory and subdirectories."""
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    return db_files

def migrate_db(db_path):
    """Add new tables for leave requests, meetings, honorary ranks, and penalties."""
    print(f"Attempting to migrate database: {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create leave_requests table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='leave_requests'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE leave_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                reason TEXT NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                rejection_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                created_by_id INTEGER,
                reviewed_by_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id),
                FOREIGN KEY (reviewed_by_id) REFERENCES users (id)
            )
            ''')
            print("Created leave_requests table")
        except sqlite3.OperationalError as e:
            print(f"Error creating leave_requests table: {e}")
    else:
        print("leave_requests table already exists")
    
    # Create leave_attachments table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='leave_attachments'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE leave_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(50),
                file_size INTEGER,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                leave_request_id INTEGER NOT NULL,
                uploaded_by_id INTEGER NOT NULL,
                FOREIGN KEY (leave_request_id) REFERENCES leave_requests (id),
                FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
            )
            ''')
            print("Created leave_attachments table")
        except sqlite3.OperationalError as e:
            print(f"Error creating leave_attachments table: {e}")
    else:
        print("leave_attachments table already exists")
    
    # Create meetings table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meetings'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE meetings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                date DATE NOT NULL,
                start_time TIME NOT NULL,
                end_time TIME NOT NULL,
                location VARCHAR(255),
                external_attendees TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
            ''')
            print("Created meetings table")
        except sqlite3.OperationalError as e:
            print(f"Error creating meetings table: {e}")
    else:
        print("meetings table already exists")
    
    # Create meeting_attendees table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meeting_attendees'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE meeting_attendees (
                meeting_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                PRIMARY KEY (meeting_id, user_id),
                FOREIGN KEY (meeting_id) REFERENCES meetings (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            print("Created meeting_attendees table")
        except sqlite3.OperationalError as e:
            print(f"Error creating meeting_attendees table: {e}")
    else:
        print("meeting_attendees table already exists")
    
    # Create meeting_clients table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meeting_clients'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE meeting_clients (
                meeting_id INTEGER NOT NULL,
                client_id INTEGER NOT NULL,
                PRIMARY KEY (meeting_id, client_id),
                FOREIGN KEY (meeting_id) REFERENCES meetings (id),
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
            ''')
            print("Created meeting_clients table")
        except sqlite3.OperationalError as e:
            print(f"Error creating meeting_clients table: {e}")
    else:
        print("meeting_clients table already exists")
    
    # Create meeting_attachments table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meeting_attachments'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE meeting_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(50),
                file_size INTEGER,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                meeting_id INTEGER NOT NULL,
                uploaded_by_id INTEGER NOT NULL,
                FOREIGN KEY (meeting_id) REFERENCES meetings (id),
                FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
            )
            ''')
            print("Created meeting_attachments table")
        except sqlite3.OperationalError as e:
            print(f"Error creating meeting_attachments table: {e}")
    else:
        print("meeting_attachments table already exists")
    
    # Create honorary_ranks table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='honorary_ranks'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE honorary_ranks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                color VARCHAR(20) DEFAULT '#007bff',
                icon VARCHAR(50) DEFAULT 'fa-award',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by_id INTEGER,
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
            ''')
            print("Created honorary_ranks table")
        except sqlite3.OperationalError as e:
            print(f"Error creating honorary_ranks table: {e}")
    else:
        print("honorary_ranks table already exists")
    
    # Create user_honorary_ranks table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_honorary_ranks'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE user_honorary_ranks (
                user_id INTEGER NOT NULL,
                honorary_rank_id INTEGER NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                assigned_by_id INTEGER,
                PRIMARY KEY (user_id, honorary_rank_id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (honorary_rank_id) REFERENCES honorary_ranks (id),
                FOREIGN KEY (assigned_by_id) REFERENCES users (id)
            )
            ''')
            print("Created user_honorary_ranks table")
        except sqlite3.OperationalError as e:
            print(f"Error creating user_honorary_ranks table: {e}")
    else:
        print("user_honorary_ranks table already exists")
    
    # Create penalties table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE penalties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                penalty_type VARCHAR(50) NOT NULL,
                reason TEXT NOT NULL,
                details TEXT,
                start_date DATE NOT NULL,
                end_date DATE,
                salary_deduction FLOAT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                issued_by_id INTEGER NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (issued_by_id) REFERENCES users (id)
            )
            ''')
            print("Created penalties table")
        except sqlite3.OperationalError as e:
            print(f"Error creating penalties table: {e}")
    else:
        print("penalties table already exists")
    
    # Create penalty_attachments table if it doesn't exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalty_attachments'")
    if not cursor.fetchone():
        try:
            cursor.execute('''
            CREATE TABLE penalty_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(50),
                file_size INTEGER,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                penalty_id INTEGER NOT NULL,
                uploaded_by_id INTEGER NOT NULL,
                FOREIGN KEY (penalty_id) REFERENCES penalties (id),
                FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
            )
            ''')
            print("Created penalty_attachments table")
        except sqlite3.OperationalError as e:
            print(f"Error creating penalty_attachments table: {e}")
    else:
        print("penalty_attachments table already exists")
    
    # Update notification_type column in notifications table to include new types
    cursor.execute("PRAGMA table_info(notifications)")
    columns = cursor.fetchall()
    notification_type_column = next((col for col in columns if col[1] == 'notification_type'), None)
    
    if notification_type_column:
        # Add new notification types if needed
        # Note: SQLite doesn't support ALTER TABLE to modify column constraints,
        # so we're just documenting the new types here
        print("Notification types now include: leave, meeting, honorary_rank, penalty")
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    return True

if __name__ == '__main__':
    db_files = find_db_files()
    
    if not db_files:
        print("No database files found")
        sys.exit(1)
    
    print(f"Found {len(db_files)} database files: {db_files}")
    
    success = False
    for db_file in db_files:
        if migrate_db(db_file):
            success = True
    
    if success:
        print("Migration completed successfully for at least one database")
        sys.exit(0)
    else:
        print("Migration failed for all databases")
        sys.exit(1)
