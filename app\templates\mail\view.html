{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ mail.subject }}</h1>
    <div>
        <a href="{{ url_for('mail.inbox') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة
        </a>
        <div class="btn-group ms-2">
            <a href="{{ url_for('mail.compose', reply_to=mail.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-reply me-1"></i>رد
            </a>
            <a href="{{ url_for('mail.compose', forward=mail.id) }}" class="btn btn-outline-info">
                <i class="fas fa-share me-1"></i>إعادة توجيه
            </a>
            <a href="{{ url_for('mail.confirm_delete', id=mail.id) }}" class="btn btn-outline-danger">
                <i class="fas fa-trash me-1"></i>حذف
            </a>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="row">
            <div class="col-md-6">
                <strong>من:</strong> {{ mail.sender.first_name }} {{ mail.sender.last_name }}
            </div>
            <div class="col-md-6 text-md-end">
                <strong>التاريخ:</strong> {{ mail.created_at.strftime('%Y-%m-%d %H:%M') }}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <strong>إلى:</strong> {{ mail.get_all_recipients_names() }}
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="mail-content mb-4">
            {{ mail.body|safe }}
        </div>

        {% if mail.attachments %}
        <hr>
        <div class="attachments">
            <h5><i class="fas fa-paperclip me-2"></i>المرفقات ({{ mail.attachments|length }})</h5>
            <div class="row">
                {% for attachment in mail.attachments %}
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="attachment-icon me-3">
                                    {% if attachment.mime_type.startswith('image/') %}
                                    <i class="fas fa-file-image fa-2x text-primary"></i>
                                    {% elif attachment.mime_type.startswith('application/pdf') %}
                                    <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                    {% elif attachment.mime_type.startswith('application/msword') or attachment.mime_type.startswith('application/vnd.openxmlformats-officedocument.wordprocessingml') %}
                                    <i class="fas fa-file-word fa-2x text-primary"></i>
                                    {% elif attachment.mime_type.startswith('application/vnd.ms-excel') or attachment.mime_type.startswith('application/vnd.openxmlformats-officedocument.spreadsheetml') %}
                                    <i class="fas fa-file-excel fa-2x text-success"></i>
                                    {% elif attachment.mime_type.startswith('application/vnd.ms-powerpoint') or attachment.mime_type.startswith('application/vnd.openxmlformats-officedocument.presentationml') %}
                                    <i class="fas fa-file-powerpoint fa-2x text-warning"></i>
                                    {% else %}
                                    <i class="fas fa-file fa-2x text-secondary"></i>
                                    {% endif %}
                                </div>
                                <div class="attachment-info">
                                    <h6 class="mb-0">{{ attachment.filename }}</h6>
                                    <small class="text-muted">{{ (attachment.file_size / 1024)|round(1) }} KB</small>
                                </div>
                            </div>
                            <div class="text-end mt-2">
                                <a href="{{ url_for('mail.download_attachment', id=attachment.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download me-1"></i>تنزيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>


{% endblock %}
