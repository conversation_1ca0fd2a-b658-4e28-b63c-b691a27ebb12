# اختبار النظام المحاسبي المتكامل
import sqlite3
from datetime import date, datetime
from decimal import Decimal

def test_accounting_system():
    """اختبار شامل للنظام المحاسبي المتكامل"""
    
    print("🧪 بدء اختبار النظام المحاسبي المتكامل...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    try:
        # 1. اختبار وجود الجداول الجديدة
        print("\n1️⃣ اختبار وجود الجداول...")
        
        tables_to_check = [
            'chart_of_accounts',
            'journal_entries', 
            'journal_items',
            'vendors',
            'vendor_bills',
            'vendor_bill_items',
            'payments_received',
            'payments_made',
            'fiscal_years',
            'tax_rates'
        ]
        
        for table in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            if result:
                print(f"   ✅ جدول {table} موجود")
            else:
                print(f"   ❌ جدول {table} غير موجود")
        
        # 2. اختبار شجرة الحسابات
        print("\n2️⃣ اختبار شجرة الحسابات...")
        
        cursor.execute("SELECT COUNT(*) FROM chart_of_accounts")
        accounts_count = cursor.fetchone()[0]
        print(f"   📊 عدد الحسابات: {accounts_count}")
        
        # عرض بعض الحسابات الرئيسية
        cursor.execute("""
            SELECT account_code, account_name, account_type, balance 
            FROM chart_of_accounts 
            WHERE account_code IN ('1010', '1020', '2010', '4010', '5010')
            ORDER BY account_code
        """)
        
        main_accounts = cursor.fetchall()
        for code, name, type_, balance in main_accounts:
            print(f"   💰 {code} - {name} ({type_}): ${balance}")
        
        # 3. اختبار السنوات المالية
        print("\n3️⃣ اختبار السنوات المالية...")
        
        cursor.execute("SELECT name, start_date, end_date, is_current FROM fiscal_years")
        fiscal_years = cursor.fetchall()
        for name, start, end, is_current in fiscal_years:
            status = "الحالية" if is_current else "غير نشطة"
            print(f"   📅 السنة المالية {name}: {start} إلى {end} ({status})")
        
        # 4. اختبار معدلات الضرائب
        print("\n4️⃣ اختبار معدلات الضرائب...")
        
        cursor.execute("SELECT name, rate, is_active FROM tax_rates")
        tax_rates = cursor.fetchall()
        for name, rate, is_active in tax_rates:
            status = "نشط" if is_active else "غير نشط"
            print(f"   🏛️ {name}: {rate}% ({status})")
        
        # 5. اختبار الحقول الجديدة في جدول الفواتير
        print("\n5️⃣ اختبار تحديثات جدول الفواتير...")
        
        cursor.execute("PRAGMA table_info(invoices)")
        columns = cursor.fetchall()
        
        new_fields = ['invoice_type', 'payment_terms', 'amount_due', 'journal_entry_id', 'tax_amount', 'discount_amount']
        existing_fields = [col[1] for col in columns]
        
        for field in new_fields:
            if field in existing_fields:
                print(f"   ✅ حقل {field} موجود في جدول الفواتير")
            else:
                print(f"   ❌ حقل {field} غير موجود في جدول الفواتير")
        
        # 6. اختبار الحقول الجديدة في جدول العملاء
        print("\n6️⃣ اختبار تحديثات جدول العملاء...")
        
        cursor.execute("PRAGMA table_info(clients)")
        columns = cursor.fetchall()
        
        new_client_fields = ['credit_limit', 'payment_terms', 'tax_id', 'is_active']
        existing_client_fields = [col[1] for col in columns]
        
        for field in new_client_fields:
            if field in existing_client_fields:
                print(f"   ✅ حقل {field} موجود في جدول العملاء")
            else:
                print(f"   ❌ حقل {field} غير موجود في جدول العملاء")
        
        # 7. اختبار إنشاء قيد يومية تجريبي
        print("\n7️⃣ اختبار إنشاء قيد يومية تجريبي...")
        
        try:
            # إنشاء قيد تجريبي
            test_entry_number = f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            cursor.execute("""
                INSERT INTO journal_entries 
                (entry_number, entry_date, description, total_debit, total_credit, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (test_entry_number, date.today(), "قيد تجريبي للاختبار", 1000.00, 1000.00, 'draft'))
            
            entry_id = cursor.lastrowid
            
            # إضافة بنود القيد
            # بند مدين: النقد في البنك
            cursor.execute("""
                SELECT id FROM chart_of_accounts WHERE account_code = '1010'
            """)
            cash_account_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT INTO journal_items 
                (journal_entry_id, account_id, debit, credit, description)
                VALUES (?, ?, ?, ?, ?)
            """, (entry_id, cash_account_id, 1000.00, 0, "بند مدين تجريبي"))
            
            # بند دائن: الإيرادات
            cursor.execute("""
                SELECT id FROM chart_of_accounts WHERE account_code = '4010'
            """)
            revenue_account_id = cursor.fetchone()[0]
            
            cursor.execute("""
                INSERT INTO journal_items 
                (journal_entry_id, account_id, debit, credit, description)
                VALUES (?, ?, ?, ?, ?)
            """, (entry_id, revenue_account_id, 0, 1000.00, "بند دائن تجريبي"))
            
            print(f"   ✅ تم إنشاء قيد تجريبي برقم {test_entry_number}")
            
            # حذف القيد التجريبي
            cursor.execute("DELETE FROM journal_items WHERE journal_entry_id = ?", (entry_id,))
            cursor.execute("DELETE FROM journal_entries WHERE id = ?", (entry_id,))
            print(f"   🗑️ تم حذف القيد التجريبي")
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء القيد التجريبي: {e}")
        
        # 8. اختبار إحصائيات النظام
        print("\n8️⃣ إحصائيات النظام...")
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        invoices_count = cursor.fetchone()[0]
        print(f"   📄 عدد الفواتير: {invoices_count}")
        
        # عدد العملاء
        cursor.execute("SELECT COUNT(*) FROM clients")
        clients_count = cursor.fetchone()[0]
        print(f"   👥 عدد العملاء: {clients_count}")
        
        # عدد المشاريع
        cursor.execute("SELECT COUNT(*) FROM projects")
        projects_count = cursor.fetchone()[0]
        print(f"   📋 عدد المشاريع: {projects_count}")
        
        # عدد المعاملات المالية
        cursor.execute("SELECT COUNT(*) FROM transactions")
        transactions_count = cursor.fetchone()[0]
        print(f"   💳 عدد المعاملات المالية: {transactions_count}")
        
        print("\n🎉 اكتمل اختبار النظام المحاسبي بنجاح!")
        print("\n📋 ملخص النتائج:")
        print("   ✅ جميع الجداول الجديدة تم إنشاؤها بنجاح")
        print("   ✅ شجرة الحسابات تم إدراجها بنجاح")
        print("   ✅ السنوات المالية ومعدلات الضرائب تم إعدادها")
        print("   ✅ تحديثات جداول الفواتير والعملاء تمت بنجاح")
        print("   ✅ آلية إنشاء القيود المحاسبية تعمل بشكل صحيح")
        
        print("\n🚀 النظام المالي المتكامل جاهز للاستخدام!")
        print("\n🔗 الروابط المهمة:")
        print("   📊 لوحة التحكم المحاسبية: http://127.0.0.1:5000/accounting/dashboard")
        print("   💰 شجرة الحسابات: http://127.0.0.1:5000/accounting/chart-of-accounts")
        print("   📖 قيود اليومية: http://127.0.0.1:5000/accounting/journal-entries")
        print("   💵 تسجيل دفعة: http://127.0.0.1:5000/accounting/payments/receive")
        print("   📈 التقارير المالية: http://127.0.0.1:5000/accounting/reports")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    
    finally:
        conn.close()
    
    return True

def test_api_endpoints():
    """اختبار نقاط النهاية للـ API"""
    import requests
    
    print("\n🌐 اختبار APIs...")
    
    base_url = "http://127.0.0.1:5000"
    
    # قائمة APIs للاختبار
    api_endpoints = [
        "/accounting/api/accounts",
        "/accounting/api/dashboard/kpis",
        "/accounting/api/reports/profit-loss",
        "/accounting/api/reports/balance-sheet"
    ]
    
    for endpoint in api_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {endpoint} - يعمل بشكل صحيح")
            else:
                print(f"   ⚠️ {endpoint} - كود الاستجابة: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {endpoint} - خطأ في الاتصال: {e}")

if __name__ == '__main__':
    # تشغيل الاختبارات
    success = test_accounting_system()
    
    if success:
        print("\n" + "="*60)
        print("🎊 تهانينا! تم تطبيق النظام المالي المتكامل بنجاح!")
        print("="*60)
        
        # اختبار APIs (اختياري)
        try:
            test_api_endpoints()
        except Exception as e:
            print(f"تعذر اختبار APIs: {e}")
    else:
        print("\n❌ فشل في تطبيق النظام. يرجى مراجعة الأخطاء أعلاه.")
