import sqlite3
import os
from app.config import SQL<PERSON>CHEMY_DATABASE_URI

def run_migration():
    """
    Add verification links tables for transactions and invoices
    """
    print("Running migration: add_verification_links.py")
    
    # Extract database path from URI
    db_path = SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')
    
    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if the transaction_verification_links table already exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='transaction_verification_links'")
    if not cursor.fetchone():
        # Create transaction_verification_links table
        cursor.execute("""
        CREATE TABLE transaction_verification_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url VARCHAR(500) NOT NULL,
            description VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            transaction_id INTEGER NOT NULL,
            added_by_id INTEGER NOT NULL,
            FOREIGN KEY (transaction_id) REFERENCES transactions (id),
            FOREIGN KEY (added_by_id) REFERENCES users (id)
        )
        """)
        print("Created transaction_verification_links table")
    else:
        print("transaction_verification_links table already exists")
    
    # Check if the invoice_verification_links table already exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_verification_links'")
    if not cursor.fetchone():
        # Create invoice_verification_links table
        cursor.execute("""
        CREATE TABLE invoice_verification_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url VARCHAR(500) NOT NULL,
            description VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            invoice_id INTEGER NOT NULL,
            added_by_id INTEGER NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES invoices (id),
            FOREIGN KEY (added_by_id) REFERENCES users (id)
        )
        """)
        print("Created invoice_verification_links table")
    else:
        print("invoice_verification_links table already exists")
    
    # Close the connection
    conn.commit()
    conn.close()
    
    print("Migration completed: add_verification_links.py")

if __name__ == "__main__":
    run_migration()
