{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>رفع ملف لمشروع {{ project.name }}</h1>
    <div>
        <a href="{{ url_for('project.files', id=project.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للملفات
        </a>
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
            <i class="fas fa-project-diagram me-1"></i>عرض المشروع
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">رفع ملف جديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('project.upload_file', id=project.id) }}" enctype="multipart/form-data">
            <div class="mb-4">
                <label for="file" class="form-label">اختر الملف (اختياري)</label>
                <input type="file" class="form-control" id="file" name="file">
                <div class="form-text">
                    الحد الأقصى لحجم الملف: 16 ميجابايت. يمكنك تخطي هذا الحقل إذا كنت ترغب فقط في إضافة ملاحظات أو روابط.
                </div>
            </div>

            <div class="mb-4">
                <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أضف ملاحظات حول هذا الملف"></textarea>
                <div class="form-text">
                    يمكنك إضافة ملاحظات أو وصف للملف هنا.
                </div>
            </div>

            <div class="mb-4">
                <label for="links" class="form-label">الروابط (اختياري، افصل بين الروابط بفاصلة)</label>
                <textarea class="form-control" id="links" name="links" rows="3" placeholder="https://example.com, https://another-example.com"></textarea>
                <div class="form-text">
                    يمكنك إضافة رابط واحد أو أكثر، مفصولة بفواصل. يمكنك رفع روابط بدون الحاجة لرفع ملف.
                </div>
            </div>

            <div id="file-preview" class="mb-4 d-none">
                <div class="card">
                    <div class="card-body">
                        <h6 class="mb-2">معاينة الملف</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file fa-2x me-3"></i>
                            <div>
                                <p class="mb-0 file-name">اسم الملف</p>
                                <small class="text-muted file-info">حجم الملف</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('project.files', id=project.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ</button>
            </div>
        </form>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('file');
        const filePreview = document.getElementById('file-preview');
        const fileName = document.querySelector('.file-name');
        const fileInfo = document.querySelector('.file-info');

        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];

                // Show file preview
                filePreview.classList.remove('d-none');
                fileName.textContent = file.name;

                // Format file size
                let fileSize = file.size;
                let fileSizeFormatted = '';

                if (fileSize < 1024) {
                    fileSizeFormatted = fileSize + ' bytes';
                } else if (fileSize < 1024 * 1024) {
                    fileSizeFormatted = (fileSize / 1024).toFixed(2) + ' KB';
                } else {
                    fileSizeFormatted = (fileSize / (1024 * 1024)).toFixed(2) + ' MB';
                }

                fileInfo.textContent = fileSizeFormatted;

                // Set appropriate icon based on file type
                const fileIcon = document.querySelector('.fa-file');
                const fileType = file.type;

                if (fileType.startsWith('image/')) {
                    fileIcon.className = 'fas fa-file-image fa-2x me-3';
                } else if (fileType === 'application/pdf') {
                    fileIcon.className = 'fas fa-file-pdf fa-2x me-3';
                } else if (fileType.includes('word') || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    fileIcon.className = 'fas fa-file-word fa-2x me-3';
                } else if (fileType.includes('excel') || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    fileIcon.className = 'fas fa-file-excel fa-2x me-3';
                } else if (fileType.includes('powerpoint') || fileType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
                    fileIcon.className = 'fas fa-file-powerpoint fa-2x me-3';
                } else if (fileType.startsWith('text/')) {
                    fileIcon.className = 'fas fa-file-alt fa-2x me-3';
                } else if (fileType.startsWith('video/')) {
                    fileIcon.className = 'fas fa-file-video fa-2x me-3';
                } else if (fileType.startsWith('audio/')) {
                    fileIcon.className = 'fas fa-file-audio fa-2x me-3';
                } else if (fileType === 'application/zip' || fileType === 'application/x-zip-compressed') {
                    fileIcon.className = 'fas fa-file-archive fa-2x me-3';
                } else if (fileType === 'application/x-javascript' || fileType === 'text/javascript') {
                    fileIcon.className = 'fas fa-file-code fa-2x me-3';
                } else {
                    fileIcon.className = 'fas fa-file fa-2x me-3';
                }
            } else {
                filePreview.classList.add('d-none');
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
