{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تحديثات المشروع: {{ project.name }}</h1>
    <div>
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للمشروع
        </a>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == project.manager_id %}
        <a href="{{ url_for('project.create_update', id=project.id) }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إضافة تحديث
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <!-- Pinned Updates -->
        {% if pinned_updates %}
        <div class="card shadow mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-thumbtack me-2"></i>التحديثات المثبتة</h5>
            </div>
            <div class="card-body">
                {% for update in pinned_updates %}
                <div class="update-item mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                    <div class="d-flex justify-content-between align-items-start">
                        <h4>{{ update.title }}</h4>
                        <div class="btn-group">
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == project.manager_id %}
                            <form action="{{ url_for('project.toggle_pin_update', update_id=update.id) }}" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-warning" title="إلغاء التثبيت">
                                    <i class="fas fa-thumbtack"></i>
                                </button>
                            </form>
                            {% endif %}

                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == project.manager_id or current_user.id == update.created_by_id %}
                            <a href="{{ url_for('project.edit_update', update_id=update.id) }}" class="btn btn-sm btn-primary" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('project.delete_update', update_id=update.id) }}" class="btn btn-sm btn-danger" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-muted small mb-2">
                        <i class="fas fa-user me-1"></i>{{ update.created_by.get_full_name() }} |
                        <i class="fas fa-calendar me-1"></i>{{ update.created_at.strftime('%Y-%m-%d %H:%M') }}
                        {% if update.updated_at and update.updated_at != update.created_at %}
                        | <i class="fas fa-edit me-1"></i>تم التعديل {{ update.updated_at.strftime('%Y-%m-%d %H:%M') }}
                        {% endif %}
                    </div>
                    <div class="update-content">
                        {{ update.content|safe }}
                    </div>

                    {% if update.links %}
                    <div class="update-links mt-3">
                        <h6><i class="fas fa-link me-1"></i>الروابط:</h6>
                        <ul class="list-unstyled">
                            {% for link in update.get_links_list() %}
                            <li>
                                <a href="{{ link }}" target="_blank" rel="noopener noreferrer">
                                    {{ link }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>


                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Regular Updates -->
        <div class="card shadow">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">جميع التحديثات</h5>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <select id="per-page-select" class="form-select form-select-sm" onchange="changePerPage(this.value)">
                            <option value="25" {% if per_page == 25 %}selected{% endif %}>25 في الصفحة</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50 في الصفحة</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100 في الصفحة</option>
                        </select>
                    </div>
                    <span class="text-muted small">
                        إجمالي التحديثات: {{ regular_updates.total }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                {% if regular_updates.items %}
                {% for update in regular_updates.items %}
                <div class="update-item mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                    <div class="d-flex justify-content-between align-items-start">
                        <h4>{{ update.title }}</h4>
                        <div class="btn-group">
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == project.manager_id %}
                            <form action="{{ url_for('project.toggle_pin_update', update_id=update.id) }}" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-outline-warning" title="تثبيت">
                                    <i class="fas fa-thumbtack"></i>
                                </button>
                            </form>
                            {% endif %}

                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == project.manager_id or current_user.id == update.created_by_id %}
                            <a href="{{ url_for('project.edit_update', update_id=update.id) }}" class="btn btn-sm btn-primary" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('project.delete_update', update_id=update.id) }}" class="btn btn-sm btn-danger" title="حذف">
                                <i class="fas fa-trash"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-muted small mb-2">
                        <i class="fas fa-user me-1"></i>{{ update.created_by.get_full_name() }} |
                        <i class="fas fa-calendar me-1"></i>{{ update.created_at.strftime('%Y-%m-%d %H:%M') }}
                        {% if update.updated_at and update.updated_at != update.created_at %}
                        | <i class="fas fa-edit me-1"></i>تم التعديل {{ update.updated_at.strftime('%Y-%m-%d %H:%M') }}
                        {% endif %}
                    </div>
                    <div class="update-content">
                        {{ update.content|safe }}
                    </div>

                    {% if update.links %}
                    <div class="update-links mt-3">
                        <h6><i class="fas fa-link me-1"></i>الروابط:</h6>
                        <ul class="list-unstyled">
                            {% for link in update.get_links_list() %}
                            <li>
                                <a href="{{ link }}" target="_blank" rel="noopener noreferrer">
                                    {{ link }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>


                {% endfor %}
                {% else %}
                <p class="text-muted mb-0">لا يوجد تحديثات لهذا المشروع حاليًا.</p>
                {% endif %}

                <!-- Pagination -->
                {% if regular_updates.pages > 1 %}
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <!-- Previous page -->
                            {% if regular_updates.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('project.updates', id=project.id, page=regular_updates.prev_num, per_page=per_page) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            <!-- Page numbers -->
                            {% for page_num in regular_updates.iter_pages(left_edge=2, right_edge=2, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == regular_updates.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="{{ url_for('project.updates', id=project.id, page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('project.updates', id=project.id, page=page_num, per_page=per_page) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">...</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            <!-- Next page -->
                            {% if regular_updates.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('project.updates', id=project.id, page=regular_updates.next_num, per_page=per_page) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .update-item {
        position: relative;
    }
    .update-content {
        white-space: pre-line;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    function changePerPage(value) {
        // Redirect to the same page with the new per_page parameter
        window.location.href = "{{ url_for('project.updates', id=project.id) }}" + "?per_page=" + value;
    }
</script>
{% endblock %}
