# دليل التطبيق العملي للنظام المالي المتكامل

## البدء الفوري - الخطوات العملية

### الخطوة 1: إعداد البيئة والنسخ الاحتياطي

```bash
# 1. إنشاء نسخة احتياطية من قاعدة البيانات الحالية
cp app.db app_backup_$(date +%Y%m%d).db

# 2. إنشاء فرع جديد للتطوير
git checkout -b enhanced-finance-system

# 3. تحديث requirements.txt
echo "decimal==1.70" >> requirements.txt
pip install -r requirements.txt
```

### الخطوة 2: إنشاء ملفات الهجرة

```bash
# إنشاء ملف هجرة جديد
flask db revision -m "Create enhanced accounting system"
```

ثم انسخ محتوى `migration_script.py` إلى ملف الهجرة الذي تم إنشاؤه في مجلد `migrations/versions/`.

### الخطوة 3: إنشاء النماذج المحسنة

قم بإنشاء ملف جديد `app/models/accounting.py`:

```python
# app/models/accounting.py
from datetime import datetime, date
from decimal import Decimal
from app import db
from sqlalchemy import event
from sqlalchemy.orm import validates

# انسخ محتوى enhanced_models.py هنا
```

### الخطوة 4: تحديث ملف النماذج الحالي

```python
# app/models/finance.py - إضافة في نهاية الملف

# تحسين نموذج Invoice الحالي
def enhance_existing_invoice():
    """إضافة الحقول الجديدة للفاتورة الحالية"""
    
    # إضافة خصائص جديدة لكلاس Invoice
    Invoice.invoice_type = db.Column(db.Enum('sales', 'service'), default='service')
    Invoice.payment_terms = db.Column(db.Integer, default=30)
    Invoice.amount_due = db.Column(db.Numeric(15, 2))
    Invoice.journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'))
    Invoice.tax_amount = db.Column(db.Numeric(15, 2), default=0)
    Invoice.discount_amount = db.Column(db.Numeric(15, 2), default=0)
    
    # إضافة العلاقة مع قيد اليومية
    Invoice.journal_entry = db.relationship('JournalEntry', backref='invoice')
    
    # تحسين دالة mark_as_paid
    def enhanced_mark_as_paid(self):
        """تحسين دالة تحديد الفاتورة كمدفوعة مع القيد المحاسبي"""
        from app.models.accounting import AccountingService
        
        self.status = 'paid'
        self.amount_due = 0
        
        # إنشاء قيد محاسبي للدفعة
        if not self.journal_entry_id:
            AccountingService.create_invoice_journal_entry(self)
        
        # إنشاء سجل دفعة
        from app.models.accounting import PaymentReceived
        payment = PaymentReceived(
            customer_id=self.client_id,
            invoice_id=self.id,
            payment_number=f"PAY-{self.invoice_number}",
            payment_date=date.today(),
            amount=self.total_amount,
            payment_method='bank_transfer',
            notes=f'Payment for invoice {self.invoice_number}'
        )
        db.session.add(payment)
        payment.create_journal_entry()
    
    # استبدال الدالة القديمة
    Invoice.mark_as_paid = enhanced_mark_as_paid
```

### الخطوة 5: تطبيق الهجرة وإدراج البيانات

```bash
# تطبيق الهجرة
flask db upgrade

# تشغيل إدراج البيانات الأساسية
python migration_script.py
```

### الخطوة 6: إنشاء خدمات المحاسبة

قم بإنشاء `app/services/accounting_service.py`:

```python
# app/services/accounting_service.py
from app.models.accounting import ChartOfAccounts, JournalEntry, JournalItem
from app import db
from flask_login import current_user

class AccountingService:
    @staticmethod
    def create_invoice_journal_entry(invoice):
        """إنشاء قيد يومية للفاتورة"""
        # البحث عن الحسابات
        ar_account = ChartOfAccounts.query.filter_by(account_code='1020').first()
        
        # تحديد حساب الإيرادات حسب نوع المشروع
        if invoice.project and 'تصميم' in invoice.project.name.lower():
            revenue_account = ChartOfAccounts.query.filter_by(account_code='4010').first()
        elif invoice.project and 'برمجة' in invoice.project.name.lower():
            revenue_account = ChartOfAccounts.query.filter_by(account_code='4020').first()
        else:
            revenue_account = ChartOfAccounts.query.filter_by(account_code='4010').first()
        
        # إنشاء القيد
        entry = JournalEntry(
            entry_number=f"INV-{invoice.invoice_number}",
            entry_date=invoice.issue_date,
            description=f"فاتورة رقم {invoice.invoice_number} للعميل {invoice.client.name}",
            source_document_type='invoice',
            source_document_id=invoice.id,
            created_by_id=current_user.id if current_user.is_authenticated else 1
        )
        
        # بند المدين: حسابات العملاء
        debit_item = JournalItem(
            account_id=ar_account.id,
            debit=invoice.total_amount,
            credit=0,
            description=f"فاتورة للعميل {invoice.client.name}"
        )
        
        # بند الدائن: الإيرادات
        credit_item = JournalItem(
            account_id=revenue_account.id,
            debit=0,
            credit=invoice.total_amount,
            description=f"إيرادات من {invoice.client.name}"
        )
        
        entry.items.append(debit_item)
        entry.items.append(credit_item)
        
        db.session.add(entry)
        db.session.flush()
        
        invoice.journal_entry_id = entry.id
        entry.post()
        
        return entry
```

### الخطوة 7: تحديث المسارات الحالية

قم بتحديث `app/routes/finance.py`:

```python
# إضافة في بداية الملف
from app.services.accounting_service import AccountingService
from app.models.accounting import ChartOfAccounts, JournalEntry

# تحسين دالة mark_invoice_paid
@finance_bp.route('/mark_invoice_paid/<int:id>', methods=['POST'])
@login_required
def enhanced_mark_invoice_paid(id):
    """تحسين دالة تحديد الفاتورة كمدفوعة"""
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إدارة الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    if invoice.status == 'paid':
        flash('الفاتورة مدفوعة بالفعل', 'info')
        return redirect(url_for('finance.view_invoice', id=id))

    try:
        # استخدام الدالة المحسنة
        invoice.mark_as_paid()
        db.session.commit()
        flash('تم تحديد الفاتورة كمدفوعة وإنشاء القيد المحاسبي بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في معالجة الدفعة: {str(e)}', 'danger')

    return redirect(url_for('finance.view_invoice', id=id))
```

### الخطوة 8: إنشاء لوحة تحكم محسنة

قم بإنشاء `app/templates/finance/enhanced_dashboard.html`:

```html
{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">لوحة التحكم المالية المتكاملة</h1>
            <p class="mb-0">نظرة شاملة على الوضع المالي للشركة</p>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="row mb-4">
        <!-- الإيرادات الشهرية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الإيرادات هذا الشهر</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(monthly_revenue) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المصروفات الشهرية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                المصروفات هذا الشهر</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(monthly_expenses) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- صافي الربح -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                صافي الربح</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(net_profit) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرصيد النقدي -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الرصيد النقدي</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ "{:,.2f}".format(cash_balance) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-university fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Profit & Loss Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الأداء المالي الشهري</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyPerformanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Account Balances -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أرصدة الحسابات الرئيسية</h6>
                </div>
                <div class="card-body">
                    <canvas id="accountBalancesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('finance.create_invoice') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-file-invoice"></i> إنشاء فاتورة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-success btn-block" onclick="openPaymentModal()">
                                <i class="fas fa-money-bill-wave"></i> تسجيل دفعة
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-warning btn-block">
                                <i class="fas fa-receipt"></i> فاتورة مورد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-info btn-block">
                                <i class="fas fa-chart-bar"></i> التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل دفعة مستلمة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="form-group">
                        <label>العميل</label>
                        <select class="form-control" name="customer_id" required>
                            <option value="">اختر العميل</option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label>المبلغ</label>
                        <input type="number" class="form-control" name="amount" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>طريقة الدفع</label>
                        <select class="form-control" name="payment_method" required>
                            <option value="bank_transfer">حوالة بنكية</option>
                            <option value="cash">نقداً</option>
                            <option value="check">شيك</option>
                            <option value="credit_card">بطاقة ائتمان</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>رقم المرجع</label>
                        <input type="text" class="form-control" name="reference_number">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitPayment()">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// JavaScript للرسوم البيانية والتفاعل
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للأداء الشهري
    const ctx1 = document.getElementById('monthlyPerformanceChart').getContext('2d');
    new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات',
                data: [{{ monthly_revenue }}, 35000, 42000, 38000, 45000, 40000],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }, {
                label: 'المصروفات',
                data: [{{ monthly_expenses }}, 15000, 18000, 16000, 20000, 17000],
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الأداء المالي الشهري'
                }
            }
        }
    });

    // رسم بياني لأرصدة الحسابات
    const ctx2 = document.getElementById('accountBalancesChart').getContext('2d');
    new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['النقد', 'حسابات العملاء', 'المعدات'],
            datasets: [{
                data: [{{ cash_balance }}, {{ outstanding_receivables }}, 25000],
                backgroundColor: [
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 99, 132)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
});

function openPaymentModal() {
    $('#paymentModal').modal('show');
}

function submitPayment() {
    // منطق إرسال الدفعة
    const formData = new FormData(document.getElementById('paymentForm'));
    
    fetch('/api/v2/payments/receive', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم تسجيل الدفعة بنجاح');
            $('#paymentModal').modal('hide');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    });
}
</script>
{% endblock %}
```

### الخطوة 9: إضافة مسار للوحة التحكم المحسنة

```python
# app/routes/finance.py - إضافة مسار جديد

@finance_bp.route('/enhanced-dashboard')
@login_required
def enhanced_dashboard():
    """لوحة التحكم المالية المحسنة"""
    from app.models.accounting import ChartOfAccounts
    
    # حساب المؤشرات
    revenue_accounts = ChartOfAccounts.query.filter_by(account_type='Revenue').all()
    expense_accounts = ChartOfAccounts.query.filter_by(account_type='Expense').all()
    cash_account = ChartOfAccounts.query.filter_by(account_code='1010').first()
    
    monthly_revenue = sum(acc.balance for acc in revenue_accounts)
    monthly_expenses = sum(acc.balance for acc in expense_accounts)
    net_profit = monthly_revenue - monthly_expenses
    cash_balance = cash_account.balance if cash_account else 0
    
    # المبالغ المستحقة
    outstanding_receivables = sum(
        invoice.amount_due or 0 for invoice in Invoice.query.filter_by(status='sent').all()
    )
    
    return render_template('finance/enhanced_dashboard.html',
                         monthly_revenue=monthly_revenue,
                         monthly_expenses=monthly_expenses,
                         net_profit=net_profit,
                         cash_balance=cash_balance,
                         outstanding_receivables=outstanding_receivables)
```

### الخطوة 10: الاختبار والتشغيل

```bash
# تشغيل الخادم
python run.py

# زيارة الرابط الجديد
# http://localhost:5000/finance/enhanced-dashboard
```

## النتائج المتوقعة

بعد تطبيق هذه الخطوات، ستحصل على:

1. **نظام محاسبة مزدوج القيد كامل**
2. **قيود محاسبية تلقائية** لكل فاتورة ودفعة
3. **لوحة تحكم محسنة** مع مؤشرات الأداء
4. **تقارير مالية دقيقة** ومتكاملة
5. **تتبع أفضل للمدفوعات** والمستحقات

هذا التطبيق العملي يحول نظامك الحالي إلى نظام مالي متكامل ومؤتمت بالكامل!
