from app import create_app
from app.models.project import Project
from app.models.task import Task
from app.models.leave import LeaveRequest
from app.models.meeting import Meeting
from app.models.user import User

app = create_app()

with app.app_context():
    print('Users:', User.query.count())
    print('Projects:', Project.query.count())
    print('Tasks:', Task.query.count())
    print('Leaves:', LeaveRequest.query.count())
    print('Meetings:', Meeting.query.count())

    # Verificar si hay proyectos para el usuario 1
    user_id = 1
    user = User.query.get(user_id)
    if user:
        print(f"\nVerificando datos para el usuario: {user.username}")

        # Proyectos
        projects = Project.query.join(Project.members).filter(Project.members.any(id=user_id)).all()
        print(f"Proyectos para el usuario {user.username}: {len(projects)}")
        for p in projects:
            print(f"  - {p.name} ({p.start_date} a {p.end_date})")

        # Tareas
        tasks = Task.query.filter_by(assignee_id=user_id).all()
        print(f"Tareas para el usuario {user.username}: {len(tasks)}")
        for t in tasks:
            print(f"  - {t.title} (vence: {t.due_date})")

        # Solicitudes de permiso
        leaves = LeaveRequest.query.filter_by(user_id=user_id).all()
        print(f"Solicitudes de permiso para el usuario {user.username}: {len(leaves)}")
        for l in leaves:
            print(f"  - {l.reason} ({l.start_date} a {l.end_date}, estado: {l.status})")

        # Reuniones
        meetings = Meeting.query.join(Meeting.attendees).filter(Meeting.attendees.any(id=user_id)).all()
        print(f"Reuniones para el usuario {user.username}: {len(meetings)}")
        for m in meetings:
            print(f"  - {m.title} ({m.date}, {m.start_time} a {m.end_time})")
