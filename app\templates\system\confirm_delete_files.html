{% extends 'base.html' %}

{% block title %}تأكيد حذف الملفات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">تأكيد حذف الملفات</h6>
                    <a href="{{ url_for('system.manage_files') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>العودة إلى إدارة الملفات
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الملفات المحددة بشكل نهائي.
                    </div>
                    
                    <h5 class="mb-3">هل أنت متأكد من رغبتك في حذف الملفات التالية؟</h5>
                    
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>المسار</th>
                                    <th>الحجم</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in files %}
                                <tr>
                                    <td>{{ file.name }}</td>
                                    <td>{{ file.path }}</td>
                                    <td>{{ file.size_str }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-info">
                        <p>إجمالي عدد الملفات: <strong>{{ files|length }}</strong></p>
                        <p>إجمالي حجم الملفات: <strong>{{ total_size_str }}</strong></p>
                    </div>
                    
                    <form action="{{ url_for('system.delete_files') }}" method="POST">
                        {% for file in files %}
                        <input type="hidden" name="files" value="{{ file.path }}">
                        {% endfor %}
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('system.manage_files') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
