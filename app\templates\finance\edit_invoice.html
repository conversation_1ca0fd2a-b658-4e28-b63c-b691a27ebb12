{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>تعديل الفاتورة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.invoices') }}">الفواتير</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل الفاتورة #{{ invoice.invoice_number }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تعديل الفاتورة #{{ invoice.invoice_number }}</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('finance.edit_invoice', id=invoice.id) }}" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" value="{{ invoice.invoice_number }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="client_id" class="form-label">العميل</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="client_search" placeholder="ابحث عن عميل..." autocomplete="off">
                                    <div id="client_search_results" class="position-absolute w-100 mt-1 shadow-sm d-none" style="max-height: 200px; overflow-y: auto; z-index: 1000; background-color: white; border: 1px solid #ced4da; border-radius: 0.25rem;"></div>
                                </div>
                                <select class="form-select d-none" id="client_id" name="client_id" required>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}" {% if invoice.client_id == client.id %}selected{% endif %}>{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                                <div id="selected_client" class="mt-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2" id="selected_client_name">
                                            {% if invoice.client %}{{ invoice.client.name }}{% endif %}
                                        </span>
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="clear_client">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="project_id" class="form-label">المشروع (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="project_search" placeholder="ابحث عن مشروع..." autocomplete="off">
                                    <div id="project_search_results" class="position-absolute w-100 mt-1 shadow-sm d-none" style="max-height: 200px; overflow-y: auto; z-index: 1000; background-color: white; border: 1px solid #ced4da; border-radius: 0.25rem;"></div>
                                </div>
                                <select class="form-select d-none" id="project_id" name="project_id">
                                    <option value="">-- بدون مشروع --</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}" {% if invoice.project_id == project.id %}selected{% endif %}>{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                                <div id="selected_project" class="mt-2 {% if not invoice.project_id %}d-none{% endif %}">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2" id="selected_project_name">
                                            {% if invoice.project %}{{ invoice.project.name }}{% endif %}
                                        </span>
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="clear_project">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ invoice.issue_date.strftime('%Y-%m-%d') }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '' }}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="approval_date" class="form-label">تاريخ الموافقة</label>
                                <input type="date" class="form-control" id="approval_date" name="approval_date" value="{{ invoice.approval_date.strftime('%Y-%m-%d') if invoice.approval_date else '' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" {% if invoice.status == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="paid" {% if invoice.status == 'paid' %}selected{% endif %}>مدفوع</option>
                                <option value="overdue" {% if invoice.status == 'overdue' %}selected{% endif %}>متأخر</option>
                                <option value="cancelled" {% if invoice.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ invoice.notes }}</textarea>
                        </div>

                        <h5 class="mt-4 mb-3">عناصر الفاتورة</h5>
                        <div id="invoice-items">
                            {% for item in invoice.items %}
                            <div class="invoice-item card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label class="form-label">الوصف</label>
                                            <input type="text" class="form-control" name="item_description[]" value="{{ item.description }}" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">الكمية</label>
                                            <input type="number" step="0.01" class="form-control item-quantity" name="item_quantity[]" value="{{ item.quantity }}" required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">سعر الوحدة</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" step="0.01" class="form-control item-price" name="item_unit_price[]" value="{{ item.unit_price }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">الموظف المشرف</label>
                                            <select class="form-select" name="item_supervisor[]">
                                                <option value="">-- بدون مشرف --</option>
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}" {% if item.supervisor_id == employee.id %}selected{% endif %}>{{ employee.username }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label class="form-label">المجموع</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="text" class="form-control item-total" value="{{ item.total_price }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">نوع ربح الشركة</label>
                                            <select class="form-select company-profit-type" name="item_company_profit_type[]">
                                                <option value="percentage" {% if item.company_profit_type == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                                                <option value="fixed" {% if item.company_profit_type == 'fixed' %}selected{% endif %}>مبلغ ثابت</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">قيمة ربح الشركة</label>
                                            <div class="input-group">
                                                <input type="number" step="0.01" class="form-control" name="item_company_profit_value[]" value="{{ item.company_profit_value }}">
                                                <span class="input-group-text company-profit-symbol">{% if item.company_profit_type == 'percentage' %}%{% else %}${% endif %}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select class="form-select" name="item_status[]">
                                                <option value="غير مستلم" {% if item.status == 'غير مستلم' %}selected{% endif %}>غير مستلم</option>
                                                <option value="مستلم" {% if item.status == 'مستلم' %}selected{% endif %}>مستلم</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">تاريخ الاستلام (اختياري)</label>
                                            <input type="date" class="form-control" name="item_receipt_date[]" value="{{ item.receipt_date.strftime('%Y-%m-%d') if item.receipt_date else '' }}">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-item">حذف العنصر</button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <button type="button" class="btn btn-secondary mb-4" id="add-item-btn">
                            <i class="fas fa-plus me-1"></i>إضافة عنصر
                        </button>

                        <h5 class="mt-4 mb-3">الرسوم والضرائب والخصومات</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">عمولة التحويل</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="transfer_fee_type" id="transfer_fee_type">
                                        <option value="percentage" {% if invoice.transfer_fee_type == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                                        <option value="fixed" {% if invoice.transfer_fee_type == 'fixed' %}selected{% endif %}>مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="transfer_fee_value" id="transfer_fee_value" value="{{ invoice.transfer_fee_value }}">
                                    <span class="input-group-text" id="transfer_fee_symbol">{% if invoice.transfer_fee_type == 'percentage' %}%{% else %}${% endif %}</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الضريبة</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="tax_type" id="tax_type">
                                        <option value="percentage" {% if invoice.tax_type == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                                        <option value="fixed" {% if invoice.tax_type == 'fixed' %}selected{% endif %}>مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="tax_value" id="tax_value" value="{{ invoice.tax_value }}">
                                    <span class="input-group-text" id="tax_symbol">{% if invoice.tax_type == 'percentage' %}%{% else %}${% endif %}</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الخصم</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" name="discount_type" id="discount_type">
                                        <option value="percentage" {% if invoice.discount_type == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                                        <option value="fixed" {% if invoice.discount_type == 'fixed' %}selected{% endif %}>مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="discount_value" id="discount_value" value="{{ invoice.discount_value }}">
                                    <span class="input-group-text" id="discount_symbol">{% if invoice.discount_type == 'percentage' %}%{% else %}${% endif %}</span>
                                </div>
                            </div>
                        </div>

                        <h5 class="mt-4 mb-3">المرفقات</h5>
                        <div class="mb-3">
                            <label for="attachments" class="form-label">إضافة مرفقات</label>
                            <input class="form-control" type="file" id="attachments" name="attachments" multiple>
                        </div>

                        {% if invoice.attachments.count() > 0 %}
                        <div class="mb-4">
                            <h6>المرفقات الحالية:</h6>
                            <ul class="list-group">
                                {% for attachment in invoice.attachments %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                    <form action="{{ url_for('finance.delete_invoice_attachment', attachment_id=attachment.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا المرفق؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <h5 class="mt-4 mb-3">روابط الإثبات (اختياري)</h5>
                        <div class="mb-3">
                            <div id="verification-links-container">
                                {% if invoice.verification_links.count() > 0 %}
                                    {% for link in invoice.verification_links %}
                                    <div class="verification-link-item mb-2 row">
                                        <div class="col-md-5">
                                            <input type="url" class="form-control" name="verification_links[]" value="{{ link.url }}" placeholder="أدخل الرابط هنا">
                                            <input type="hidden" name="verification_link_ids[]" value="{{ link.id }}">
                                        </div>
                                        <div class="col-md-5">
                                            <input type="text" class="form-control" name="verification_descriptions[]" value="{{ link.description }}" placeholder="وصف الرابط (اختياري)">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                <div class="verification-link-item mb-2 row">
                                    <div class="col-md-5">
                                        <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" id="add-link-btn">
                                <i class="fas fa-plus me-1"></i>إضافة رابط آخر
                            </button>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // Validate client selection
            const clientSelect = document.getElementById('client_id');
            if (!clientSelect.value) {
                event.preventDefault();
                alert('يرجى اختيار عميل');
                document.getElementById('client_search').focus();
                return false;
            }

            // Validate other required fields
            const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
        });
        // Add new item
        const addItemBtn = document.getElementById('add-item-btn');
        const invoiceItems = document.getElementById('invoice-items');

        addItemBtn.addEventListener('click', function() {
            const itemTemplate = `
                <div class="invoice-item card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <input type="text" class="form-control" name="item_description[]" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" step="0.01" class="form-control item-quantity" name="item_quantity[]" value="1" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">سعر الوحدة</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" class="form-control item-price" name="item_unit_price[]" value="0" required>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">الموظف المشرف</label>
                                <select class="form-select" name="item_supervisor[]">
                                    <option value="">-- بدون مشرف --</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.username }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المجموع</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control item-total" value="0" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع ربح الشركة</label>
                                <select class="form-select company-profit-type" name="item_company_profit_type[]">
                                    <option value="percentage" selected>نسبة مئوية</option>
                                    <option value="fixed">مبلغ ثابت</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">قيمة ربح الشركة</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control" name="item_company_profit_value[]" value="0">
                                    <span class="input-group-text company-profit-symbol">%</span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="item_status[]">
                                    <option value="غير مستلم" selected>غير مستلم</option>
                                    <option value="مستلم">مستلم</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ الاستلام (اختياري)</label>
                                <input type="date" class="form-control" name="item_receipt_date[]">
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-danger remove-item">حذف العنصر</button>
                    </div>
                </div>
            `;

            // Add new item to the list
            invoiceItems.insertAdjacentHTML('beforeend', itemTemplate);

            // Add event listeners to the new item
            setupItemEventListeners(invoiceItems.lastElementChild);
        });

        // Setup event listeners for existing items
        document.querySelectorAll('.invoice-item').forEach(item => {
            setupItemEventListeners(item);
        });

        function setupItemEventListeners(item) {
            // Remove item
            item.querySelector('.remove-item').addEventListener('click', function() {
                item.remove();
                updateTotals();
            });

            // Update item total when quantity or price changes
            const quantityInput = item.querySelector('.item-quantity');
            const priceInput = item.querySelector('.item-price');
            const totalInput = item.querySelector('.item-total');

            function updateItemTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const total = quantity * price;
                totalInput.value = total.toFixed(2);
                updateTotals();
            }

            quantityInput.addEventListener('input', updateItemTotal);
            priceInput.addEventListener('input', updateItemTotal);

            // Update company profit symbol
            const companyProfitType = item.querySelector('.company-profit-type');
            const companyProfitSymbol = item.querySelector('.company-profit-symbol');

            if (companyProfitType && companyProfitSymbol) {
                companyProfitType.addEventListener('change', function() {
                    companyProfitSymbol.textContent = this.value === 'percentage' ? '%' : '$';
                });
            }
        }

        // Update fee, tax, and discount symbols
        const transferFeeType = document.getElementById('transfer_fee_type');
        const transferFeeSymbol = document.getElementById('transfer_fee_symbol');
        const taxType = document.getElementById('tax_type');
        const taxSymbol = document.getElementById('tax_symbol');
        const discountType = document.getElementById('discount_type');
        const discountSymbol = document.getElementById('discount_symbol');

        transferFeeType.addEventListener('change', function() {
            transferFeeSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        taxType.addEventListener('change', function() {
            taxSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        discountType.addEventListener('change', function() {
            discountSymbol.textContent = this.value === 'percentage' ? '%' : '$';
        });

        // Calculate totals
        function updateTotals() {
            // This function would calculate subtotal, fees, taxes, discounts, and total
            // For now, we'll just update the item totals
            document.querySelectorAll('.invoice-item').forEach(item => {
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(item.querySelector('.item-price').value) || 0;
                const total = quantity * price;
                item.querySelector('.item-total').value = total.toFixed(2);
            });
        }

        // Initial update
        updateTotals();

        // Handle verification links
        const addLinkBtn = document.getElementById('add-link-btn');
        const linksContainer = document.getElementById('verification-links-container');

        // Add new link field
        addLinkBtn.addEventListener('click', function() {
            const linkItem = document.createElement('div');
            linkItem.className = 'verification-link-item mb-2 row';
            linkItem.innerHTML = `
                <div class="col-md-5">
                    <input type="url" class="form-control" name="verification_links[]" placeholder="أدخل الرابط هنا">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" name="verification_descriptions[]" placeholder="وصف الرابط (اختياري)">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger remove-link"><i class="fas fa-times"></i></button>
                </div>
            `;
            linksContainer.appendChild(linkItem);

            // Add event listener to the new remove button
            const removeBtn = linkItem.querySelector('.remove-link');
            removeBtn.addEventListener('click', function() {
                linksContainer.removeChild(linkItem);
            });
        });

        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-link').forEach(button => {
            button.addEventListener('click', function() {
                const linkItem = this.closest('.verification-link-item');
                linksContainer.removeChild(linkItem);
            });
        });

        // Client search functionality
        const clientSearch = document.getElementById('client_search');
        const clientSearchResults = document.getElementById('client_search_results');
        const clientSelect = document.getElementById('client_id');
        const selectedClient = document.getElementById('selected_client');
        const selectedClientName = document.getElementById('selected_client_name');
        const clearClient = document.getElementById('clear_client');

        // Ensure the client search field is initialized with the current client
        if (clientSelect.value) {
            const selectedOption = clientSelect.options[clientSelect.selectedIndex];
            selectedClientName.textContent = selectedOption.textContent;
            selectedClient.classList.remove('d-none');
        }

        // Create an array of client objects from the select options
        const clients = Array.from(clientSelect.options).map(option => {
            return {
                id: option.value,
                name: option.textContent
            };
        });

        // Function to filter clients based on search term
        function filterClients(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            return clients.filter(client =>
                client.name.toLowerCase().includes(searchTerm)
            );
        }

        // Function to display client search results
        function displayClientSearchResults(results) {
            clientSearchResults.innerHTML = '';

            if (results.length === 0) {
                clientSearchResults.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
                return;
            }

            results.forEach(client => {
                const resultItem = document.createElement('div');
                resultItem.className = 'p-2 border-bottom client-result';
                resultItem.textContent = client.name;
                resultItem.dataset.id = client.id;
                resultItem.dataset.name = client.name;
                resultItem.style.cursor = 'pointer';

                resultItem.addEventListener('click', function() {
                    // Set the selected client in the hidden select
                    clientSelect.value = this.dataset.id;

                    // Display the selected client
                    selectedClientName.textContent = this.dataset.name;
                    selectedClient.classList.remove('d-none');

                    // Clear the search input and hide results
                    clientSearch.value = '';
                    clientSearchResults.classList.add('d-none');
                });

                clientSearchResults.appendChild(resultItem);
            });
        }

        // Event listener for client search input
        clientSearch.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            if (searchTerm.length > 0) {
                const results = filterClients(searchTerm);
                displayClientSearchResults(results);
                clientSearchResults.classList.remove('d-none');
            } else {
                clientSearchResults.classList.add('d-none');
            }
        });

        // Event listener for clear client button
        clearClient.addEventListener('click', function() {
            clientSelect.value = '';
            selectedClient.classList.add('d-none');
            clientSearch.value = '';
        });

        // Close client search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!clientSearch.contains(event.target) && !clientSearchResults.contains(event.target)) {
                clientSearchResults.classList.add('d-none');
            }
        });

        // Focus event to show all clients when focusing on empty search
        clientSearch.addEventListener('focus', function() {
            if (this.value.trim() === '') {
                displayClientSearchResults(clients);
                clientSearchResults.classList.remove('d-none');
            }
        });

        // Project search functionality
        const projectSearch = document.getElementById('project_search');
        const projectSearchResults = document.getElementById('project_search_results');
        const projectSelect = document.getElementById('project_id');
        const selectedProject = document.getElementById('selected_project');
        const selectedProjectName = document.getElementById('selected_project_name');
        const clearProject = document.getElementById('clear_project');

        // Ensure the project search field is initialized with the current project
        if (projectSelect.value) {
            const selectedOption = projectSelect.options[projectSelect.selectedIndex];
            selectedProjectName.textContent = selectedOption.textContent;
            selectedProject.classList.remove('d-none');
        }

        // Create an array of project objects from the select options
        const projects = Array.from(projectSelect.options).slice(1).map(option => {
            return {
                id: option.value,
                name: option.textContent
            };
        });

        // Function to filter projects based on search term
        function filterProjects(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            return projects.filter(project =>
                project.name.toLowerCase().includes(searchTerm)
            );
        }

        // Function to display search results
        function displaySearchResults(results) {
            projectSearchResults.innerHTML = '';

            if (results.length === 0) {
                projectSearchResults.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
                return;
            }

            results.forEach(project => {
                const resultItem = document.createElement('div');
                resultItem.className = 'p-2 border-bottom project-result';
                resultItem.textContent = project.name;
                resultItem.dataset.id = project.id;
                resultItem.dataset.name = project.name;
                resultItem.style.cursor = 'pointer';

                resultItem.addEventListener('click', function() {
                    // Set the selected project in the hidden select
                    projectSelect.value = this.dataset.id;

                    // Display the selected project
                    selectedProjectName.textContent = this.dataset.name;
                    selectedProject.classList.remove('d-none');

                    // Clear the search input and hide results
                    projectSearch.value = '';
                    projectSearchResults.classList.add('d-none');
                });

                projectSearchResults.appendChild(resultItem);
            });
        }

        // Event listener for project search input
        projectSearch.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            if (searchTerm.length > 0) {
                const results = filterProjects(searchTerm);
                displaySearchResults(results);
                projectSearchResults.classList.remove('d-none');
            } else {
                projectSearchResults.classList.add('d-none');
            }
        });

        // Event listener for clear project button
        clearProject.addEventListener('click', function() {
            projectSelect.value = '';
            selectedProject.classList.add('d-none');
            projectSearch.value = '';
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!projectSearch.contains(event.target) && !projectSearchResults.contains(event.target)) {
                projectSearchResults.classList.add('d-none');
            }
        });

        // Focus event to show all projects when focusing on empty search
        projectSearch.addEventListener('focus', function() {
            if (this.value.trim() === '') {
                displaySearchResults(projects);
                projectSearchResults.classList.remove('d-none');
            }
        });
    });
</script>
{% endblock %}
