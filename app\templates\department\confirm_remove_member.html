{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ title }}</h1>
    <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة إلى قائمة الأعضاء
    </a>
</div>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد إزالة العضو</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> سيتم إزالة العضو من القسم. يمكن إعادة تعيينه لاحقًا إذا لزم الأمر.
        </div>
        
        <h5 class="mb-3">هل أنت متأكد من رغبتك في إزالة <strong>{{ user.get_full_name() }}</strong> من قسم <strong>{{ department.name }}</strong>؟</h5>
        
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">معلومات العضو</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الاسم:</strong> {{ user.get_full_name() }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ user.email }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                        <p><strong>الصلاحيات:</strong> 
                            {% for role in user.roles %}
                            <span class="badge bg-secondary me-1">{{ role.name }}</span>
                            {% endfor %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>إلغاء
            </a>
            <form action="{{ url_for('department.remove_member', dept_id=department.id, user_id=user.id) }}" method="POST">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-user-minus me-1"></i>تأكيد الإزالة
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
