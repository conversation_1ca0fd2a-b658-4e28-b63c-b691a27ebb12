from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app.models.calendar_event import CalendarEvent

calendar_bp = Blueprint('calendar', __name__)

@calendar_bp.route('/')
@login_required
def index():
    """Página principal del calendario"""
    return render_template('calendar/index.html', title='التقويم')

@calendar_bp.route('/events')
@login_required
def get_events():
    """API para obtener eventos del calendario"""
    # Obtener parámetros de fecha de la solicitud
    start = request.args.get('start')
    end = request.args.get('end')

    # Convertir fechas si están presentes
    # FullCalendar envía fechas en formato ISO 8601 (YYYY-MM-DDThh:mm:ss+zz:zz)
    # Extraemos solo la parte de la fecha (YYYY-MM-DD)
    if start:
        start_date_str = start.split('T')[0] if 'T' in start else start
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    else:
        start_date = None

    if end:
        end_date_str = end.split('T')[0] if 'T' in end else end
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
    else:
        end_date = None

    # Obtener eventos del usuario actual
    events = CalendarEvent.get_user_events(current_user.id, start_date, end_date)

    # Convertir eventos a formato JSON
    events_json = [event.to_dict() for event in events]

    return jsonify(events_json)
