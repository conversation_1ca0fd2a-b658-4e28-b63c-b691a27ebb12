import os
import sqlite3

def update_database_schema():
    # Get the path to the database file
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')

    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return False

    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Add new columns to the users table
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'profile_image_data' not in columns:
            print("Adding profile_image_data column to users table...")
            cursor.execute("ALTER TABLE users ADD COLUMN profile_image_data BLOB")

        if 'profile_image_mime' not in columns:
            print("Adding profile_image_mime column to users table...")
            cursor.execute("ALTER TABLE users ADD COLUMN profile_image_mime VARCHAR(64)")

        # Add new columns to the clients table
        cursor.execute("PRAGMA table_info(clients)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'profile_image' not in columns:
            print("Adding profile_image column to clients table...")
            cursor.execute("ALTER TABLE clients ADD COLUMN profile_image TEXT DEFAULT 'default.jpg'")

        if 'profile_image_data' not in columns:
            print("Adding profile_image_data column to clients table...")
            cursor.execute("ALTER TABLE clients ADD COLUMN profile_image_data BLOB")

        if 'profile_image_mime' not in columns:
            print("Adding profile_image_mime column to clients table...")
            cursor.execute("ALTER TABLE clients ADD COLUMN profile_image_mime VARCHAR(64)")

        conn.commit()
        print("Database schema updated successfully!")
        return True

    except sqlite3.Error as e:
        print(f"Error updating database schema: {e}")
        conn.rollback()
        return False

    finally:
        conn.close()

if __name__ == "__main__":
    if update_database_schema():
        print("Database update completed!")
    else:
        print("Database update failed!")
