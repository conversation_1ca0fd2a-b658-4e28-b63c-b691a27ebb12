from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.department import Department
from app.models.user import User

department_bp = Blueprint('department', __name__, url_prefix='/departments')

@department_bp.route('/')
@login_required
def index():
    # Check if user has permission to view departments
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head')):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    departments = Department.query.all()
    return render_template('department/index.html', title='Departments', departments=departments)

@department_bp.route('/view/<int:id>')
@login_required
def view(id):
    department = Department.query.get_or_404(id)

    # Check if user has permission to view department details
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.has_role('department_head') or current_user.department_id == id):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    return render_template('department/view.html', title=f'Department: {department.name}', department=department)

@department_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has permission to create departments
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('You do not have permission to create departments', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all users who can be department heads (all users)
    potential_heads = User.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        head_id = request.form.get('head_id')

        # Check if department name already exists
        if Department.query.filter_by(name=name).first():
            flash('Department name already exists', 'danger')
            return redirect(url_for('department.create'))

        # Create new department
        department = Department(
            name=name,
            description=description
        )

        # Set department head if provided
        if head_id:
            department.head_id = head_id

        db.session.add(department)
        db.session.commit()

        flash('Department created successfully', 'success')
        return redirect(url_for('department.index'))

    return render_template('department/create.html', title='Create Department', potential_heads=potential_heads)

@department_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Check if user has permission to edit departments
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('You do not have permission to edit departments', 'danger')
        return redirect(url_for('dashboard.index'))

    department = Department.query.get_or_404(id)

    # Get all users who can be department heads (all users)
    potential_heads = User.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        head_id = request.form.get('head_id')

        # Check if department name already exists (if changed)
        if name != department.name and Department.query.filter_by(name=name).first():
            flash('Department name already exists', 'danger')
            return redirect(url_for('department.edit', id=id))

        # Update department
        department.name = name
        department.description = description

        # Set department head if provided
        if head_id:
            department.head_id = head_id
        else:
            department.head_id = None

        db.session.commit()

        flash('Department updated successfully', 'success')
        return redirect(url_for('department.view', id=id))

    return render_template('department/edit.html', title=f'Edit Department: {department.name}',
                          department=department, potential_heads=potential_heads)

@department_bp.route('/confirm-delete/<int:id>')
@login_required
def confirm_delete(id):
    # Only admin can delete departments
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف الأقسام', 'danger')
        return redirect(url_for('dashboard.index'))

    department = Department.query.get_or_404(id)

    # Check if department has members
    has_members = bool(department.members)

    # Check if department has projects
    has_projects = len(department.get_projects()) > 0

    return render_template('department/confirm_delete.html',
                          title='تأكيد حذف القسم',
                          department=department,
                          has_members=has_members,
                          has_projects=has_projects)

@department_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    # Only admin can delete departments
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف الأقسام', 'danger')
        return redirect(url_for('dashboard.index'))

    department = Department.query.get_or_404(id)

    # Check if department has members
    if department.members:
        flash('لا يمكن حذف قسم يحتوي على أعضاء. يرجى إعادة تعيين الأعضاء أولاً.', 'danger')
        return redirect(url_for('department.view', id=id))

    # Check if department has projects
    if len(department.get_projects()) > 0:
        flash('لا يمكن حذف قسم يحتوي على مشاريع. يرجى إعادة تعيين المشاريع أولاً.', 'danger')
        return redirect(url_for('department.view', id=id))

    db.session.delete(department)
    db.session.commit()

    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('department.index'))

@department_bp.route('/<int:id>/members')
@login_required
def members(id):
    department = Department.query.get_or_404(id)

    # Check if user has permission to view department members
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == department.head_id or current_user.department_id == id):
        flash('You do not have permission to view this page', 'danger')
        return redirect(url_for('dashboard.index'))

    members = User.query.filter_by(department_id=id).all()

    return render_template('department/members.html', title=f'{department.name} Members',
                          department=department, members=members)

@department_bp.route('/<int:id>/add_member', methods=['GET', 'POST'])
@login_required
def add_member(id):
    # Check if user has permission to add department members
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == Department.query.get_or_404(id).head_id):
        flash('You do not have permission to add department members', 'danger')
        return redirect(url_for('dashboard.index'))

    department = Department.query.get_or_404(id)

    # Get all users who are not already in this department
    available_users = User.query.filter(
        (User.department_id != id) | (User.department_id == None)
    ).all()

    if request.method == 'POST':
        user_ids = request.form.getlist('user_ids')

        for user_id in user_ids:
            user = User.query.get(user_id)
            if user:
                user.department_id = id

        db.session.commit()

        flash('Members added successfully', 'success')
        return redirect(url_for('department.members', id=id))

    return render_template('department/add_member.html', title=f'Add Members to {department.name}',
                          department=department, available_users=available_users)

@department_bp.route('/<int:dept_id>/confirm-remove-member/<int:user_id>')
@login_required
def confirm_remove_member(dept_id, user_id):
    # Check if user has permission to remove department members
    department = Department.query.get_or_404(dept_id)
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == department.head_id):
        flash('ليس لديك صلاحية لإزالة أعضاء القسم', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(user_id)

    # Cannot remove department head from their own department
    if user.id == department.head_id:
        flash('لا يمكن إزالة رئيس القسم من قسمه', 'danger')
        return redirect(url_for('department.members', id=dept_id))

    return render_template('department/confirm_remove_member.html',
                          title='تأكيد إزالة العضو',
                          department=department,
                          user=user)

@department_bp.route('/<int:dept_id>/remove_member/<int:user_id>', methods=['POST'])
@login_required
def remove_member(dept_id, user_id):
    # Check if user has permission to remove department members
    department = Department.query.get_or_404(dept_id)
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == department.head_id):
        flash('ليس لديك صلاحية لإزالة أعضاء القسم', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(user_id)

    # Cannot remove department head from their own department
    if user.id == department.head_id:
        flash('لا يمكن إزالة رئيس القسم من قسمه', 'danger')
        return redirect(url_for('department.members', id=dept_id))

    user.department_id = None
    db.session.commit()

    flash('تم إزالة العضو بنجاح', 'success')
    return redirect(url_for('department.members', id=dept_id))
