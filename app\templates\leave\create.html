{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">طلب إجازة جديد</h1>
        <a href="{{ url_for('leave.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تفاصيل الإجازة</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('leave.create') }}" enctype="multipart/form-data">
                <div class="row">
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <div class="col-md-12 mb-3">
                        <label for="user_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="{{ current_user.id }}" selected>{{ current_user.get_full_name() }} (أنا)</option>
                            {% for user in users %}
                                {% if user.id != current_user.id %}
                                <option value="{{ user.id }}">{{ user.get_full_name() }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر الموظف الذي تريد إنشاء طلب إجازة له</div>
                    </div>
                    {% endif %}

                    <div class="col-md-6 mb-3">
                        <label for="start_date" class="form-label">تاريخ بداية الإجازة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" required>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="end_date" class="form-label">تاريخ نهاية الإجازة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="end_date" name="end_date" required>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="reason" class="form-label">سبب الإجازة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label for="attachments" class="form-label">المرفقات</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">يمكنك إرفاق ملفات متعددة (مثل التقارير الطبية أو أي مستندات داعمة)</div>
                    </div>

                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>إرسال طلب الإجازة
                        </button>
                        <a href="{{ url_for('leave.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Set minimum date for start_date to today
        const today = new Date().toISOString().split('T')[0];
        $('#start_date').attr('min', today);
        
        // Update end_date min value when start_date changes
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
            
            // If end_date is before start_date, set it to start_date
            if ($('#end_date').val() < $(this).val()) {
                $('#end_date').val($(this).val());
            }
        });
        
        // Calculate duration when dates change
        function updateDuration() {
            const startDate = new Date($('#start_date').val());
            const endDate = new Date($('#end_date').val());
            
            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // Include both start and end dates
                
                $('#duration').text(diffDays + ' يوم');
            } else {
                $('#duration').text('');
            }
        }
        
        $('#start_date, #end_date').change(updateDuration);
    });
</script>
{% endblock %}
