{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف الموظف</h1>
    <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة لصفحة الموظف
    </a>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات الموظف بشكل نهائي.
                </div>
                
                {% if employee.username == 'GolDeN' %}
                <div class="alert alert-danger">
                    <i class="fas fa-ban me-2"></i>
                    <strong>لا يمكن حذف المستخدم GolDeN!</strong> هذا المستخدم محمي من الحذف.
                </div>
                {% endif %}
                
                {% if employee.id == current_user.id %}
                <div class="alert alert-danger">
                    <i class="fas fa-ban me-2"></i>
                    <strong>لا يمكن حذف حسابك الشخصي!</strong> لا يمكنك حذف حسابك الخاص.
                </div>
                {% endif %}
                
                <h4 class="mb-3">هل أنت متأكد من رغبتك في حذف الموظف التالي؟</h4>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ employee.get_full_name() }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>اسم المستخدم:</strong> {{ employee.username }}</p>
                                <p><strong>البريد الإلكتروني:</strong> {{ employee.email }}</p>
                                <p><strong>القسم:</strong> {{ employee.department.name if employee.department else 'غير محدد' }}</p>
                                <p><strong>الصلاحيات:</strong> 
                                    {% for role in employee.roles %}
                                    <span class="badge bg-primary me-1">{{ role.name }}</span>
                                    {% endfor %}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>تاريخ الانضمام:</strong> {{ employee.date_joined.strftime('%Y-%m-%d') }}</p>
                                <p><strong>آخر تسجيل دخول:</strong> {{ employee.last_login.strftime('%Y-%m-%d %H:%M') if employee.last_login else 'لم يسجل الدخول بعد' }}</p>
                                <p><strong>الحالة:</strong> 
                                    {% if employee.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <p><strong>عدد المشاريع:</strong> {{ projects_count }}</p>
                            <p><strong>عدد المهام:</strong> {{ tasks_count }}</p>
                        </div>
                    </div>
                </div>
                
                <form action="{{ url_for('employee.delete', id=employee.id) }}" method="POST">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                        
                        {% if employee.username != 'GolDeN' and employee.id != current_user.id %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>حذف الموظف
                        </button>
                        {% else %}
                        <button type="button" class="btn btn-danger" disabled>
                            <i class="fas fa-trash me-1"></i>حذف الموظف
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
