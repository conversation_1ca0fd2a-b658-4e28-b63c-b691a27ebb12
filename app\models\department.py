from datetime import datetime
from app import db

class Department(db.Model):
    __tablename__ = 'departments'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.<PERSON>umn(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Department head (manager)
    head_id = db.<PERSON>umn(db.Integer, db.ForeignKey('users.id'))
    head = db.relationship('User', foreign_keys=[head_id], backref='managed_department')

    # Projects associated with this department (legacy)
    # This relationship is now handled by the backref in Project model

    def __repr__(self):
        return f'<Department {self.name}>'

    def get_member_count(self):
        return len(self.members)

    def get_active_projects_count(self):
        from app.models.project import Project
        return Project.query.filter_by(department_id=self.id, status='in_progress').count()

    def get_projects(self):
        """Get all projects associated with this department (both legacy and new)"""
        from app.models.project import Project
        # Combine legacy projects and associated projects
        legacy_projects = self.legacy_projects.all() if hasattr(self, 'legacy_projects') else []
        associated_projects = self.associated_projects if hasattr(self, 'associated_projects') else []

        # Remove duplicates
        all_projects = list(set(legacy_projects + associated_projects))
        return all_projects
