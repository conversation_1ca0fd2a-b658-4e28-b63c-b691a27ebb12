# نماذج المحاسبة المتكاملة للنظام المالي
from datetime import datetime, date
from decimal import Decimal
from app import db
from sqlalchemy import event
from sqlalchemy.orm import validates

class ChartOfAccounts(db.Model):
    """شجرة الحسابات - نموذج الحسابات المحاسبية"""
    __tablename__ = 'chart_of_accounts'

    id = db.Column(db.Integer, primary_key=True)
    account_code = db.Column(db.String(20), unique=True, nullable=False)
    account_name = db.Column(db.String(255), nullable=False)
    account_type = db.Column(db.Enum('Asset', 'Liability', 'Equity', 'Revenue', 'Expense'), nullable=False)
    parent_account_id = db.Column(db.Integer, db.ForeignKey('chart_of_accounts.id'))
    balance = db.Column(db.Numeric(15, 2), default=0)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    parent = db.relationship('ChartOfAccounts', remote_side=[id], backref='children')
    journal_items = db.relationship('JournalItem', backref='account', lazy='dynamic')

    def __repr__(self):
        return f'<Account {self.account_code}: {self.account_name}>'

    def update_balance(self):
        """تحديث رصيد الحساب بناءً على بنود اليومية"""
        total_debit = sum(item.debit for item in self.journal_items if item.journal_entry.status == 'posted')
        total_credit = sum(item.credit for item in self.journal_items if item.journal_entry.status == 'posted')

        if self.account_type in ['Asset', 'Expense']:
            self.balance = total_debit - total_credit
        else:  # Liability, Equity, Revenue
            self.balance = total_credit - total_debit

        db.session.commit()

    def get_balance_for_period(self, start_date, end_date):
        """حساب رصيد الحساب لفترة محددة"""
        items = self.journal_items.join(JournalEntry).filter(
            JournalEntry.entry_date.between(start_date, end_date),
            JournalEntry.status == 'posted'
        ).all()

        total_debit = sum(item.debit for item in items)
        total_credit = sum(item.credit for item in items)

        if self.account_type in ['Asset', 'Expense']:
            return total_debit - total_credit
        else:
            return total_credit - total_debit

class JournalEntry(db.Model):
    """قيود اليومية - القيود المحاسبية"""
    __tablename__ = 'journal_entries'

    id = db.Column(db.Integer, primary_key=True)
    entry_number = db.Column(db.String(50), unique=True, nullable=False)
    entry_date = db.Column(db.Date, nullable=False, default=date.today)
    description = db.Column(db.Text, nullable=False)
    source_document_type = db.Column(db.String(50))  # 'invoice', 'bill', 'payment'
    source_document_id = db.Column(db.Integer)
    total_debit = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    total_credit = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    status = db.Column(db.Enum('draft', 'posted', 'cancelled'), default='draft')
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    created_by = db.relationship('User', backref='journal_entries')
    items = db.relationship('JournalItem', backref='journal_entry', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<JournalEntry {self.entry_number}>'

    @validates('status')
    def validate_status(self, key, status):
        if status == 'posted':
            self.validate_balance()
        return status

    def validate_balance(self):
        """التحقق من توازن القيد (المدين = الدائن)"""
        if abs(self.total_debit - self.total_credit) > 0.01:
            raise ValueError("القيد غير متوازن: المدين يجب أن يساوي الدائن")

    def calculate_totals(self):
        """حساب إجماليات المدين والدائن"""
        self.total_debit = sum(item.debit for item in self.items)
        self.total_credit = sum(item.credit for item in self.items)

    def post(self):
        """ترحيل القيد وتحديث أرصدة الحسابات"""
        self.calculate_totals()
        self.validate_balance()
        self.status = 'posted'

        # تحديث أرصدة الحسابات
        for item in self.items:
            item.account.update_balance()

        db.session.commit()

    def cancel(self):
        """إلغاء القيد"""
        if self.status == 'posted':
            # إنشاء قيد عكسي
            reverse_entry = JournalEntry(
                entry_number=f"REV-{self.entry_number}",
                entry_date=date.today(),
                description=f"عكس القيد {self.entry_number}",
                source_document_type=self.source_document_type,
                source_document_id=self.source_document_id,
                created_by_id=self.created_by_id
            )

            # إنشاء بنود عكسية
            for item in self.items:
                reverse_item = JournalItem(
                    account_id=item.account_id,
                    debit=item.credit,  # عكس المبالغ
                    credit=item.debit,
                    description=f"عكس: {item.description}"
                )
                reverse_entry.items.append(reverse_item)

            db.session.add(reverse_entry)
            reverse_entry.post()

        self.status = 'cancelled'
        db.session.commit()

class JournalItem(db.Model):
    """بنود قيد اليومية"""
    __tablename__ = 'journal_items'

    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('chart_of_accounts.id'), nullable=False)
    debit = db.Column(db.Numeric(15, 2), default=0)
    credit = db.Column(db.Numeric(15, 2), default=0)
    description = db.Column(db.Text)

    def __repr__(self):
        return f'<JournalItem Account:{self.account_id} Dr:{self.debit} Cr:{self.credit}>'

    @validates('debit', 'credit')
    def validate_amounts(self, key, value):
        """التحقق من أن المبالغ موجبة"""
        if value < 0:
            raise ValueError("المبالغ يجب أن تكون موجبة")
        return value

class PaymentReceived(db.Model):
    """الدفعات المستلمة من العملاء"""
    __tablename__ = 'payments_received'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    payment_number = db.Column(db.String(50), unique=True, nullable=False)
    payment_date = db.Column(db.Date, nullable=False, default=date.today)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.Enum('cash', 'bank_transfer', 'check', 'credit_card'), nullable=False)
    reference_number = db.Column(db.String(100))
    notes = db.Column(db.Text)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    customer = db.relationship('Client', backref='payments_received')
    invoice = db.relationship('Invoice', backref='payments_received')
    journal_entry = db.relationship('JournalEntry', backref='payment_received')
    created_by = db.relationship('User', backref='payments_received')

    def __repr__(self):
        return f'<PaymentReceived {self.payment_number}: ${self.amount}>'

    def create_journal_entry(self):
        """إنشاء قيد يومية للدفعة المستلمة"""
        # البحث عن الحسابات المطلوبة
        cash_account = ChartOfAccounts.query.filter_by(account_code='1010').first()  # النقد في البنك
        ar_account = ChartOfAccounts.query.filter_by(account_code='1020').first()    # حسابات العملاء

        if not cash_account or not ar_account:
            raise ValueError("الحسابات المطلوبة غير موجودة")

        # إنشاء القيد
        entry = JournalEntry(
            entry_number=f"PAY-{self.payment_number}",
            entry_date=self.payment_date,
            description=f"دفعة مستلمة من العميل {self.customer.name}",
            source_document_type='payment_received',
            source_document_id=self.id,
            created_by_id=self.created_by_id
        )

        # بند المدين: النقد في البنك
        debit_item = JournalItem(
            account_id=cash_account.id,
            debit=self.amount,
            credit=0,
            description=f"دفعة مستلمة - {self.payment_method}"
        )

        # بند الدائن: حسابات العملاء
        credit_item = JournalItem(
            account_id=ar_account.id,
            debit=0,
            credit=self.amount,
            description=f"تحصيل من العميل {self.customer.name}"
        )

        entry.items.append(debit_item)
        entry.items.append(credit_item)

        db.session.add(entry)
        db.session.flush()  # للحصول على ID

        self.journal_entry_id = entry.id
        entry.post()  # ترحيل القيد

        # تحديث رصيد الفاتورة إذا كانت مرتبطة
        if self.invoice:
            self.invoice.amount_due = max(0, (self.invoice.amount_due or self.invoice.total_amount) - self.amount)
            if self.invoice.amount_due == 0:
                self.invoice.status = 'paid'

class Vendor(db.Model):
    """الموردون"""
    __tablename__ = 'vendors'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    company = db.Column(db.String(255))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_id = db.Column(db.String(50))
    payment_terms = db.Column(db.Integer, default=30)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    bills = db.relationship('VendorBill', backref='vendor', lazy='dynamic')
    payments_made = db.relationship('PaymentMade', backref='vendor', lazy='dynamic')

    def __repr__(self):
        return f'<Vendor {self.name}>'

    def get_total_outstanding(self):
        """إجمالي المبالغ المستحقة للمورد"""
        return sum(bill.amount_due for bill in self.bills if bill.status in ['to_pay'])

class FiscalYear(db.Model):
    """السنوات المالية"""
    __tablename__ = 'fiscal_years'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    is_current = db.Column(db.Boolean, default=False)
    status = db.Column(db.Enum('open', 'closed'), default='open')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<FiscalYear {self.name}>'

class TaxRate(db.Model):
    """معدلات الضرائب"""
    __tablename__ = 'tax_rates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    rate = db.Column(db.Numeric(5, 2), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<TaxRate {self.name}: {self.rate}%>'

class VendorBill(db.Model):
    """فواتير الموردين"""
    __tablename__ = 'vendor_bills'

    id = db.Column(db.Integer, primary_key=True)
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id'), nullable=False)
    bill_number = db.Column(db.String(100), nullable=False)
    vendor_reference = db.Column(db.String(100))
    issue_date = db.Column(db.Date, nullable=False, default=date.today)
    due_date = db.Column(db.Date, nullable=False)
    subtotal = db.Column(db.Numeric(15, 2), nullable=False)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    amount_due = db.Column(db.Numeric(15, 2), nullable=False)
    status = db.Column(db.Enum('draft', 'to_pay', 'paid', 'cancelled'), default='draft')
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    items = db.relationship('VendorBillItem', backref='bill', lazy='dynamic', cascade='all, delete-orphan')
    journal_entry = db.relationship('JournalEntry', backref='vendor_bill')
    created_by = db.relationship('User', backref='vendor_bills')

    def __repr__(self):
        return f'<VendorBill {self.bill_number}: ${self.total_amount}>'

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        self.subtotal = sum(item.subtotal for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount
        self.amount_due = self.total_amount

class VendorBillItem(db.Model):
    """بنود فواتير الموردين"""
    __tablename__ = 'vendor_bill_items'

    id = db.Column(db.Integer, primary_key=True)
    bill_id = db.Column(db.Integer, db.ForeignKey('vendor_bills.id'), nullable=False)
    description = db.Column(db.Text, nullable=False)
    quantity = db.Column(db.Numeric(10, 2), default=1)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    subtotal = db.Column(db.Numeric(15, 2), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('chart_of_accounts.id'), nullable=False)

    # العلاقات
    account = db.relationship('ChartOfAccounts', backref='vendor_bill_items')

    def calculate_subtotal(self):
        """حساب المجموع الفرعي"""
        self.subtotal = self.quantity * self.unit_price

class PaymentMade(db.Model):
    """الدفعات المسددة للموردين"""
    __tablename__ = 'payments_made'

    id = db.Column(db.Integer, primary_key=True)
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id'), nullable=False)
    bill_id = db.Column(db.Integer, db.ForeignKey('vendor_bills.id'))
    payment_number = db.Column(db.String(50), unique=True, nullable=False)
    payment_date = db.Column(db.Date, nullable=False, default=date.today)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.Enum('cash', 'bank_transfer', 'check'), nullable=False)
    reference_number = db.Column(db.String(100))
    notes = db.Column(db.Text)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    bill = db.relationship('VendorBill', backref='payments_made')
    journal_entry = db.relationship('JournalEntry', backref='payment_made')
    created_by = db.relationship('User', backref='payments_made')

    def __repr__(self):
        return f'<PaymentMade {self.payment_number}: ${self.amount}>'

# Events للتحديث التلقائي
@event.listens_for(JournalItem, 'after_insert')
@event.listens_for(JournalItem, 'after_update')
@event.listens_for(JournalItem, 'after_delete')
def update_journal_entry_totals(mapper, connection, target):
    """تحديث إجماليات القيد عند تغيير البنود"""
    if target.journal_entry:
        target.journal_entry.calculate_totals()

@event.listens_for(VendorBillItem.quantity, 'set')
@event.listens_for(VendorBillItem.unit_price, 'set')
def update_subtotal(target, value, oldvalue, initiator):
    if target.quantity and target.unit_price:
        target.calculate_subtotal()

@event.listens_for(VendorBillItem, 'after_insert')
@event.listens_for(VendorBillItem, 'after_update')
@event.listens_for(VendorBillItem, 'after_delete')
def update_bill_totals(mapper, connection, target):
    if target.bill:
        target.bill.calculate_totals()
