from datetime import datetime
from app import db

class IdDocument(db.Model):
    __tablename__ = 'id_documents'
    
    id = db.Column(db.Integer, primary_key=True)
    document_type = db.Column(db.String(50), nullable=False)  # 'passport', 'id_card', etc.
    document_number = db.Column(db.String(100), nullable=False)
    issue_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)
    issuing_country = db.Column(db.String(100))
    document_file = db.Column(db.String(255))  # Path to stored document file
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Foreign key to user
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    def __repr__(self):
        return f'<IdDocument {self.document_type}: {self.document_number}>'
