import os
import sqlite3

def upgrade():
    # Crear directorio para archivos adjuntos
    os.makedirs(os.path.join('app', 'static', 'uploads', 'invoices'), exist_ok=True)

    # Conectar a la base de datos
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app', 'sparkle.db')
    print(f'Database path: {db_path}')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Verificar si las columnas ya existen en la tabla de facturas
        cursor.execute("PRAGMA table_info(invoices)")
        columns = [row[1] for row in cursor.fetchall()]

        # Agregar columnas si no existen
        if 'approval_date' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN approval_date DATETIME")

        if 'transfer_fee_type' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN transfer_fee_type VARCHAR(10) DEFAULT 'percentage'")

        if 'transfer_fee_value' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN transfer_fee_value FLOAT DEFAULT 0")

        if 'tax_type' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN tax_type VARCHAR(10) DEFAULT 'percentage'")

        if 'tax_value' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN tax_value FLOAT DEFAULT 0")

        if 'discount_type' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN discount_type VARCHAR(10) DEFAULT 'percentage'")

        if 'discount_value' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN discount_value FLOAT DEFAULT 0")

        if 'subtotal' not in columns:
            cursor.execute("ALTER TABLE invoices ADD COLUMN subtotal FLOAT DEFAULT 0")

        # Verificar si las tablas de elementos de línea y archivos adjuntos existen
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_items'")
        if not cursor.fetchone():
            # Crear tabla de elementos de línea
            cursor.execute("""
            CREATE TABLE invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                description VARCHAR(255) NOT NULL,
                quantity FLOAT DEFAULT 1,
                unit_price FLOAT NOT NULL,
                company_profit_type VARCHAR(10) DEFAULT 'percentage',
                company_profit_value FLOAT DEFAULT 0,
                invoice_id INTEGER,
                supervisor_id INTEGER,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (supervisor_id) REFERENCES users (id)
            )
            """)

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_attachments'")
        if not cursor.fetchone():
            # Crear tabla de archivos adjuntos
            cursor.execute("""
            CREATE TABLE invoice_attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(50),
                file_size INTEGER,
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                invoice_id INTEGER,
                uploaded_by_id INTEGER,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (uploaded_by_id) REFERENCES users (id)
            )
            """)

        # Obtener todas las facturas
        cursor.execute("SELECT id, invoice_number, total_amount FROM invoices")
        invoices = cursor.fetchall()

        # Crear elementos de línea para facturas existentes
        for invoice_id, invoice_number, total_amount in invoices:
            # Verificar si la factura ya tiene elementos de línea
            cursor.execute("SELECT COUNT(*) FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
            count = cursor.fetchone()[0]

            if count == 0:
                # Crear un elemento de línea con el monto total
                cursor.execute("""
                INSERT INTO invoice_items (description, quantity, unit_price, invoice_id)
                VALUES (?, 1, ?, ?)
                """, (f"Factura #{invoice_number}", total_amount, invoice_id))

        # Actualizar subtotales
        for invoice_id, _, _ in invoices:
            # Calcular subtotal sumando los elementos de línea
            cursor.execute("""
            SELECT SUM(quantity * unit_price) FROM invoice_items WHERE invoice_id = ?
            """, (invoice_id,))
            subtotal = cursor.fetchone()[0] or 0

            # Actualizar subtotal en la factura
            cursor.execute("""
            UPDATE invoices SET subtotal = ? WHERE id = ?
            """, (subtotal, invoice_id))

        # Confirmar cambios
        conn.commit()
        print("Migración completada con éxito.")

    except Exception as e:
        conn.rollback()
        print(f"Error durante la migración: {e}")

    finally:
        conn.close()

def downgrade():
    # No implementado
    pass

if __name__ == "__main__":
    upgrade()
