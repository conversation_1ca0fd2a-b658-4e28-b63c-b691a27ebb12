/* Custom styles for Sparkle Media Agency */

/* Global styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Dashboard cards */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Navbar customization */
.navbar-brand {
    font-weight: bold;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 56px 0 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    background-color: #212529;
    overflow-y: auto;
    border-left: 1px solid #dee2e6;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #fff;
    padding: 0.75rem 1rem;
    border-right: 3px solid transparent;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-right-color: #EABF54;
}

.sidebar .nav-link.active {
    color: #EABF54;
    border-right-color: #EABF54;
    background-color: rgba(255, 255, 255, 0.05);
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    color: #adb5bd;
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
    color: #EABF54;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 3.5rem;
        padding-top: 0;
        height: calc(100vh - 3.5rem);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        width: 80% !important;
        max-width: 300px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .col-md-9.ms-sm-auto {
        width: 100%;
        margin-right: 0 !important;
    }
}

/* Tables */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Forms */
.form-control:focus {
    border-color: #6c757d;
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

/* Buttons */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Notification badge */
.notification-count {
    font-size: 0.65rem;
}

/* Project status badges */
.badge {
    font-weight: 500;
}

/* Footer */
footer {
    margin-top: 2rem;
}

/* Login and Register pages */
.auth-card {
    max-width: 400px;
    margin: 2rem auto;
}

/* Profile page */
.profile-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Project details */
.project-header {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Task list */
.task-list-item {
    border-left: 4px solid #dee2e6;
    transition: border-color 0.3s ease;
}

.task-list-item:hover {
    border-left-color: #0d6efd;
}

.task-list-item.completed {
    border-left-color: #198754;
}

.task-list-item.overdue {
    border-left-color: #dc3545;
}

/* Invoice styles */
.invoice-header {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.invoice-total {
    font-size: 1.5rem;
    font-weight: bold;
}

/* Client details */
.client-header {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Department styles */
.department-card {
    height: 100%;
}

.department-card .card-body {
    display: flex;
    flex-direction: column;
}

.department-card .card-text {
    flex-grow: 1;
}

/* Employee list */
.employee-list-item {
    transition: background-color 0.3s ease;
}

.employee-list-item:hover {
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1rem;
    }

    .card h2 {
        font-size: 1.5rem;
    }
}

/* RTL specific adjustments */
[dir="rtl"] .dropdown-menu {
    text-align: right;
}

[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-1,
[dir="rtl"] .me-2,
[dir="rtl"] .me-3 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-1,
[dir="rtl"] .ms-2,
[dir="rtl"] .ms-3 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

/* Custom colors for status indicators */
.status-pending {
    color: #6c757d;
}

.status-in-progress {
    color: #0d6efd;
}

.status-completed {
    color: #198754;
}

.status-cancelled {
    color: #dc3545;
}

/* Priority indicators */
.priority-low {
    color: #0dcaf0;
}

.priority-medium {
    color: #ffc107;
}

.priority-high {
    color: #dc3545;
}

/* Announcement bar */
.announcement-bar {
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1030;
    height: 40px;
    display: flex;
    align-items: center;
}

.animated-text {
    white-space: nowrap;
    animation: marquee 25s linear infinite;
    display: inline-block;
    padding-right: 100%;
}

@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}
