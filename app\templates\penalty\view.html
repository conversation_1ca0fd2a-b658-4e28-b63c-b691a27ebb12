{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تفاصيل العقوبة</h1>
        <div>
            {% if current_user.has_role('admin') or current_user.has_role('manager') %}
            <a href="{{ url_for('penalty.edit', id=penalty.id) }}" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i>تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('penalty.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات العقوبة</h6>
                    <div>
                        {% if penalty.is_active() %}
                        <span class="badge bg-success">سارية</span>
                        {% else %}
                        <span class="badge bg-secondary">منتهية</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">الموظف:</p>
                            <p class="mb-3 fw-bold">{{ penalty.user.get_full_name() }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">نوع العقوبة:</p>
                            <p class="mb-3 fw-bold">
                                {% if penalty.penalty_type == 'verbal_warning' %}
                                لفت نظر شفوي
                                {% elif penalty.penalty_type == 'written_warning' %}
                                لفت نظر كتابي (تحذير أول)
                                {% elif penalty.penalty_type == 'written_notice' %}
                                إنذار كتابي (تحذير ثاني)
                                {% elif penalty.penalty_type == 'suspension' %}
                                إيقاف مؤقت عن العمل / خصم من الراتب
                                {% elif penalty.penalty_type == 'final_warning' %}
                                الإنذار النهائي
                                {% elif penalty.penalty_type == 'termination' %}
                                الفصل من العمل / إنهاء التعاقد
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">تاريخ البدء:</p>
                            <p class="mb-3 fw-bold">{{ penalty.start_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1 text-muted">تاريخ الانتهاء:</p>
                            <p class="mb-3 fw-bold">{{ penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else 'غير محدد (دائمة)' }}</p>
                        </div>
                    </div>

                    {% if penalty.penalty_type == 'suspension' and penalty.salary_deduction %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">قيمة الخصم من الراتب:</p>
                        <p class="mb-3 fw-bold">${{ penalty.salary_deduction }}</p>
                    </div>
                    {% endif %}

                    <div class="mb-4">
                        <p class="mb-1 text-muted">سبب العقوبة:</p>
                        <p class="mb-3">{{ penalty.reason }}</p>
                    </div>

                    {% if penalty.details %}
                    <div class="mb-4">
                        <p class="mb-1 text-muted">تفاصيل إضافية:</p>
                        <p class="mb-3">{{ penalty.details }}</p>
                    </div>
                    {% endif %}

                    <div class="mb-4">
                        <p class="mb-1 text-muted">تم الإصدار بواسطة:</p>
                        <p class="mb-3">{{ penalty.issued_by.get_full_name() }} ({{ penalty.created_at.strftime('%Y-%m-%d %H:%M') }})</p>
                    </div>

                    {% if penalty.attachments.count() > 0 %}
                    <div class="mb-4">
                        <p class="mb-2 text-muted">المرفقات:</p>
                        <div class="list-group">
                            {% for attachment in penalty.attachments %}
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-file me-2"></i>
                                    <a href="{{ url_for('static', filename=attachment.file_path) }}" target="_blank">{{ attachment.filename }}</a>
                                    <small class="text-muted d-block">
                                        تم الرفع بواسطة: {{ attachment.uploaded_by.get_full_name() }} - 
                                        {{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                </div>
                <div class="card-body">
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <a href="{{ url_for('penalty.edit', id=penalty.id) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i>تعديل العقوبة
                    </a>
                    {% endif %}
                    
                    {% if current_user.has_role('admin') %}
                    <form action="{{ url_for('penalty.delete', id=penalty.id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذه العقوبة؟');">
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash me-1"></i>حذف العقوبة
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
