{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>العملاء</h1>
    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
    <a href="{{ url_for('client.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>إضافة عميل جديد
    </a>
    {% endif %}
</div>

<!-- Search and Filter Form -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">بحث وتصفية</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('client.index') }}" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث باسم العميل أو البريد الإلكتروني أو الهاتف أو الشركة" value="{{ search_query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% if search_query %}
                    <a href="{{ url_for('client.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 عميل</option>
                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 عميل</option>
                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 عميل</option>
                </select>
            </div>
        </form>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة العملاء</h5>
    </div>
    <div class="card-body">
        {% if clients.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>عدد المشاريع</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients.items %}
                    <tr>
                        <td>{{ client.name }}</td>
                        <td>{{ client.email }}</td>
                        <td>{{ client.phone }}</td>
                        <td>{{ client.projects|length }}</td>
                        <td>{{ client.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('client.view', id=client.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                                <a href="{{ url_for('client.edit', id=client.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('client.delete', id=client.id) }}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>


                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    عرض {{ clients.items|length }} من {{ clients.total }} عميل
                    {% if search_query %}
                    <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                    {% endif %}
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        {% if clients.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('client.index', page=clients.prev_num, per_page=current_per_page, search=search_query) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% set start_page = clients.page - 2 if clients.page > 2 else 1 %}
                        {% set end_page = start_page + 4 if start_page + 4 <= clients.pages else clients.pages %}
                        {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        <li class="page-item {% if page_num == clients.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('client.index', page=page_num, per_page=current_per_page, search=search_query) }}">{{ page_num }}</a>
                        </li>
                        {% endfor %}

                        {% if clients.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('client.index', page=clients.next_num, per_page=current_per_page, search=search_query) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query %}
            <i class="fas fa-info-circle me-1"></i>
            لا توجد نتائج مطابقة لـ "{{ search_query }}".
            <a href="{{ url_for('client.index') }}" class="alert-link">عرض جميع العملاء</a>
            {% else %}
            <i class="fas fa-info-circle me-1"></i>
            لا يوجد عملاء حتى الآن.
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- No client-side search script needed anymore -->
{% endblock %}
