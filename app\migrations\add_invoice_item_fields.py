import sqlite3
import os
from app.config import SQLALCHEMY_DATABASE_URI

def run_migration():
    """
    Add new fields to invoice_items table:
    - employee_profit (calculated field, not stored)
    - status (مستلم, غير مستلم)
    - receipt_date (optional)
    """
    print("Running migration: add_invoice_item_fields.py")
    
    # Extract database path from URI
    db_path = SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')
    
    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if the invoice_items table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_items'")
    if not cursor.fetchone():
        print("invoice_items table does not exist")
        conn.close()
        return
    
    # Check if the status column already exists
    cursor.execute("PRAGMA table_info(invoice_items)")
    columns = [column[1] for column in cursor.fetchall()]
    
    # Add status column if it doesn't exist
    if 'status' not in columns:
        cursor.execute("ALTER TABLE invoice_items ADD COLUMN status VARCHAR(20) DEFAULT 'غير مستلم'")
        print("Added status column to invoice_items table")
    else:
        print("status column already exists in invoice_items table")
    
    # Add receipt_date column if it doesn't exist
    if 'receipt_date' not in columns:
        cursor.execute("ALTER TABLE invoice_items ADD COLUMN receipt_date DATETIME")
        print("Added receipt_date column to invoice_items table")
    else:
        print("receipt_date column already exists in invoice_items table")
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print("Migration completed: add_invoice_item_fields.py")

if __name__ == "__main__":
    run_migration()
