// Main JavaScript file for Sparkle Media Agency

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Fix for delete confirmation modals
    const deleteButtons = document.querySelectorAll('[data-bs-toggle="modal"][data-bs-target^="#delete"]');
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent event bubbling
                const targetModalId = this.getAttribute('data-bs-target');
                const targetModal = document.querySelector(targetModalId);
                if (targetModal) {
                    const modal = new bootstrap.Modal(targetModal);
                    modal.show();
                }
            });
        });
    }

    // Fix for modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal .btn-close, .modal .btn-secondary');
    if (modalCloseButtons) {
        modalCloseButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent event bubbling
                const modal = bootstrap.Modal.getInstance(this.closest('.modal'));
                if (modal) {
                    modal.hide();
                }
            });
        });
    }

    // Task status change
    const taskStatusSelects = document.querySelectorAll('.task-status-select');
    if (taskStatusSelects) {
        taskStatusSelects.forEach(select => {
            select.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                const form = document.getElementById(`task-status-form-${taskId}`);
                if (form) {
                    form.submit();
                }
            });
        });
    }

    // Mark notification as read
    const markReadButtons = document.querySelectorAll('.mark-notification-read');
    if (markReadButtons) {
        markReadButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const notificationId = this.getAttribute('data-notification-id');
                const url = this.getAttribute('data-url');

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const notificationItem = document.getElementById(`notification-${notificationId}`);
                        if (notificationItem) {
                            notificationItem.classList.remove('bg-light');
                            this.style.display = 'none';

                            // Update notification count
                            updateNotificationCount();
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        });
    }

    // Mark all notifications as read
    const markAllReadButton = document.getElementById('mark-all-notifications-read');
    if (markAllReadButton) {
        markAllReadButton.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.getAttribute('data-url');

            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const unreadNotifications = document.querySelectorAll('.notification-unread');
                    unreadNotifications.forEach(item => {
                        item.classList.remove('bg-light');
                        item.classList.remove('notification-unread');
                        const markReadButton = item.querySelector('.mark-notification-read');
                        if (markReadButton) {
                            markReadButton.style.display = 'none';
                        }
                    });

                    // Update notification count
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
        });
    }

    // Invoice item dynamic addition
    const addInvoiceItemButton = document.getElementById('add-invoice-item');
    if (addInvoiceItemButton) {
        addInvoiceItemButton.addEventListener('click', function() {
            const invoiceItemsContainer = document.getElementById('invoice-items-container');
            const itemCount = document.querySelectorAll('.invoice-item').length;

            const newItem = document.createElement('div');
            newItem.className = 'invoice-item card mb-3';
            newItem.innerHTML = `
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="item_description_${itemCount}" class="form-label">الوصف</label>
                            <input type="text" class="form-control" id="item_description_${itemCount}" name="item_description[]" required>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="item_quantity_${itemCount}" class="form-label">الكمية</label>
                            <input type="number" class="form-control item-quantity" id="item_quantity_${itemCount}" name="item_quantity[]" min="1" value="1" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="item_price_${itemCount}" class="form-label">السعر</label>
                            <input type="number" class="form-control item-price" id="item_price_${itemCount}" name="item_price[]" min="0" step="0.01" required>
                        </div>
                        <div class="col-md-1 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-danger remove-item">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            invoiceItemsContainer.appendChild(newItem);

            // Add event listener to the new remove button
            const removeButton = newItem.querySelector('.remove-item');
            removeButton.addEventListener('click', function() {
                newItem.remove();
                calculateInvoiceTotal();
            });

            // Add event listeners to quantity and price inputs
            const quantityInput = newItem.querySelector('.item-quantity');
            const priceInput = newItem.querySelector('.item-price');

            quantityInput.addEventListener('input', calculateInvoiceTotal);
            priceInput.addEventListener('input', calculateInvoiceTotal);
        });
    }

    // Calculate invoice total
    function calculateInvoiceTotal() {
        const quantityInputs = document.querySelectorAll('.item-quantity');
        const priceInputs = document.querySelectorAll('.item-price');
        let total = 0;

        for (let i = 0; i < quantityInputs.length; i++) {
            const quantity = parseFloat(quantityInputs[i].value) || 0;
            const price = parseFloat(priceInputs[i].value) || 0;
            total += quantity * price;
        }

        const totalElement = document.getElementById('invoice-total');
        if (totalElement) {
            totalElement.textContent = total.toFixed(2);
        }
    }

    // Initialize invoice total calculation
    calculateInvoiceTotal();

    // Project progress calculation
    const projectProgressBars = document.querySelectorAll('.project-progress');
    if (projectProgressBars) {
        projectProgressBars.forEach(progressBar => {
            const percentage = progressBar.getAttribute('data-percentage');
            progressBar.style.width = `${percentage}%`;

            if (percentage < 30) {
                progressBar.classList.add('bg-danger');
            } else if (percentage < 70) {
                progressBar.classList.add('bg-warning');
            } else {
                progressBar.classList.add('bg-success');
            }
        });
    }

    // File upload preview
    const fileInput = document.getElementById('profile_image');
    const previewContainer = document.getElementById('image-preview-container');
    const previewImage = document.getElementById('image-preview');

    if (fileInput && previewContainer && previewImage) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];

            if (file) {
                const reader = new FileReader();

                previewContainer.style.display = 'block';

                reader.addEventListener('load', function() {
                    previewImage.setAttribute('src', this.result);
                });

                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
                previewImage.setAttribute('src', '');
            }
        });
    }

    // Date range picker initialization
    const dateRangePicker = document.getElementById('date-range');
    if (dateRangePicker) {
        // This would typically use a library like daterangepicker.js
        // For now, we'll just add a placeholder
        console.log('Date range picker would be initialized here');
    }

    // Task filter
    const taskFilterSelect = document.getElementById('task-filter');
    if (taskFilterSelect) {
        taskFilterSelect.addEventListener('change', function() {
            const status = this.value;
            const taskItems = document.querySelectorAll('.task-item');

            taskItems.forEach(item => {
                if (status === 'all' || item.getAttribute('data-status') === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
});

// Function to update notification count
function updateNotificationCount() {
    const countElement = document.querySelector('.notification-count');
    if (countElement) {
        fetch('/notifications/get_unread_count')
            .then(response => response.json())
            .then(data => {
                const count = data.count;

                if (count > 0) {
                    countElement.textContent = count;
                    countElement.style.display = 'inline-block';
                } else {
                    countElement.style.display = 'none';
                }
            })
            .catch(error => console.error('Error:', error));
    }
}

// Function to properly handle modal events
function setupModals() {
    // Ensure all modals are properly initialized
    const allModals = document.querySelectorAll('.modal');
    allModals.forEach(modalElement => {
        // Remove any existing event listeners to prevent duplicates
        const newModalElement = modalElement.cloneNode(true);
        modalElement.parentNode.replaceChild(newModalElement, modalElement);

        // Initialize the modal
        const modal = new bootstrap.Modal(newModalElement);

        // Add event listener to prevent modal from showing automatically
        newModalElement.addEventListener('show.bs.modal', function(event) {
            // If the modal is being shown programmatically (not by our click handlers)
            if (!event.relatedTarget) {
                return;
            }
        });

        // Add event listener to ensure modal is fully hidden
        newModalElement.addEventListener('hidden.bs.modal', function() {
            document.body.classList.remove('modal-open');
            const modalBackdrops = document.querySelectorAll('.modal-backdrop');
            modalBackdrops.forEach(backdrop => backdrop.remove());
        });
    });
}

// Call setupModals when the page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(setupModals, 500); // Slight delay to ensure all elements are loaded
});
