import json
from flask import request
from flask_login import current_user
from app import db
from app.models.activity_log import ActivityLog

def log_activity(action, entity_type, entity_id, description, details=None):
    """
    Log an activity in the system
    
    Args:
        action (str): The action performed (create, update, delete, etc.)
        entity_type (str): The type of entity (user, project, task, etc.)
        entity_id (int): The ID of the entity
        description (str): A human-readable description of the activity
        details (dict, optional): Additional details about the activity
    """
    try:
        # Get the current user ID
        user_id = current_user.id if current_user.is_authenticated else None
        
        # Get the client IP address
        ip_address = request.remote_addr
        
        # Convert details to JSON string if provided
        details_json = json.dumps(details) if details else None
        
        # Create a new activity log entry
        log_entry = ActivityLog(
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            description=description,
            details=details_json,
            ip_address=ip_address
        )
        
        # Add and commit to the database
        db.session.add(log_entry)
        db.session.commit()
        
        return True
    except Exception as e:
        print(f"Error logging activity: {e}")
        return False
