{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>المعاملات المالية</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">المعاملات</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المعاملات المالية</h5>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                    <a href="{{ url_for('finance.add_transaction') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة معاملة جديدة
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('finance.transactions') }}" class="mb-4">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث بالوصف أو الفئة" value="{{ search_query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    {% if search_query %}
                                    <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="type" name="type" onchange="this.form.submit()">
                                    <option value="all" {% if transaction_type == 'all' or not transaction_type %}selected{% endif %}>جميع المعاملات</option>
                                    <option value="income" {% if transaction_type == 'income' %}selected{% endif %}>الإيرادات</option>
                                    <option value="expense" {% if transaction_type == 'expense' %}selected{% endif %}>المصروفات</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="category" name="category" onchange="this.form.submit()">
                                    <option value="all" {% if category == 'all' or not category %}selected{% endif %}>جميع الفئات</option>
                                    {% for cat in categories %}
                                    <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>{{ cat }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="project_id" name="project_id" onchange="this.form.submit()">
                                    <option value="" {% if not project_id %}selected{% endif %}>جميع المشاريع</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}" {% if project_id|string == project.id|string %}selected{% endif %}>{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 معاملة</option>
                                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 معاملة</option>
                                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 معاملة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">من تاريخ</span>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">إلى تاريخ</span>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if transactions.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>الفئة</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>المشروع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions.items %}
                                <tr class="transaction-item" data-type="{{ transaction.transaction_type }}">
                                    <td>{{ transaction.date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>
                                        {% if transaction.category == 'invoice_payment' %}
                                        <span class="badge bg-primary">دفع فاتورة</span>
                                        {% else %}
                                        {{ transaction.category }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if transaction.transaction_type == 'income' %}
                                        <span class="badge bg-success">إيراد</span>
                                        {% else %}
                                        <span class="badge bg-danger">مصروف</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ transaction.amount }}</td>
                                    <td>
                                        {% if transaction.project %}
                                        <a href="{{ url_for('project.view', id=transaction.project.id) }}">{{ transaction.project.name }}</a>
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                                        <div class="btn-group">
                                            <a href="{{ url_for('finance.edit_transaction', id=transaction.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('finance.delete_transaction', id=transaction.id) }}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>


                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                عرض {{ transactions.items|length }} من {{ transactions.total }} معاملة
                                {% if search_query %}
                                <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                                {% endif %}
                                {% if transaction_type and transaction_type != 'all' %}
                                <span class="text-muted">
                                    (النوع:
                                    {% if transaction_type == 'income' %}إيراد{% endif %}
                                    {% if transaction_type == 'expense' %}مصروف{% endif %}
                                    )
                                </span>
                                {% endif %}
                                {% if category and category != 'all' %}
                                <span class="text-muted">(الفئة: {{ category }})</span>
                                {% endif %}
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination">
                                    {% if transactions.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.transactions', page=transactions.prev_num, per_page=current_per_page, search=search_query, type=transaction_type, category=category, date_from=date_from, date_to=date_to, project_id=project_id) }}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% set start_page = transactions.page - 2 if transactions.page > 2 else 1 %}
                                    {% set end_page = start_page + 4 if start_page + 4 <= transactions.pages else transactions.pages %}
                                    {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                                    {% for page_num in range(start_page, end_page + 1) %}
                                    <li class="page-item {% if page_num == transactions.page %}active{% endif %}">
                                        <a class="page-link" href="{{ url_for('finance.transactions', page=page_num, per_page=current_per_page, search=search_query, type=transaction_type, category=category, date_from=date_from, date_to=date_to, project_id=project_id) }}">{{ page_num }}</a>
                                    </li>
                                    {% endfor %}

                                    {% if transactions.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.transactions', page=transactions.next_num, per_page=current_per_page, search=search_query, type=transaction_type, category=category, date_from=date_from, date_to=date_to, project_id=project_id) }}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        {% if search_query or transaction_type != 'all' or category != 'all' or date_from or date_to or project_id %}
                        لا توجد نتائج مطابقة للبحث.
                        <a href="{{ url_for('finance.transactions') }}" class="alert-link">عرض جميع المعاملات</a>
                        {% else %}
                        لا توجد معاملات مالية حاليًا.
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>



{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
{% endblock %}
