from datetime import datetime
from app import db
from app.models.user import User

# Tabla de asociación para destinatarios
mail_recipients = db.<PERSON>('mail_recipients',
    db.<PERSON>umn('mail_id', db.Integer, db.<PERSON><PERSON><PERSON>('mail.id', ondelete='CASCADE'), primary_key=True),
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>('users.id', ondelete='CASCADE'), primary_key=True),
    db.<PERSON>umn('read_at', db.DateTime, nullable=True)
)

class Mail(db.Model):
    """Modelo para los correos electrónicos internos"""
    id = db.Column(db.Integer, primary_key=True)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_draft = db.Column(db.<PERSON>, default=False)
    sender_id = db.Column(db.Integer, db.<PERSON>ey('users.id', ondelete='CASCADE'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('mail.id'), nullable=True)

    # Relaciones
    sender = db.relationship('User', foreign_keys=[sender_id], backref=db.backref('sent_mails', cascade="all, delete-orphan"))
    recipients = db.relationship('User', secondary=mail_recipients,
                                lazy='subquery', backref=db.backref('received_mails', lazy=True),
                                passive_deletes=True)
    attachments = db.relationship('MailAttachment', backref='mail', lazy=True, cascade="all, delete-orphan")
    replies = db.relationship('Mail', backref=db.backref('parent', remote_side=[id]), lazy=True)

    def __repr__(self):
        return f'<Mail {self.id}: {self.subject}>'

    def is_read_by(self, user_id):
        """Verifica si el correo ha sido leído por un usuario específico"""
        result = db.session.execute(
            db.select(mail_recipients.c.read_at)
            .where(mail_recipients.c.mail_id == self.id)
            .where(mail_recipients.c.user_id == user_id)
        ).scalar()
        return result is not None

    def mark_as_read(self, user_id):
        """Marca el correo como leído por un usuario específico"""
        if not self.is_read_by(user_id):
            db.session.execute(
                db.update(mail_recipients)
                .where(mail_recipients.c.mail_id == self.id)
                .where(mail_recipients.c.user_id == user_id)
                .values(read_at=datetime.utcnow())
            )
            db.session.commit()

    def get_all_recipients_names(self):
        """Obtiene los nombres de todos los destinatarios como una cadena"""
        return ', '.join([user.first_name + ' ' + user.last_name for user in self.recipients])

class MailAttachment(db.Model):
    """Modelo para los archivos adjuntos de los correos"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # Tamaño en bytes
    mime_type = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    mail_id = db.Column(db.Integer, db.ForeignKey('mail.id'), nullable=False)

    def __repr__(self):
        return f'<MailAttachment {self.id}: {self.filename}>'
