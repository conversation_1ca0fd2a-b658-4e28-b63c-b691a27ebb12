from datetime import datetime
from app import db

# Association table for project members
project_members = db.<PERSON>('project_members',
    db.<PERSON>umn('project_id', db.<PERSON><PERSON><PERSON>, db.<PERSON>('projects.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.<PERSON><PERSON>, db.<PERSON>('users.id'), primary_key=True)
)

# Association table for project managers
project_managers = db.Table('project_managers',
    db.<PERSON>umn('project_id', db.Integer, db.<PERSON>('projects.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>ey('users.id'), primary_key=True)
)

# Association table for project departments
project_departments = db.Table('project_departments',
    db.Column('project_id', db.Integer, db.ForeignKey('projects.id'), primary_key=True),
    db.Column('department_id', db.Integer, db.<PERSON>('departments.id'), primary_key=True)
)

class Project(db.Model):
    __tablename__ = 'projects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)
    invoice_approval_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    priority = db.Column(db.String(20), default='medium')  # low, medium, high
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    # Single department (legacy support)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    department = db.relationship('Department', foreign_keys=[department_id], backref=db.backref('legacy_projects', lazy='dynamic'))

    # Multiple departments
    departments = db.relationship('Department', secondary=project_departments,
                                 lazy='subquery', backref=db.backref('associated_projects', lazy=True))

    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    client = db.relationship('Client', backref='projects')

    # Single project manager (legacy support)
    manager_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_projects')

    # Multiple project managers
    managers = db.relationship('User', secondary=project_managers,
                              lazy='subquery', backref=db.backref('co_managed_projects', lazy=True))

    # Tasks in this project
    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')

    # Files associated with this project
    files = db.relationship('ProjectFile', backref='project', lazy='dynamic', cascade='all, delete-orphan')

    # Invoices for this project
    invoices = db.relationship('Invoice', backref='project', lazy='dynamic')

    # Messages in this project
    messages = db.relationship('ProjectMessage', backref='project', lazy='dynamic', cascade='all, delete-orphan')

    # Updates/announcements for this project
    updates = db.relationship('ProjectUpdate', backref='project', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Project {self.name}>'

    def get_completion_percentage(self):
        total_tasks = self.tasks.count()
        if total_tasks == 0:
            return 0
        completed_tasks = self.tasks.filter_by(status='completed').count()
        try:
            return int((completed_tasks / total_tasks) * 100)
        except ZeroDivisionError:
            return 0

    def is_overdue(self):
        if self.end_date and self.status != 'completed':
            return datetime.utcnow() > self.end_date
        return False

class ProjectFile(db.Model):
    __tablename__ = 'project_files'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    filepath = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    notes = db.Column(db.Text)  # Optional notes for the file
    links = db.Column(db.Text)  # Campo para almacenar enlaces (opcional)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='uploaded_files')

    def get_links_list(self):
        """Devuelve una lista de enlaces si existen"""
        if self.links:
            return [link.strip() for link in self.links.split(',') if link.strip()]
        return []

    def __repr__(self):
        return f'<ProjectFile {self.filename}>'


class ProjectUpdate(db.Model):
    __tablename__ = 'project_updates'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    is_pinned = db.Column(db.Boolean, default=False)
    links = db.Column(db.Text)  # Campo para almacenar enlaces (opcional)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='project_updates')

    def get_links_list(self):
        """Devuelve una lista de enlaces si existen"""
        if self.links:
            return [link.strip() for link in self.links.split(',') if link.strip()]
        return []

    def __repr__(self):
        return f'<ProjectUpdate {self.id}: {self.title}>'
