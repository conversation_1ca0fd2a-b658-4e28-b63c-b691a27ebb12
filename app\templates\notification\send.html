{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <select class="form-select" id="related_project_id" name="related_project_id">
                                    <option value="">-- بدون مشروع --</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}">{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <select class="form-select" id="related_task_id" name="related_task_id">
                                    <option value="">-- بدون مهمة --</option>
                                    {% for task in tasks %}
                                    <option value="{{ task.id }}">{{ task.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
