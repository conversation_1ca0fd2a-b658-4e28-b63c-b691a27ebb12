{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">إزالة عضو من المشروع</h1>
        <div>
            <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة إلى قائمة الأعضاء
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">تأكيد إزالة العضو</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيتم إزالة هذا العضو من المشروع وسيفقد الوصول إلى جميع بيانات المشروع.
                    </div>
                    
                    <h4 class="mb-3">هل أنت متأكد من رغبتك في إزالة العضو التالي من المشروع؟</h4>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{{ user.get_full_name() }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>البريد الإلكتروني:</strong> {{ user.email }}</p>
                                    <p><strong>الوظيفة:</strong> {{ user.job_title or 'غير محدد' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>القسم:</strong> {{ user.department.name if user.department else 'غير محدد' }}</p>
                                    <p><strong>الأدوار:</strong> 
                                        {% for role in user.roles %}
                                        <span class="badge bg-secondary me-1">{{ role.name }}</span>
                                        {% endfor %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form action="{{ url_for('project.remove_member', project_id=project.id, user_id=user.id) }}" method="POST">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-user-minus me-1"></i>إزالة العضو
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
