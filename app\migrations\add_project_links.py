import sqlite3
import os
from app.config import SQLALCHEMY_DATABASE_URI

def run_migration():
    """
    Add links field to project_updates and project_files tables
    """
    print("Running migration: add_project_links.py")
    
    # Extract database path from URI
    db_path = SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')
    
    # Check if the database file exists
    if not os.path.exists(db_path):
        print(f"Database file not found at {db_path}")
        return
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if the project_updates table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='project_updates'")
    if cursor.fetchone():
        # Check if the links column already exists
        cursor.execute("PRAGMA table_info(project_updates)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'links' not in columns:
            cursor.execute("ALTER TABLE project_updates ADD COLUMN links TEXT")
            print("Added links column to project_updates table")
        else:
            print("links column already exists in project_updates table")
    else:
        print("project_updates table does not exist")
    
    # Check if the project_files table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='project_files'")
    if cursor.fetchone():
        # Check if the links column already exists
        cursor.execute("PRAGMA table_info(project_files)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'links' not in columns:
            cursor.execute("ALTER TABLE project_files ADD COLUMN links TEXT")
            print("Added links column to project_files table")
        else:
            print("links column already exists in project_files table")
    else:
        print("project_files table does not exist")
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print("Migration completed: add_project_links.py")

if __name__ == "__main__":
    run_migration()
