{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">تقرير الإجازات</h1>
        <a href="{{ url_for('report.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>العودة إلى التقارير
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('report.export', report_type='leave') }}" method="POST">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="employee_id" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id" name="employee_id">
                                    <option value="all" selected>جميع الموظفين</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}">{{ employee.get_full_name() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" selected>الكل</option>
                                    <option value="pending">قيد الانتظار</option>
                                    <option value="approved">تمت الموافقة</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="export_format" class="form-label">تنسيق التصدير</label>
                                <select class="form-select" id="export_format" name="export_format">
                                    <option value="pdf" selected>PDF</option>
                                    <option value="excel">Excel</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-export me-1"></i>تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معاينة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>المدة</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in leave_requests %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ leave.user.get_full_name() }}</td>
                                    <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ (leave.end_date - leave.start_date).days + 1 }} يوم</td>
                                    <td>{{ leave.reason }}</td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                        <span class="badge bg-warning">قيد الانتظار</span>
                                        {% elif leave.status == 'approved' %}
                                        <span class="badge bg-success">تمت الموافقة</span>
                                        {% elif leave.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Initialize select2
        $('#employee_id, #status, #export_format').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dir: 'rtl'
        });
        
        // Set min/max dates
        $('#start_date').change(function() {
            $('#end_date').attr('min', $(this).val());
        });
        
        $('#end_date').change(function() {
            $('#start_date').attr('max', $(this).val());
        });
    });
</script>
{% endblock %}
