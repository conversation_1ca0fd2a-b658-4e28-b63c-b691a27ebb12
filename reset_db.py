import os
import sqlite3
from app import create_app, db
from app.models import User, Role, Department, Client, Project, Task, Transaction, Invoice, Notification, ProjectMessage

# Delete the existing database
db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'sparkle.db')
if os.path.exists(db_path):
    os.remove(db_path)
    print(f"Deleted existing database: {db_path}")

# Create the app
app = create_app()

# Create all tables
with app.app_context():
    db.create_all()
    print("Created all database tables")

    # Create default roles
    admin_role = Role(name='admin', description='Administrator')
    manager_role = Role(name='manager', description='Manager')
    employee_role = Role(name='employee', description='Employee')

    db.session.add_all([admin_role, manager_role, employee_role])
    db.session.commit()
    print("Created default roles")

    # Create default admin user
    admin = User(
        username='GolDeN',
        email='<EMAIL>',
        first_name='Admin',
        last_name='User',
        password_hash='$2b$12$rj8MnLcKBxAgL7GUHvYkNuRZEH58g8RmIaTXPRZQvWitpYpJhQCCy',  # GolDeN2252005
        is_active=True
    )
    admin.roles.append(admin_role)

    db.session.add(admin)
    db.session.commit()
    print("Created default admin user (username: GolDeN, password: GolDeN2252005)")

    print("Database setup completed successfully!")
