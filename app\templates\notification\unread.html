{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>الإشعارات غير المقروءة</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">غير المقروءة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الإشعارات غير المقروءة</h5>
                    <div>
                        <a href="{{ url_for('notification.index') }}" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-list me-1"></i>جميع الإشعارات
                        </a>
                        <form action="{{ url_for('notification.mark_all_read') }}" method="POST" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="list-group">
                        {% for notification in notifications %}
                        <div class="list-group-item list-group-item-action bg-light">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ notification.title }}</h5>
                                <small class="text-muted">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            {% if notification.link_url %}
                            <div class="mt-2">
                                <a href="{{ notification.link_url }}" class="btn btn-sm btn-outline-secondary" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>{{ notification.link_text or 'فتح الرابط' }}
                                </a>
                            </div>
                            {% endif %}
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <small class="text-muted">
                                    {% if notification.notification_type %}
                                    <span class="badge bg-info">{{ notification.notification_type }}</span>
                                    {% endif %}

                                    {% if notification.related_project %}
                                    <a href="{{ url_for('project.view', id=notification.related_project.id) }}" class="text-decoration-none">
                                        <span class="badge bg-primary">{{ notification.related_project.name }}</span>
                                    </a>
                                    {% endif %}

                                    {% if notification.related_task %}
                                    <a href="{{ url_for('project.tasks', id=notification.related_task.project_id) }}" class="text-decoration-none">
                                        <span class="badge bg-secondary">{{ notification.related_task.title }}</span>
                                    </a>
                                    {% endif %}
                                </small>
                                <div>
                                    <form action="{{ url_for('notification.mark_read', id=notification.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-check me-1"></i>تحديد كمقروء
                                        </button>
                                    </form>
                                    <form action="{{ url_for('notification.delete', id=notification.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا الإشعار؟')">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        لا توجد إشعارات غير مقروءة حاليًا.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
