from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
import datetime
import os
from werkzeug.utils import secure_filename
from app import db
from app.models.user import User, Role
from app.models.department import Department
from app.config import UPLOAD_FOLDER
from app.utils.activity_logger import log_activity
from app.utils.image_handler import get_image_url

employee_bp = Blueprint('employee', __name__, url_prefix='/employees')

@employee_bp.route('/')
@login_required
def index():
    # All users can view the employee list
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')

    # Build the query
    query = User.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (User.username.like(search_term)) |
            (User.first_name.like(search_term)) |
            (User.last_name.like(search_term)) |
            (User.email.like(search_term))
        )

    # Order by username
    query = query.order_by(User.username)

    # Paginate the results
    employees = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('employee/index.html',
                          title='Employees',
                          employees=employees,
                          search_query=search_query,
                          current_per_page=per_page)

@employee_bp.route('/view/<int:id>')
@login_required
def view(id):
    employee = User.query.get_or_404(id)

    # All users can view employee details
    # But projects and tasks are filtered based on permissions

    # Check if employee is currently on leave
    from app.models.leave import LeaveRequest
    from datetime import datetime

    active_leave = LeaveRequest.query.filter(
        LeaveRequest.user_id == employee.id,
        LeaveRequest.status == 'approved',
        LeaveRequest.start_date <= datetime.now().date(),
        LeaveRequest.end_date >= datetime.now().date()
    ).first()

    # Get pagination parameters for projects
    projects_page = request.args.get('projects_page', 1, type=int)
    projects_per_page = request.args.get('projects_per_page', 10, type=int)
    projects_status_filter = request.args.get('projects_status', 'all')

    # Get pagination parameters for tasks
    tasks_page = request.args.get('tasks_page', 1, type=int)
    tasks_per_page = request.args.get('tasks_per_page', 10, type=int)
    tasks_status_filter = request.args.get('tasks_status', 'all')
    tasks_priority_filter = request.args.get('tasks_priority', 'all')

    # Build projects query
    from app.models import Project
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers can see all projects
        projects_query = Project.query.filter(
            Project.members.contains(employee)
        )
    elif current_user.id == employee.id:
        # Employee can see their own projects
        # but exclude cancelled projects unless specifically filtered
        if projects_status_filter == 'cancelled':
            projects_query = Project.query.filter(
                Project.members.contains(employee)
            )
        else:
            projects_query = Project.query.filter(
                Project.members.contains(employee),
                Project.status != 'cancelled'
            )
    else:
        # Other employees can only see shared projects
        # but exclude cancelled projects unless specifically filtered
        if projects_status_filter == 'cancelled':
            projects_query = Project.query.filter(
                Project.members.contains(employee),
                Project.members.contains(current_user)
            )
        else:
            projects_query = Project.query.filter(
                Project.members.contains(employee),
                Project.members.contains(current_user),
                Project.status != 'cancelled'
            )

    # Apply status filter to projects
    if projects_status_filter != 'all':
        projects_query = projects_query.filter(Project.status == projects_status_filter)

    # Order projects by creation date (newest first)
    projects_query = projects_query.order_by(Project.created_at.desc())

    # Paginate projects
    projects = projects_query.paginate(page=projects_page, per_page=projects_per_page, error_out=False)

    # Build tasks query
    from app.models import Task
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers can see all tasks
        tasks_query = Task.query.filter(Task.assignee_id == employee.id)
    elif current_user.id == employee.id:
        # Employee can see their own tasks
        tasks_query = Task.query.filter(Task.assignee_id == employee.id)
    else:
        # Other employees can only see tasks from shared projects
        shared_project_ids = [p.id for p in projects_query.all()]
        if shared_project_ids:
            tasks_query = Task.query.filter(
                Task.assignee_id == employee.id,
                Task.project_id.in_(shared_project_ids)
            )
        else:
            # No shared projects, so no tasks to show
            tasks_query = Task.query.filter(Task.id < 0)  # Empty query

    # Apply status filter to tasks
    if tasks_status_filter != 'all':
        tasks_query = tasks_query.filter(Task.status == tasks_status_filter)

    # Apply priority filter to tasks
    if tasks_priority_filter != 'all':
        tasks_query = tasks_query.filter(Task.priority == tasks_priority_filter)

    # Order tasks by due date (most urgent first)
    tasks_query = tasks_query.order_by(Task.due_date.asc())

    # Paginate tasks
    tasks = tasks_query.paginate(page=tasks_page, per_page=tasks_per_page, error_out=False)

    # Calculate project statistics
    total_projects = projects_query.count()
    completed_projects = projects_query.filter(Project.status == 'completed').count()
    in_progress_projects = projects_query.filter(Project.status == 'in_progress').count()
    pending_projects = projects_query.filter(Project.status == 'pending').count()

    # Get recent projects for activity timeline
    recent_projects = projects_query.order_by(Project.created_at.desc()).limit(5).all()

    return render_template('employee/view.html',
                          title=f'Employee: {employee.username}',
                          employee=employee,
                          projects=projects,
                          total_projects=total_projects,
                          completed_projects=completed_projects,
                          in_progress_projects=in_progress_projects,
                          pending_projects=pending_projects,
                          recent_projects=recent_projects,
                          filtered_tasks=tasks,
                          active_leave=active_leave,
                          get_image_url=get_image_url,
                          # Project pagination and filtering parameters
                          projects_status_filter=projects_status_filter,
                          projects_per_page=projects_per_page,
                          # Task pagination and filtering parameters
                          tasks_status_filter=tasks_status_filter,
                          tasks_priority_filter=tasks_priority_filter,
                          tasks_per_page=tasks_per_page)

@employee_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has permission to create employees
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('You do not have permission to create employees', 'danger')
        return redirect(url_for('dashboard.index'))

    departments = Department.query.all()

    # Filter roles based on user permissions
    if current_user.has_role('admin'):
        # Admin can assign any role
        roles = Role.query.all()
    else:
        # Manager can only assign department_head and lower roles
        restricted_roles = ['admin', 'manager']
        roles = Role.query.filter(~Role.name.in_(restricted_roles)).all()

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        phone = request.form.get('phone')
        department_id = request.form.get('department_id')
        role_ids = request.form.getlist('roles')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'danger')
            return redirect(url_for('employee.create'))

        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'danger')
            return redirect(url_for('employee.create'))

        # Create new user with basic information
        user = User(
            username=username,
            email=email,
            phone=phone,
            nationality=request.form.get('nationality'),
            bank_account=request.form.get('bank_account') if current_user.has_role('admin') or current_user.has_role('manager') else None
        )

        # Set full name fields - only for admin and manager
        if current_user.has_role('admin') or current_user.has_role('manager'):
            user.full_name_ar = request.form.get('full_name_ar')
            user.full_name_en = request.form.get('full_name_en')

        # Set first and last name fields - for admin, manager, and department_head
        if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head'):
            user.first_name = first_name
            user.last_name = last_name
            user.first_name_en = request.form.get('first_name_en')
            user.last_name_en = request.form.get('last_name_en')

        # Parse birth date
        birth_date = request.form.get('birth_date')
        if birth_date:
            try:
                from datetime import datetime
                user.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
            except ValueError:
                pass
        user.set_password(password)

        # Set department if provided
        if department_id:
            user.department_id = department_id

        # Add roles with permission check
        restricted_roles = ['admin', 'manager']

        for role_id in role_ids:
            role = Role.query.get(role_id)
            if role:
                # If current user is not admin, prevent assigning restricted roles
                if not current_user.has_role('admin') and role.name in restricted_roles:
                    flash(f'ليس لديك صلاحية لتعيين دور {role.name}', 'warning')
                    continue

                user.roles.append(role)

        # Handle profile image upload
        if 'profile_image' in request.files and request.files['profile_image'].filename:
            from werkzeug.utils import secure_filename
            import os
            from app.config import UPLOAD_FOLDER
            from app.utils.image_storage import save_image_to_db, get_mime_type

            file = request.files['profile_image']

            # Save image to database
            img_data = save_image_to_db(file)
            if img_data:
                user.profile_image_data = img_data
                user.profile_image_mime = get_mime_type(img_data)

            # Also save to file system for backward compatibility
            filename = secure_filename(file.filename)
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            profile_images_dir = os.path.join(UPLOAD_FOLDER, 'profile_images')
            os.makedirs(profile_images_dir, exist_ok=True)

            # Save the file
            file_path = os.path.join(profile_images_dir, unique_filename)
            file.save(file_path)

            # Update the database with the new image path
            user.profile_image = f'uploads/profile_images/{unique_filename}'

        db.session.add(user)
        db.session.commit()

        # Log the activity
        log_activity(
            action='create',
            entity_type='user',
            entity_id=user.id,
            description=f'تم إنشاء موظف جديد: {user.username}'
        )

        flash('تم إنشاء الموظف بنجاح', 'success')
        return redirect(url_for('employee.index'))

    return render_template('employee/create.html', title='Create Employee', departments=departments, roles=roles)

@employee_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Check if user has permission to edit employees
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers can edit any employee
        pass
    elif current_user.id == id:
        # Users can edit their own profile
        pass
    else:
        # Other users cannot edit other employees
        flash('لا تملك صلاحية تعديل بيانات الموظفين الآخرين', 'danger')
        return redirect(url_for('dashboard.index'))

    employee = User.query.get_or_404(id)
    departments = Department.query.all()

    # Filter roles based on user permissions
    if current_user.has_role('admin'):
        # Admin can assign any role
        roles = Role.query.all()
    else:
        # Manager can only assign department_head and lower roles
        restricted_roles = ['admin', 'manager']
        roles = Role.query.filter(~Role.name.in_(restricted_roles)).all()

    if request.method == 'POST':
        # Update basic information based on role permissions

        # Full name fields - only admins and managers can update
        if current_user.has_role('admin') or current_user.has_role('manager'):
            employee.full_name_ar = request.form.get('full_name_ar')
            employee.full_name_en = request.form.get('full_name_en')

        # First and last name fields - admins, managers, and department_heads can update
        if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head'):
            employee.first_name = request.form.get('first_name')
            employee.last_name = request.form.get('last_name')
            employee.first_name_en = request.form.get('first_name_en')
            employee.last_name_en = request.form.get('last_name_en')

        # Phone field - admins and managers can update
        if current_user.has_role('admin') or current_user.has_role('manager'):
            employee.phone = request.form.get('phone')

            # Parse birth date
            birth_date = request.form.get('birth_date')
            if birth_date:
                try:
                    from datetime import datetime
                    employee.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()
                except ValueError:
                    employee.birth_date = None
            else:
                employee.birth_date = None

            employee.nationality = request.form.get('nationality')

        # Only admins and managers can update bank account
        if current_user.has_role('admin') or current_user.has_role('manager'):
            employee.bank_account = request.form.get('bank_account')

        # Update username if admin and changed
        if current_user.has_role('admin'):
            new_username = request.form.get('username')
            if new_username and new_username != employee.username:
                # Check if username already exists
                if User.query.filter_by(username=new_username).first():
                    flash('Username already exists', 'danger')
                    return redirect(url_for('employee.edit', id=id))
                employee.username = new_username

        # Update email if changed and not already taken
        new_email = request.form.get('email')
        if new_email != employee.email:
            if User.query.filter_by(email=new_email).first():
                flash('Email already exists', 'danger')
                return redirect(url_for('employee.edit', id=id))
            employee.email = new_email

        # Update department - only admins and managers can update department
        if current_user.has_role('admin') or current_user.has_role('manager'):
            department_id = request.form.get('department_id')
            if department_id:
                employee.department_id = department_id
            else:
                employee.department_id = None

        # Update roles (admin or manager)
        if current_user.has_role('admin') or current_user.has_role('manager'):
            # Special protection for GolDeN user
            if employee.username == 'GolDeN':
                if current_user.has_role('admin'):
                    # Ensure admin role is selected for GolDeN
                    admin_role = Role.query.filter_by(name='admin').first()
                    role_ids = request.form.getlist('roles')

                    # If admin role is not in the selected roles, add it
                    if admin_role and str(admin_role.id) not in role_ids:
                        role_ids.append(str(admin_role.id))
                        flash('تم الحفاظ على صلاحية المسؤول للمستخدم GolDeN', 'info')

                    # Clear existing roles but keep track of whether admin was there
                    had_admin = any(role.name == 'admin' for role in employee.roles)
                    employee.roles = []

                    # Add selected roles
                    for role_id in role_ids:
                        role = Role.query.get(role_id)
                        if role:
                            employee.roles.append(role)

                    # Double-check that admin role is still there
                    if had_admin and not any(role.name == 'admin' for role in employee.roles):
                        employee.roles.append(admin_role)
                else:
                    # Manager cannot edit GolDeN user's roles
                    flash('لا يمكنك تعديل صلاحيات المستخدم GolDeN', 'danger')
            else:
                # For other users
                role_ids = request.form.getlist('roles')

                # Check if user has admin or manager role before changes
                had_admin = any(role.name == 'admin' for role in employee.roles)
                had_manager = any(role.name == 'manager' for role in employee.roles)

                # Clear existing roles
                employee.roles = []

                # Add selected roles with permission check
                restricted_roles = ['admin', 'manager']

                for role_id in role_ids:
                    role = Role.query.get(role_id)
                    if role:
                        # If current user is not admin, prevent assigning restricted roles
                        if not current_user.has_role('admin') and role.name in restricted_roles:
                            flash(f'ليس لديك صلاحية لتعيين دور {role.name}', 'warning')
                            continue

                        employee.roles.append(role)

                # If user had admin role and current user is not admin, restore it
                if had_admin and not current_user.has_role('admin'):
                    admin_role = Role.query.filter_by(name='admin').first()
                    if admin_role and not any(role.name == 'admin' for role in employee.roles):
                        employee.roles.append(admin_role)
                        flash('تم الحفاظ على صلاحية المسؤول للمستخدم', 'info')

                # If user had manager role and current user is not admin, restore it
                if had_manager and not current_user.has_role('admin'):
                    manager_role = Role.query.filter_by(name='manager').first()
                    if manager_role and not any(role.name == 'manager' for role in employee.roles):
                        employee.roles.append(manager_role)
                        flash('تم الحفاظ على صلاحية المدير للمستخدم', 'info')

        # Update password if provided
        new_password = request.form.get('password')
        if new_password:
            employee.set_password(new_password)

        # Update active status - only admins and managers can update active status
        if current_user.has_role('admin') or current_user.has_role('manager'):
            is_active = 'is_active' in request.form

            # Protect GolDeN user from being deactivated
            if employee.username == 'GolDeN' and not is_active:
                flash('لا يمكن تعطيل حساب المستخدم GolDeN', 'warning')
                employee.is_active = True  # Force active status for GolDeN
            else:
                employee.is_active = is_active

        # Handle profile image upload
        if 'profile_image' in request.files and request.files['profile_image'].filename:
            file = request.files['profile_image']
            from app.utils.image_storage import save_image_to_db, get_mime_type

            # Save image to database
            img_data = save_image_to_db(file)
            if img_data:
                employee.profile_image_data = img_data
                employee.profile_image_mime = get_mime_type(img_data)

            # Also save to file system for backward compatibility
            filename = secure_filename(file.filename)
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            profile_images_dir = os.path.join(UPLOAD_FOLDER, 'profile_images')
            os.makedirs(profile_images_dir, exist_ok=True)

            file_path = os.path.join(profile_images_dir, unique_filename)
            file.save(file_path)

            # Update the database with the new image path
            employee.profile_image = f'uploads/profile_images/{unique_filename}'

        db.session.commit()

        # Log the activity
        log_activity(
            action='update',
            entity_type='user',
            entity_id=employee.id,
            description=f'تم تحديث بيانات الموظف: {employee.username}'
        )

        flash('تم تحديث بيانات الموظف بنجاح', 'success')
        return redirect(url_for('employee.view', id=id))

    return render_template('employee/edit.html', title=f'Edit Employee: {employee.username}',
                          employee=employee, departments=departments, roles=roles)

@employee_bp.route('/edit_cv/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_cv(id):
    employee = User.query.get_or_404(id)

    # Check if user has permission to edit CV
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == id):
        flash('You do not have permission to edit this CV', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        # Update CV information
        employee.cv = request.form.get('cv')

        db.session.commit()
        flash('تم تحديث السيرة الذاتية بنجاح', 'success')
        return redirect(url_for('employee.view', id=id))

    return render_template('employee/edit_cv.html', title=f'Edit CV: {employee.get_full_name()}', employee=employee)

@employee_bp.route('/add_id_document/<int:id>', methods=['GET', 'POST'])
@login_required
def add_id_document(id):
    from app.models.id_document import IdDocument

    employee = User.query.get_or_404(id)

    # Check if user has permission to add ID documents
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لإضافة وثائق هوية', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        document_type = request.form.get('document_type')
        document_number = request.form.get('document_number')
        issue_date_str = request.form.get('issue_date')
        expiry_date_str = request.form.get('expiry_date')
        issuing_country = request.form.get('issuing_country')

        # Parse dates
        from datetime import datetime
        issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d') if issue_date_str else None
        expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d') if expiry_date_str else None

        # Create new ID document
        id_document = IdDocument(
            document_type=document_type,
            document_number=document_number,
            issue_date=issue_date,
            expiry_date=expiry_date,
            issuing_country=issuing_country,
            user_id=employee.id
        )

        # Handle document file upload
        if 'document_file' in request.files and request.files['document_file'].filename:
            from werkzeug.utils import secure_filename
            import os
            from app.config import UPLOAD_FOLDER

            file = request.files['document_file']
            filename = secure_filename(file.filename)

            # Create a unique filename to avoid overwriting but preserve the original extension
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            documents_dir = os.path.join(UPLOAD_FOLDER, 'id_documents')
            os.makedirs(documents_dir, exist_ok=True)

            # Save the file
            file_path = os.path.join(documents_dir, unique_filename)
            file.save(file_path)

            # Store the original filename in the database path
            # Format: uploads/id_documents/unique_filename|original_filename
            id_document.document_file = f'uploads/id_documents/{unique_filename}|{filename}'

        db.session.add(id_document)
        db.session.commit()

        flash('تمت إضافة وثيقة الهوية بنجاح', 'success')
        return redirect(url_for('employee.view', id=id))

    return render_template('employee/add_id_document.html', title='إضافة وثيقة هوية', employee=employee)

@employee_bp.route('/view_id_document/<int:id>', methods=['GET'])
@login_required
def view_id_document(id):
    from app.models.id_document import IdDocument

    # Get the document
    document = IdDocument.query.get_or_404(id)
    employee = User.query.get_or_404(document.user_id)

    # Check if user has permission to view ID documents
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لعرض وثائق الهوية', 'danger')
        return redirect(url_for('dashboard.index'))

    return render_template('employee/view_id_document.html',
                          title='عرض وثيقة هوية',
                          document=document,
                          employee=employee)

@employee_bp.route('/download_id_document/<int:id>', methods=['GET'])
@login_required
def download_id_document(id):
    from app.models.id_document import IdDocument
    from flask import send_file
    import mimetypes
    import os
    from app.config import UPLOAD_FOLDER

    # Get the document
    document = IdDocument.query.get_or_404(id)

    # Check if user has permission to view ID documents
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتنزيل وثائق الهوية', 'danger')
        return redirect(url_for('dashboard.index'))

    # Check if document has a file
    if not document.document_file:
        flash('لا يوجد ملف مرفق لهذه الوثيقة', 'warning')
        return redirect(url_for('employee.view_id_document', id=document.id))

    # Parse the document file path to get both the storage path and original filename
    # Format: uploads/id_documents/unique_filename|original_filename
    file_info = document.document_file.split('|')

    if len(file_info) > 1:
        # New format with original filename
        storage_path = file_info[0]
        original_filename = file_info[1]
    else:
        # Old format without original filename
        storage_path = document.document_file
        original_filename = os.path.basename(storage_path)

    # Get the file path
    file_path = os.path.join(UPLOAD_FOLDER, storage_path.replace('uploads/', ''))

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف غير موجود على الخادم', 'danger')
        return redirect(url_for('employee.view_id_document', id=document.id))

    # Determine the MIME type based on file extension
    file_ext = os.path.splitext(original_filename)[1].lower()
    mime_type = mimetypes.guess_type(original_filename)[0]

    if not mime_type:
        # Default to application/octet-stream if MIME type cannot be determined
        mime_type = 'application/octet-stream'

    # Return the file as an attachment with the original filename and correct MIME type
    return send_file(file_path, as_attachment=True, download_name=original_filename, mimetype=mime_type)

@employee_bp.route('/edit_id_document/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_id_document(id):
    from app.models.id_document import IdDocument

    # Get the document
    document = IdDocument.query.get_or_404(id)
    employee = User.query.get_or_404(document.user_id)

    # Check if user has permission to edit ID documents
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتعديل وثائق الهوية', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        document.document_type = request.form.get('document_type')
        document.document_number = request.form.get('document_number')

        # Parse dates
        issue_date_str = request.form.get('issue_date')
        expiry_date_str = request.form.get('expiry_date')

        from datetime import datetime
        document.issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d') if issue_date_str else None
        document.expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d') if expiry_date_str else None

        document.issuing_country = request.form.get('issuing_country')

        # Handle document file upload
        if 'document_file' in request.files and request.files['document_file'].filename:
            from werkzeug.utils import secure_filename
            import os
            from app.config import UPLOAD_FOLDER

            file = request.files['document_file']
            filename = secure_filename(file.filename)

            # Create a unique filename to avoid overwriting but preserve the original extension
            base, ext = os.path.splitext(filename)
            import datetime as dt
            timestamp = dt.datetime.now().strftime('%Y%m%d%H%M%S')
            unique_filename = f"{base}_{timestamp}{ext}"

            # Ensure directory exists
            documents_dir = os.path.join(UPLOAD_FOLDER, 'id_documents')
            os.makedirs(documents_dir, exist_ok=True)

            # Save the file
            file_path = os.path.join(documents_dir, unique_filename)
            file.save(file_path)

            # Store the original filename in the database path
            # Format: uploads/id_documents/unique_filename|original_filename
            document.document_file = f'uploads/id_documents/{unique_filename}|{filename}'

        db.session.commit()
        flash('تم تحديث وثيقة الهوية بنجاح', 'success')
        return redirect(url_for('employee.view', id=employee.id))

    return render_template('employee/edit_id_document.html',
                          title='تعديل وثيقة هوية',
                          document=document,
                          employee=employee)

@employee_bp.route('/delete_id_document/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_id_document(id):
    from app.models.id_document import IdDocument

    # Get the document
    document = IdDocument.query.get_or_404(id)
    employee_id = document.user_id

    # Check if user has permission to delete ID documents
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لحذف وثائق الهوية', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        # Delete the file if it exists
        if document.document_file:
            import os
            from app.config import UPLOAD_FOLDER

            # Parse the document file path to get the storage path
            # Format: uploads/id_documents/unique_filename|original_filename
            file_info = document.document_file.split('|')

            if len(file_info) > 1:
                # New format with original filename
                storage_path = file_info[0]
            else:
                # Old format without original filename
                storage_path = document.document_file

            file_path = os.path.join(UPLOAD_FOLDER, storage_path.replace('uploads/', ''))
            if os.path.exists(file_path):
                os.remove(file_path)

        db.session.delete(document)
        db.session.commit()

        flash('تم حذف وثيقة الهوية بنجاح', 'success')
        return redirect(url_for('employee.view', id=employee_id))

    return render_template('employee/delete_id_document.html',
                          title='حذف وثيقة هوية',
                          document=document,
                          employee=User.query.get(employee_id))

@employee_bp.route('/view_password/<int:id>', methods=['GET', 'POST'])
@login_required
def view_password(id):
    # Only admin can view/reset passwords
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لعرض أو إعادة تعيين كلمات المرور', 'danger')
        return redirect(url_for('dashboard.index'))

    employee = User.query.get_or_404(id)
    generated_password = None

    if request.method == 'POST':
        password_type = request.form.get('password_type')
        require_change = 'require_change' in request.form

        if password_type == 'random':
            # Generate a random password
            import random
            import string
            password_length = 10
            chars = string.ascii_letters + string.digits + '!@#$%^&*'
            generated_password = ''.join(random.choice(chars) for _ in range(password_length))
        else:
            # Use custom password
            generated_password = request.form.get('custom_password')

            if not generated_password:
                flash('يرجى إدخال كلمة مرور مخصصة', 'danger')
                return redirect(url_for('employee.view_password', id=id))

        # Set the new password
        employee.set_password(generated_password)

        # TODO: If require_change is True, set a flag to require password change on next login
        # This would require adding a new field to the User model

        db.session.commit()

        # Log the activity
        log_activity(
            action='update',
            entity_type='password',
            entity_id=employee.id,
            description=f'تم إعادة تعيين كلمة المرور للمستخدم: {employee.username} بواسطة المسؤول: {current_user.username}'
        )

        flash('تم إعادة تعيين كلمة المرور بنجاح', 'success')

        # Return to the same page with the generated password
        return render_template('employee/view_password.html',
                              title='عرض/إعادة تعيين كلمة المرور',
                              employee=employee,
                              generated_password=generated_password)

    return render_template('employee/view_password.html',
                          title='عرض/إعادة تعيين كلمة المرور',
                          employee=employee,
                          generated_password=None)

@employee_bp.route('/change_password/<int:id>', methods=['GET', 'POST'])
@login_required
def change_password(id):
    employee = User.query.get_or_404(id)

    # Check if user has permission to change password
    if not (current_user.has_role('admin') or current_user.id == employee.id):
        flash('ليس لديك صلاحية لتغيير كلمة المرور لهذا الموظف', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Admin can change password without knowing current password
        if not current_user.has_role('admin'):
            if not employee.check_password(current_password):
                flash('كلمة المرور الحالية غير صحيحة', 'danger')
                return redirect(url_for('employee.change_password', id=id))

        if new_password != confirm_password:
            flash('كلمات المرور الجديدة غير متطابقة', 'danger')
            return redirect(url_for('employee.change_password', id=id))

        employee.set_password(new_password)
        db.session.commit()

        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('employee.view', id=id))

    return render_template('employee/change_password.html', title='تغيير كلمة المرور', employee=employee)

@employee_bp.route('/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete(id):
    # Only admin can delete employees
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف الموظفين', 'danger')
        return redirect(url_for('dashboard.index'))

    employee = User.query.get_or_404(id)

    if request.method == 'POST':
        # Cannot delete yourself
        if employee.id == current_user.id:
            flash('لا يمكنك حذف حسابك الخاص', 'danger')
            return redirect(url_for('employee.index'))

        # Protect GolDeN user from being deleted
        if employee.username == 'GolDeN':
            flash('لا يمكن حذف حساب المستخدم GolDeN', 'danger')
            return redirect(url_for('employee.index'))

        # Store employee info before deletion for logging
        employee_username = employee.username
        employee_id = employee.id

        db.session.delete(employee)
        db.session.commit()

        # Log the activity
        log_activity(
            action='delete',
            entity_type='user',
            entity_id=employee_id,
            description=f'تم حذف الموظف: {employee_username}'
        )

        flash('تم حذف الموظف بنجاح', 'success')
        return redirect(url_for('employee.index'))

    # Count related items
    from app.models.project import Project
    from app.models.task import Task

    # Get projects where employee is a member
    projects_count = len(employee.projects)

    # Get tasks assigned to employee
    tasks_count = Task.query.filter_by(assignee_id=id).count()

    return render_template('employee/delete.html',
                          title='حذف الموظف',
                          employee=employee,
                          current_user=current_user,
                          projects_count=projects_count,
                          tasks_count=tasks_count)
