{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>عرض وثيقة هوية</h1>
    <div>
        <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة لصفحة الموظف
        </a>
        <a href="{{ url_for('employee.edit_id_document', id=document.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('employee.delete_id_document', id=document.id) }}" class="btn btn-danger">
            <i class="fas fa-trash me-1"></i>حذف
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">بيانات الوثيقة لـ {{ employee.get_full_name() }}</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 mb-3">
                <p><strong>نوع الوثيقة:</strong> {{ document.document_type }}</p>
                <p><strong>رقم الوثيقة:</strong> {{ document.document_number }}</p>
                <p><strong>تاريخ الإصدار:</strong> {{ document.issue_date.strftime('%Y-%m-%d') if document.issue_date else 'غير محدد' }}</p>
                <p><strong>تاريخ الانتهاء:</strong> {{ document.expiry_date.strftime('%Y-%m-%d') if document.expiry_date else 'غير محدد' }}</p>
                <p><strong>بلد الإصدار:</strong> {{ document.issuing_country }}</p>
                <p><strong>تاريخ الإضافة:</strong> {{ document.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
            <div class="col-md-6">
                {% if document.document_file %}
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">ملف الوثيقة</h6>
                    </div>
                    <div class="card-body text-center">
                        {% set file_parts = document.document_file.split('|') %}
                        {% if file_parts|length > 1 %}
                            {% set original_filename = file_parts[1] %}
                            {% set file_path = file_parts[0] %}
                            {% set file_ext = original_filename.split('.')[-1].lower() %}
                        {% else %}
                            {% set file_path = document.document_file %}
                            {% set file_ext = file_path.split('.')[-1].lower() %}
                        {% endif %}

                        {% if file_ext in ['jpg', 'jpeg', 'png', 'gif'] %}
                            <img src="{{ url_for('static', filename=file_path) }}" alt="صورة الوثيقة" class="img-fluid mb-3" style="max-height: 300px;">
                        {% else %}
                            <div class="document-icon mb-3">
                                <i class="fas fa-file-alt fa-5x text-primary"></i>
                            </div>
                        {% endif %}
                        <a href="{{ url_for('employee.download_id_document', id=document.id) }}" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i>تحميل الملف
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>لا يوجد ملف مرفق لهذه الوثيقة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
