from app import db

class SystemConfig(db.Model):
    __tablename__ = 'system_config'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.String(255), nullable=False)
    description = db.Column(db.String(255))

    @classmethod
    def get_value(cls, key, default=None):
        """Get a configuration value by key"""
        config = cls.query.filter_by(key=key).first()
        if config:
            return config.value
        return default

    @classmethod
    def set_value(cls, key, value, description=None):
        """Set a configuration value"""
        config = cls.query.filter_by(key=key).first()
        if config:
            config.value = value
            if description:
                config.description = description
        else:
            config = cls(key=key, value=value, description=description)
            db.session.add(config)
        db.session.commit()
        return config

    @classmethod
    def is_maintenance_mode(cls):
        """Check if the system is in maintenance mode"""
        return cls.get_value('maintenance_mode', 'false').lower() == 'true'

    @classmethod
    def toggle_maintenance_mode(cls):
        """Toggle maintenance mode"""
        current_mode = cls.is_maintenance_mode()
        cls.set_value('maintenance_mode', 'false' if current_mode else 'true',
                     'Controls whether the system is in maintenance mode')
        return not current_mode

    @classmethod
    def is_announcement_bar_enabled(cls):
        """Check if the announcement bar is enabled"""
        return cls.get_value('announcement_bar_enabled', 'false').lower() == 'true'

    @classmethod
    def toggle_announcement_bar(cls):
        """Toggle announcement bar"""
        current_state = cls.is_announcement_bar_enabled()
        cls.set_value('announcement_bar_enabled', 'false' if current_state else 'true',
                     'Controls whether the announcement bar is enabled')
        return not current_state

    @classmethod
    def get_announcement_text(cls):
        """Get the announcement text"""
        return cls.get_value('announcement_text', 'أهلاً بكم في نظام إدارة شركة Sparkle Media Agency')

    @classmethod
    def set_announcement_text(cls, text):
        """Set the announcement text"""
        return cls.set_value('announcement_text', text, 'The text displayed in the announcement bar')

    @classmethod
    def get_announcement_bg_color(cls):
        """Get the announcement bar background color"""
        return cls.get_value('announcement_bg_color', '#EABF54')

    @classmethod
    def set_announcement_bg_color(cls, color):
        """Set the announcement bar background color"""
        return cls.set_value('announcement_bg_color', color, 'The background color of the announcement bar')

    @classmethod
    def get_announcement_text_color(cls):
        """Get the announcement bar text color"""
        return cls.get_value('announcement_text_color', '#343a40')

    @classmethod
    def set_announcement_text_color(cls, color):
        """Set the announcement bar text color"""
        return cls.set_value('announcement_text_color', color, 'The text color of the announcement bar')

    @classmethod
    def is_announcement_animated(cls):
        """Check if the announcement bar is animated"""
        return cls.get_value('announcement_animated', 'false').lower() == 'true'

    @classmethod
    def set_announcement_animated(cls, animated):
        """Set whether the announcement bar is animated"""
        return cls.set_value('announcement_animated', 'true' if animated else 'false',
                           'Controls whether the announcement text is animated (scrolling)')
