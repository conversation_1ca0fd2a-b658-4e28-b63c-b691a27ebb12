from app import db
from datetime import datetime

class ProjectMessage(db.Model):
    __tablename__ = 'project_messages'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign keys
    project_id = db.Column(db.Integer, db.<PERSON>('projects.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User', backref='project_messages')

    def __repr__(self):
        return f'<ProjectMessage {self.id}>'
