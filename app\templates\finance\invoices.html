{% extends 'base.html' %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>الفواتير</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الفواتير</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الفواتير</h5>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                    <a href="{{ url_for('finance.add_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ url_for('finance.invoices') }}" class="mb-4">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث برقم الفاتورة أو الملاحظات" value="{{ search_query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    {% if search_query %}
                                    <a href="{{ url_for('finance.invoices') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                                    <option value="all" {% if status_filter == 'all' or not status_filter %}selected{% endif %}>جميع الفواتير</option>
                                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوعة</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلقة</option>
                                    <option value="overdue" {% if status_filter == 'overdue' %}selected{% endif %}>متأخرة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="client_id" name="client_id" onchange="this.form.submit()">
                                    <option value="" {% if not client_id %}selected{% endif %}>جميع العملاء</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}" {% if client_id|string == client.id|string %}selected{% endif %}>{{ client.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="project_id" name="project_id" onchange="this.form.submit()">
                                    <option value="" {% if not project_id %}selected{% endif %}>جميع المشاريع</option>
                                    {% for project in projects %}
                                    <option value="{{ project.id }}" {% if project_id|string == project.id|string %}selected{% endif %}>{{ project.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                                    <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 فاتورة</option>
                                    <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 فاتورة</option>
                                    <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 فاتورة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">من تاريخ</span>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">إلى تاريخ</span>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ url_for('finance.invoices') }}" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if invoices.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المشروع</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>تاريخ الموافقة</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices.items %}
                                <tr class="invoice-item" data-status="{{ invoice.status }}">
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>
                                        {% if invoice.client %}
                                        <a href="{{ url_for('client.view', id=invoice.client.id) }}">{{ invoice.client.name }}</a>
                                        {% else %}
                                        غير محدد
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if invoice.project %}
                                        <a href="{{ url_for('project.view', id=invoice.project.id) }}">{{ invoice.project.name }}</a>
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>{{ invoice.issue_date.strftime('%Y-%m-%d') if invoice.issue_date else '-' }}</td>
                                    <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '-' }}</td>
                                    <td>{{ invoice.approval_date.strftime('%Y-%m-%d') if invoice.approval_date else '-' }}</td>
                                    <td>${{ invoice.amount }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                        {% elif invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                        {% elif invoice.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخر</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                                            <a href="{{ url_for('finance.edit_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('finance.delete_invoice', id=invoice.id) }}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>

                                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}


                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                عرض {{ invoices.items|length }} من {{ invoices.total }} فاتورة
                                {% if search_query %}
                                <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                                {% endif %}
                                {% if status_filter and status_filter != 'all' %}
                                <span class="text-muted">
                                    (الحالة:
                                    {% if status_filter == 'paid' %}مدفوعة{% endif %}
                                    {% if status_filter == 'pending' %}معلقة{% endif %}
                                    {% if status_filter == 'overdue' %}متأخرة{% endif %}
                                    )
                                </span>
                                {% endif %}
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination">
                                    {% if invoices.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.invoices', page=invoices.prev_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, client_id=client_id, project_id=project_id) }}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% set start_page = invoices.page - 2 if invoices.page > 2 else 1 %}
                                    {% set end_page = start_page + 4 if start_page + 4 <= invoices.pages else invoices.pages %}
                                    {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                                    {% for page_num in range(start_page, end_page + 1) %}
                                    <li class="page-item {% if page_num == invoices.page %}active{% endif %}">
                                        <a class="page-link" href="{{ url_for('finance.invoices', page=page_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, client_id=client_id, project_id=project_id) }}">{{ page_num }}</a>
                                    </li>
                                    {% endfor %}

                                    {% if invoices.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('finance.invoices', page=invoices.next_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, client_id=client_id, project_id=project_id) }}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        {% if search_query or status_filter != 'all' or date_from or date_to or client_id or project_id %}
                        لا توجد نتائج مطابقة للبحث.
                        <a href="{{ url_for('finance.invoices') }}" class="alert-link">عرض جميع الفواتير</a>
                        {% else %}
                        لا توجد فواتير حاليًا.
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>



{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
{% endblock %}
