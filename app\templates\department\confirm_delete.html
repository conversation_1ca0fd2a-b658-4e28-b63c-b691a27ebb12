{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ title }}</h1>
    <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة إلى القسم
    </a>
</div>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد الحذف</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف القسم بشكل نهائي.
        </div>
        
        <h5 class="mb-3">هل أنت متأكد من رغبتك في حذف قسم <strong>{{ department.name }}</strong>؟</h5>
        
        {% if has_members or has_projects %}
        <div class="alert alert-danger">
            <h6 class="alert-heading">لا يمكن حذف هذا القسم حاليًا للأسباب التالية:</h6>
            <ul class="mb-0">
                {% if has_members %}
                <li>القسم يحتوي على أعضاء. يرجى إعادة تعيين الأعضاء أولاً.</li>
                {% endif %}
                {% if has_projects %}
                <li>القسم يحتوي على مشاريع. يرجى إعادة تعيين المشاريع أولاً.</li>
                {% endif %}
            </ul>
        </div>
        
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>العودة
            </a>
            {% if has_members %}
            <a href="{{ url_for('department.members', id=department.id) }}" class="btn btn-primary">
                <i class="fas fa-users me-1"></i>إدارة الأعضاء
            </a>
            {% endif %}
        </div>
        {% else %}
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">معلومات القسم</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم القسم:</strong> {{ department.name }}</p>
                        <p><strong>الوصف:</strong> {{ department.description or 'لا يوجد وصف' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ department.created_at.strftime('%Y-%m-%d') }}</p>
                        <p><strong>رئيس القسم:</strong> {{ department.head.get_full_name() if department.head else 'لا يوجد' }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('department.view', id=department.id) }}" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>إلغاء
            </a>
            <form action="{{ url_for('department.delete', id=department.id) }}" method="POST">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i>تأكيد الحذف
                </button>
            </form>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
