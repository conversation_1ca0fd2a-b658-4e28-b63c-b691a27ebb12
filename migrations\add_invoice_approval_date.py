import sqlite3
import os

def run_migration():
    # Get the path to the database file
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app', 'sparkle.db')

    # Connect to the SQLite database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the column already exists
    cursor.execute("PRAGMA table_info(projects)")
    columns = [column[1] for column in cursor.fetchall()]

    if 'invoice_approval_date' not in columns:
        print("Adding invoice_approval_date column to projects table...")
        cursor.execute("ALTER TABLE projects ADD COLUMN invoice_approval_date DATETIME")
        conn.commit()
        print("Column added successfully!")
    else:
        print("Column invoice_approval_date already exists.")

    conn.close()

if __name__ == "__main__":
    run_migration()
