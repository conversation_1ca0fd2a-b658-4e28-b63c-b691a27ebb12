{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">طلبات الإجازة</h1>
        <a href="{{ url_for('leave.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>طلب إجازة جديد
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة طلبات الإجازة</h6>
        </div>
        <div class="card-body">
            <!-- Search and Filter Form -->
            <form method="GET" action="{{ url_for('leave.index') }}" class="mb-4">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" placeholder="ابحث باسم الموظف أو سبب الإجازة" value="{{ search_query }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            {% if search_query %}
                            <a href="{{ url_for('leave.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="all" {% if status_filter == 'all' or not status_filter %}selected{% endif %}>جميع الحالات</option>
                            <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                            <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>مرفوض</option>
                        </select>
                    </div>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <div class="col-md-3">
                        <select class="form-select" id="employee_id" name="employee_id" onchange="this.form.submit()">
                            <option value="" {% if not employee_id %}selected{% endif %}>جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if employee_id|string == employee.id|string %}selected{% endif %}>{{ employee.get_full_name() }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div class="col-md-{% if current_user.has_role('admin') or current_user.has_role('manager') %}3{% else %}6{% endif %}">
                        <select class="form-select" id="per_page" name="per_page" onchange="this.form.submit()">
                            <option value="25" {% if current_per_page == 25 %}selected{% endif %}>عرض 25 طلب</option>
                            <option value="50" {% if current_per_page == 50 %}selected{% endif %}>عرض 50 طلب</option>
                            <option value="100" {% if current_per_page == 100 %}selected{% endif %}>عرض 100 طلب</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">من تاريخ</span>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">إلى تاريخ</span>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('leave.index') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
            {% if leave_requests.items %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المدة (أيام)</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave_request in leave_requests.items %}
                        <tr>
                            <td>{{ leave_request.user.get_full_name() }}</td>
                            <td>{{ leave_request.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave_request.end_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ leave_request.get_duration_days() }}</td>
                            <td>
                                {% if leave_request.status == 'pending' %}
                                <span class="badge bg-warning text-dark">قيد الانتظار</span>
                                {% elif leave_request.status == 'approved' %}
                                <span class="badge bg-success">تمت الموافقة</span>
                                {% elif leave_request.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوض</span>
                                {% endif %}
                            </td>
                            <td>{{ leave_request.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <a href="{{ url_for('leave.view', id=leave_request.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>

                                {% if current_user.has_role('admin') or current_user.has_role('manager') or (current_user.id == leave_request.user_id and leave_request.status == 'pending') %}
                                <form action="{{ url_for('leave.delete', id=leave_request.id) }}" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الطلب؟');">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        عرض {{ leave_requests.items|length }} من {{ leave_requests.total }} طلب
                        {% if search_query %}
                        <span class="text-muted">(تصفية بواسطة: {{ search_query }})</span>
                        {% endif %}
                        {% if status_filter and status_filter != 'all' %}
                        <span class="text-muted">
                            (الحالة:
                            {% if status_filter == 'pending' %}قيد الانتظار{% endif %}
                            {% if status_filter == 'approved' %}تمت الموافقة{% endif %}
                            {% if status_filter == 'rejected' %}مرفوض{% endif %}
                            )
                        </span>
                        {% endif %}
                        {% if employee_id %}
                        <span class="text-muted">
                            (الموظف:
                            {% for employee in employees %}
                                {% if employee.id|string == employee_id|string %}
                                    {{ employee.get_full_name() }}
                                {% endif %}
                            {% endfor %}
                            )
                        </span>
                        {% endif %}
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            {% if leave_requests.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('leave.index', page=leave_requests.prev_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, employee_id=employee_id) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% set start_page = leave_requests.page - 2 if leave_requests.page > 2 else 1 %}
                            {% set end_page = start_page + 4 if start_page + 4 <= leave_requests.pages else leave_requests.pages %}
                            {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            <li class="page-item {% if page_num == leave_requests.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('leave.index', page=page_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, employee_id=employee_id) }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}

                            {% if leave_requests.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('leave.index', page=leave_requests.next_num, per_page=current_per_page, search=search_query, status=status_filter, date_from=date_from, date_to=date_to, employee_id=employee_id) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                {% if search_query or status_filter != 'all' or date_from or date_to or employee_id %}
                <p class="text-muted mb-0">لا توجد نتائج مطابقة للبحث</p>
                <a href="{{ url_for('leave.index') }}" class="btn btn-secondary mt-3">
                    <i class="fas fa-redo me-1"></i>عرض جميع طلبات الإجازة
                </a>
                {% else %}
                <p class="text-muted mb-0">لا توجد طلبات إجازة حالياً</p>
                <a href="{{ url_for('leave.create') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus me-1"></i>إنشاء طلب إجازة جديد
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[5, "desc"]]
        });
    });
</script>
{% endblock %}
