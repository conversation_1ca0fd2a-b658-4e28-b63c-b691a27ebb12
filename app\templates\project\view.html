{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ project.name }}</h1>
    <div>
        <a href="{{ url_for('project.index') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للمشاريع
        </a>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
        <a href="{{ url_for('project.edit', id=project.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Project Details -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">تفاصيل المشروع</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>الحالة:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.status == 'pending' %}
                        <span class="badge bg-secondary">معلق</span>
                        {% elif project.status == 'in_progress' %}
                        <span class="badge bg-primary">قيد التنفيذ</span>
                        {% elif project.status == 'completed' %}
                        <span class="badge bg-success">مكتمل</span>
                        {% elif project.status == 'cancelled' %}
                        <span class="badge bg-danger">ملغي</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>الأولوية:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.priority == 'low' %}
                        <span class="badge bg-info">منخفضة</span>
                        {% elif project.priority == 'medium' %}
                        <span class="badge bg-warning">متوسطة</span>
                        {% elif project.priority == 'high' %}
                        <span class="badge bg-danger">عالية</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>تاريخ البدء:</strong>
                    </div>
                    <div class="col-md-9">
                        {{ project.start_date.strftime('%Y-%m-%d') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>تاريخ الانتهاء:</strong>
                    </div>
                    <div class="col-md-9">
                        {{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}
                        {% if project.is_overdue() %}
                        <span class="badge bg-danger ms-2">متأخر</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>تاريخ اعتماد الفاتورة:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.invoice_approval_date %}
                        {{ project.invoice_approval_date.strftime('%Y-%m-%d') }}
                        {% else %}
                        غير محدد
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>القسم:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.department %}
                        <a href="{{ url_for('department.view', id=project.department.id) }}">{{ project.department.name }}</a>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>العميل:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.client %}
                        <a href="{{ url_for('client.view', id=project.client.id) }}">{{ project.client.name }}</a>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>مدير المشروع:</strong>
                    </div>
                    <div class="col-md-9">
                        {% if project.manager %}
                        <a href="{{ url_for('employee.view', id=project.manager.id) }}">{{ project.manager.get_full_name() }}</a>
                        {% else %}
                        غير محدد
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>التقدم:</strong>
                    </div>
                    <div class="col-md-9">
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar project-progress" role="progressbar"
                                 data-percentage="{{ project.get_completion_percentage() }}"
                                 style="width: {{ project.get_completion_percentage() }}%;"
                                 aria-valuenow="{{ project.get_completion_percentage() }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                {{ project.get_completion_percentage() }}%
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <strong>الوصف:</strong>
                    </div>
                    <div class="col-md-9">
                        {{ project.description or 'لا يوجد وصف متاح.' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Tasks -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المهام</h5>
                <div>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or
                        current_user.id == project.manager_id or
                        (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
                    <a href="{{ url_for('project.create_task', id=project.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة مهمة
                    </a>
                    {% endif %}
                    <a href="{{ url_for('project.tasks', id=project.id) }}" class="btn btn-sm btn-secondary ms-2">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Task Filters -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <form id="task-filters-form" method="GET" action="{{ url_for('project.view', id=project.id) }}" class="row g-2">
                            <div class="col-md-3">
                                <label for="tasks_status" class="form-label">الحالة</label>
                                <select class="form-select form-select-sm" id="tasks_status" name="tasks_status" onchange="this.form.submit()">
                                    <option value="all" {% if not tasks_status_filter %}selected{% endif %}>الكل</option>
                                    <option value="pending" {% if tasks_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                    <option value="in_progress" {% if tasks_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                    <option value="completed" {% if tasks_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if tasks_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tasks_priority" class="form-label">الأولوية</label>
                                <select class="form-select form-select-sm" id="tasks_priority" name="tasks_priority" onchange="this.form.submit()">
                                    <option value="all" {% if not tasks_priority_filter %}selected{% endif %}>الكل</option>
                                    <option value="high" {% if tasks_priority_filter == 'high' %}selected{% endif %}>عالية</option>
                                    <option value="medium" {% if tasks_priority_filter == 'medium' %}selected{% endif %}>متوسطة</option>
                                    <option value="low" {% if tasks_priority_filter == 'low' %}selected{% endif %}>منخفضة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tasks_assignee" class="form-label">المسؤول</label>
                                <select class="form-select form-select-sm" id="tasks_assignee" name="tasks_assignee" onchange="this.form.submit()">
                                    <option value="all" {% if not tasks_assignee_filter %}selected{% endif %}>الكل</option>
                                    {% for member in project_members %}
                                    <option value="{{ member.id }}" {% if tasks_assignee_filter|string == member.id|string %}selected{% endif %}>
                                        {{ member.get_full_name() }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tasks_per_page" class="form-label">عدد المهام في الصفحة</label>
                                <select class="form-select form-select-sm" id="tasks_per_page" name="tasks_per_page" onchange="this.form.submit()">
                                    <option value="5" {% if tasks_per_page == 5 %}selected{% endif %}>5</option>
                                    <option value="10" {% if tasks_per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if tasks_per_page == 25 %}selected{% endif %}>25</option>
                                </select>
                            </div>
                            <!-- Hidden fields to preserve other query parameters -->
                            {% if request.args.get('tasks_page') %}
                            <input type="hidden" name="tasks_page" value="{{ request.args.get('tasks_page') }}">
                            {% endif %}
                        </form>
                    </div>
                </div>

                {% if tasks.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان المهمة</th>
                                <th>المسؤول</th>
                                <th>الحالة</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الأولوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks.items %}
                            <tr>
                                <td>{{ task.title }}</td>
                                <td>{{ task.assignee.get_full_name() if task.assignee else 'غير محدد' }}</td>
                                <td>
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}
                                    {% if task.is_overdue() %}
                                    <span class="badge bg-danger ms-1">متأخر</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.priority == 'low' %}
                                    <span class="badge bg-info">منخفضة</span>
                                    {% elif task.priority == 'medium' %}
                                    <span class="badge bg-warning">متوسطة</span>
                                    {% elif task.priority == 'high' %}
                                    <span class="badge bg-danger">عالية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('project.view_task', task_id=task.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span class="text-muted">
                            عرض {{ tasks.items|length }} من {{ tasks.total }} مهمة
                        </span>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-sm">
                            {% if tasks.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('project.view', id=project.id, tasks_page=tasks.prev_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, tasks_assignee=tasks_assignee_filter) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% set start_page = tasks.page - 2 if tasks.page > 2 else 1 %}
                            {% set end_page = start_page + 4 if start_page + 4 <= tasks.pages else tasks.pages %}
                            {% set start_page = end_page - 4 if end_page - 4 >= 1 else 1 %}

                            {% for page_num in range(start_page, end_page + 1) %}
                            <li class="page-item {% if page_num == tasks.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('project.view', id=project.id, tasks_page=page_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, tasks_assignee=tasks_assignee_filter) }}">{{ page_num }}</a>
                            </li>
                            {% endfor %}

                            {% if tasks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('project.view', id=project.id, tasks_page=tasks.next_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, tasks_assignee=tasks_assignee_filter) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد مهام لهذا المشروع حاليًا.</p>
                {% endif %}
            </div>
        </div>

        <!-- Project Files -->
        <div class="card shadow">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">الملفات</h5>
                <div>
                    <a href="{{ url_for('project.upload_file', id=project.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-upload me-1"></i>رفع ملف
                    </a>
                    <a href="{{ url_for('project.files', id=project.id) }}" class="btn btn-sm btn-secondary ms-2">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if project.files.count() > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>النوع</th>
                                <th>الحجم</th>
                                <th>تاريخ الرفع</th>
                                <th>تم الرفع بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in project.files %}
                            <tr>
                                <td>{{ file.filename }}</td>
                                <td>{{ file.file_type or 'غير معروف' }}</td>
                                <td>{{ (file.file_size / 1024)|round(2) }} KB</td>
                                <td>{{ file.uploaded_at.strftime('%Y-%m-%d') }}</td>
                                <td>{{ file.uploaded_by.get_full_name() if file.uploaded_by else 'غير معروف' }}</td>
                                <td>
                                    <a href="{{ url_for('project.download_file', file_id=file.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                        current_user.id == project.manager_id or current_user.id == file.uploaded_by_id %}
                                    <a href="{{ url_for('project.delete_file', file_id=file.id) }}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد ملفات لهذا المشروع حاليًا.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Project Members -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أعضاء المشروع</h5>
                <div>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or
                        current_user.id == project.manager_id or
                        (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id) %}
                    <a href="{{ url_for('project.add_member', id=project.id) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-user-plus me-1"></i>إضافة
                    </a>
                    {% endif %}
                    <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-sm btn-secondary ms-2">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if project.members.count() > 0 %}
                <ul class="list-group list-group-flush">
                    {% for member in project.members %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            {% if member.id == project.manager_id %}
                            <i class="fas fa-crown text-warning me-1" data-bs-toggle="tooltip" title="مدير المشروع"></i>
                            {% endif %}
                            <a href="{{ url_for('employee.view', id=member.id) }}">{{ member.get_full_name() }}</a>
                            <small class="text-muted d-block">{{ member.email }}</small>
                        </div>
                        <div>
                            {% if (current_user.has_role('admin') or current_user.has_role('manager') or
                                current_user.id == project.manager_id or
                                (current_user.has_role('department_head') and current_user.managed_department.id == project.department_id)) and
                                member.id != project.manager_id %}
                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#removeMemberModal{{ member.id }}">
                                <i class="fas fa-user-minus"></i>
                            </button>

                            <!-- Remove Member Modal -->
                            <div class="modal fade" id="removeMemberModal{{ member.id }}" tabindex="-1" aria-labelledby="removeMemberModalLabel{{ member.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="removeMemberModalLabel{{ member.id }}">تأكيد الإزالة</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في إزالة <strong>{{ member.get_full_name() }}</strong> من المشروع؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('project.remove_member', project_id=project.id, user_id=member.id) }}" method="POST">
                                                <button type="submit" class="btn btn-danger">إزالة</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted mb-0">لا يوجد أعضاء في هذا المشروع حاليًا.</p>
                {% endif %}
            </div>
        </div>

        <!-- Project Invoices -->
        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">الفواتير</h5>
                {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                <a href="{{ url_for('finance.add_invoice') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>إنشاء فاتورة
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if project.invoices.count() > 0 %}
                <ul class="list-group list-group-flush">
                    {% for invoice in project.invoices %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{{ url_for('finance.view_invoice', id=invoice.id) }}">{{ invoice.invoice_number }}</a>
                            <small class="text-muted d-block">{{ invoice.issue_date.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <div>
                            <span class="badge bg-primary">${{ invoice.total_amount }}</span>
                            {% if invoice.status == 'unpaid' %}
                            <span class="badge bg-danger">غير مدفوع</span>
                            {% elif invoice.status == 'paid' %}
                            <span class="badge bg-success">مدفوع</span>
                            {% elif invoice.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغي</span>
                            {% endif %}
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted mb-0">لا يوجد فواتير لهذا المشروع حاليًا.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Project Updates -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">آخر التحديثات</h5>
                <a href="{{ url_for('project.updates', id=project.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-bullhorn me-1"></i>جميع التحديثات
                </a>
            </div>
            <div class="card-body">
                {% if project.updates.count() > 0 %}
                {% set recent_updates = project.updates.order_by(ProjectUpdate.is_pinned.desc(), ProjectUpdate.created_at.desc()).limit(3).all() %}
                {% for update in recent_updates %}
                <div class="update-item mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                    <div class="d-flex justify-content-between align-items-start">
                        <h6 class="mb-1">
                            {% if update.is_pinned %}<i class="fas fa-thumbtack text-warning me-1"></i>{% endif %}
                            {{ update.title }}
                        </h6>
                        <small class="text-muted">{{ update.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                    <div class="text-muted small mb-2">
                        <i class="fas fa-user me-1"></i>{{ update.created_by.get_full_name() }}
                    </div>
                    <div class="update-content small">
                        {{ update.content|truncate(100) }}
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted mb-0">لا يوجد تحديثات لهذا المشروع حاليًا.</p>
                {% endif %}
            </div>
        </div>

        <!-- Project Actions -->
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">إجراءات المشروع</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('project.tasks', id=project.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-tasks me-1"></i>إدارة المهام
                    </a>
                    <a href="{{ url_for('project.files', id=project.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-file me-1"></i>إدارة الملفات
                    </a>
                    <a href="{{ url_for('project.members', id=project.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-1"></i>إدارة الأعضاء
                    </a>

                    <a href="{{ url_for('chat.project_chat', project_id=project.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-comments me-1"></i>محادثة المشروع
                    </a>

                    <a href="{{ url_for('project.updates', id=project.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-bullhorn me-1"></i>التحديثات والإعلانات
                    </a>

                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                    <a href="{{ url_for('project.delete', id=project.id) }}" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-1"></i>حذف المشروع
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
