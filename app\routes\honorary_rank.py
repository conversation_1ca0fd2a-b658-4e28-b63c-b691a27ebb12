from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from sqlalchemy import text

from app import db
from app.models.honorary_rank import HonoraryRank, UserHonoraryRank
from app.models.user import User
from app.utils.notifications import send_notification

honorary_rank_bp = Blueprint('honorary_rank', __name__, url_prefix='/honorary-ranks')

@honorary_rank_bp.route('/')
@login_required
def index():
    # All users can view honorary ranks
    ranks = HonoraryRank.query.all()

    # Get users with honorary ranks
    users_with_ranks = db.session.query(User, HonoraryRank).join(
        UserHonoraryRank, User.id == db.metadata.tables['user_honorary_ranks'].c.user_id
    ).join(
        HonoraryRank, HonoraryRank.id == db.metadata.tables['user_honorary_ranks'].c.honorary_rank_id
    ).all()

    return render_template('honorary_rank/index.html', title='الرتب الشرفية',
                          ranks=ranks, users_with_ranks=users_with_ranks)

@honorary_rank_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Only admin can create honorary ranks
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لإنشاء رتب شرفية', 'danger')
        return redirect(url_for('honorary_rank.index'))

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        color = request.form.get('color', '#007bff')  # Default to bootstrap primary
        icon = request.form.get('icon', 'fa-award')  # Default icon

        # Check if rank name already exists
        if HonoraryRank.query.filter_by(name=name).first():
            flash('اسم الرتبة موجود بالفعل', 'danger')
            return redirect(url_for('honorary_rank.create'))

        # Create honorary rank
        rank = HonoraryRank(
            name=name,
            description=description,
            color=color,
            icon=icon,
            created_by_id=current_user.id
        )

        db.session.add(rank)
        db.session.commit()

        flash('تم إنشاء الرتبة الشرفية بنجاح', 'success')
        return redirect(url_for('honorary_rank.index'))

    return render_template('honorary_rank/create.html', title='إنشاء رتبة شرفية')

@honorary_rank_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Only admin can edit honorary ranks
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لتعديل الرتب الشرفية', 'danger')
        return redirect(url_for('honorary_rank.index'))

    rank = HonoraryRank.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        color = request.form.get('color')
        icon = request.form.get('icon')

        # Check if rank name already exists (excluding this rank)
        existing_rank = HonoraryRank.query.filter_by(name=name).first()
        if existing_rank and existing_rank.id != id:
            flash('اسم الرتبة موجود بالفعل', 'danger')
            return redirect(url_for('honorary_rank.edit', id=id))

        # Update rank
        rank.name = name
        rank.description = description
        rank.color = color
        rank.icon = icon
        rank.updated_at = datetime.utcnow()

        db.session.commit()

        flash('تم تحديث الرتبة الشرفية بنجاح', 'success')
        return redirect(url_for('honorary_rank.index'))

    return render_template('honorary_rank/edit.html', title='تعديل رتبة شرفية', rank=rank)

@honorary_rank_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    # Only admin can delete honorary ranks
    if not current_user.has_role('admin'):
        flash('ليس لديك صلاحية لحذف الرتب الشرفية', 'danger')
        return redirect(url_for('honorary_rank.index'))

    rank = HonoraryRank.query.get_or_404(id)

    # Delete rank
    db.session.delete(rank)
    db.session.commit()

    flash('تم حذف الرتبة الشرفية بنجاح', 'success')
    return redirect(url_for('honorary_rank.index'))

@honorary_rank_bp.route('/assign', methods=['GET', 'POST'])
@login_required
def assign():
    # Only admin and manager can assign honorary ranks
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتعيين الرتب الشرفية', 'danger')
        return redirect(url_for('honorary_rank.index'))

    # Get all users and ranks
    users = User.query.all()
    ranks = HonoraryRank.query.all()

    if request.method == 'POST':
        user_id = request.form.get('user_id')
        rank_id = request.form.get('rank_id')

        user = User.query.get(user_id)
        rank = HonoraryRank.query.get(rank_id)

        if not user or not rank:
            flash('المستخدم أو الرتبة غير موجودين', 'danger')
            return redirect(url_for('honorary_rank.assign'))

        # Check if user already has this rank
        if rank in user.honorary_ranks:
            flash('المستخدم لديه هذه الرتبة بالفعل', 'warning')
            return redirect(url_for('honorary_rank.assign'))

        # Instead of using the relationship and then updating the association table,
        # we'll directly insert into the association table with all the values we need

        # First check if the association already exists
        existing = db.session.execute(
            text("SELECT 1 FROM user_honorary_ranks WHERE user_id = :user_id AND honorary_rank_id = :rank_id"),
            {"user_id": user.id, "rank_id": rank.id}
        ).fetchone()

        if existing:
            # If it exists, just update the assigned_by_id
            db.session.execute(
                text("UPDATE user_honorary_ranks SET assigned_by_id = :assigned_by_id WHERE user_id = :user_id AND honorary_rank_id = :rank_id"),
                {"assigned_by_id": current_user.id, "user_id": user.id, "rank_id": rank.id}
            )
        else:
            # If it doesn't exist, insert a new row
            db.session.execute(
                text("INSERT INTO user_honorary_ranks (user_id, honorary_rank_id, assigned_at, assigned_by_id) VALUES (:user_id, :rank_id, :assigned_at, :assigned_by_id)"),
                {
                    "user_id": user.id,
                    "rank_id": rank.id,
                    "assigned_at": datetime.utcnow(),
                    "assigned_by_id": current_user.id
                }
            )

        db.session.commit()

        # Send notification to user
        send_notification(
            user_id=user.id,
            title='تم منحك رتبة شرفية جديدة',
            message=f'تهانينا! تم منحك رتبة "{rank.name}"',
            notification_type='honorary_rank'
        )

        flash(f'تم تعيين الرتبة "{rank.name}" للمستخدم "{user.get_full_name()}" بنجاح', 'success')
        return redirect(url_for('honorary_rank.index'))

    return render_template('honorary_rank/assign.html', title='تعيين رتبة شرفية',
                          users=users, ranks=ranks)

@honorary_rank_bp.route('/revoke/<int:user_id>/<int:rank_id>', methods=['POST'])
@login_required
def revoke(user_id, rank_id):
    # Only admin and manager can revoke honorary ranks
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لسحب الرتب الشرفية', 'danger')
        return redirect(url_for('honorary_rank.index'))

    user = User.query.get_or_404(user_id)
    rank = HonoraryRank.query.get_or_404(rank_id)

    # Check if user has this rank
    if rank not in user.honorary_ranks:
        flash('المستخدم ليس لديه هذه الرتبة', 'warning')
        return redirect(url_for('honorary_rank.index'))

    # Remove rank from user
    user.honorary_ranks.remove(rank)
    db.session.commit()

    # Send notification to user
    send_notification(
        user_id=user.id,
        title='تم سحب رتبة شرفية',
        message=f'تم سحب رتبة "{rank.name}" منك',
        notification_type='honorary_rank'
    )

    flash(f'تم سحب الرتبة "{rank.name}" من المستخدم "{user.get_full_name()}" بنجاح', 'success')
    return redirect(url_for('honorary_rank.index'))
