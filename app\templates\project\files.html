{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>ملفات مشروع {{ project.name }}</h1>
    <div>
        <a href="{{ url_for('project.upload_file', id=project.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-upload me-1"></i>رفع ملف
        </a>
        <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للمشروع
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">قائمة الملفات</h5>
    </div>
    <div class="card-body">
        {% if project.files.count() > 0 %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم الملف</th>
                        <th>النوع</th>
                        <th>الحجم</th>
                        <th>ملاحظات</th>
                        <th>الروابط</th>
                        <th>تاريخ الرفع</th>
                        <th>تم الرفع بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for file in project.files %}
                    <tr>
                        <td>{{ file.filename }}</td>
                        <td>{{ file.file_type or 'غير معروف' }}</td>
                        <td>{{ (file.file_size / 1024)|round(2) }} KB</td>
                        <td>
                            {% if file.notes %}
                            <span data-bs-toggle="tooltip" title="{{ file.notes }}">
                                {{ file.notes|truncate(30, true) }}
                            </span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if file.links %}
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="dropdownLinks{{ file.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-link me-1"></i>الروابط ({{ file.get_links_list()|length }})
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownLinks{{ file.id }}">
                                    {% for link in file.get_links_list() %}
                                    <li><a class="dropdown-item" href="{{ link }}" target="_blank" rel="noopener noreferrer">{{ link|truncate(40, true) }} <i class="fas fa-external-link-alt ms-1"></i></a></li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ file.uploaded_at.strftime('%Y-%m-%d') }}</td>
                        <td>{{ file.uploaded_by.get_full_name() if file.uploaded_by else 'غير معروف' }}</td>
                        <td>
                            <div class="btn-group">
                                {% if file.filepath %}
                                <a href="{{ url_for('project.download_file', file_id=file.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="تنزيل">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% else %}
                                <button class="btn btn-sm btn-secondary" disabled data-bs-toggle="tooltip" title="لا يوجد ملف للتنزيل">
                                    <i class="fas fa-download"></i>
                                </button>
                                {% endif %}
                                {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                    current_user.id == project.manager_id or current_user.id == file.uploaded_by_id %}
                                <a href="{{ url_for('project.delete_file', file_id=file.id) }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا يوجد ملفات لهذا المشروع حاليًا.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
