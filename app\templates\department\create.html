{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة قسم جديد</h1>
    <a href="{{ url_for('department.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>العودة للأقسام
    </a>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">بيانات القسم الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('department.create') }}">
            <div class="mb-3">
                <label for="name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" required>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف القسم</label>
                <textarea class="form-control" id="description" name="description" rows="4"></textarea>
            </div>
            
            <div class="mb-3">
                <label for="head_id" class="form-label">رئيس القسم</label>
                <select class="form-select" id="head_id" name="head_id">
                    <option value="">-- اختر رئيس القسم --</option>
                    {% for head in potential_heads %}
                    <option value="{{ head.id }}">{{ head.get_full_name() }}</option>
                    {% endfor %}
                </select>
                <small class="text-muted">يمكنك تعيين رئيس القسم لاحقًا</small>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('department.index') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة قسم</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
